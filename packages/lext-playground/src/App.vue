<template>
    <h1>Lext Playground</h1>
    <PlaygroundEditor />
</template>

<script setup lang="ts">
import PlaygroundEditor from './components/PlaygroundEditor.vue'
</script>

<style scoped>
.logo {
    height: 6em;
    padding: 1.5em;
    will-change: filter;
    transition: filter 300ms;
}
.logo:hover {
    filter: drop-shadow(0 0 2em #646cffaa);
}
.logo.vue:hover {
    filter: drop-shadow(0 0 2em #42b883aa);
}
</style>
