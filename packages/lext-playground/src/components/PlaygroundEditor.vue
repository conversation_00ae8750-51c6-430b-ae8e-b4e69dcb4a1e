<template>
    <div class="gradient-border">
        <Lext :value="lext">
            <div class="lext-editor">
                <div class="lext-editor-toolbar">
                    <button @click="formatParagraph">
                        正文
                    </button>
                    <button @click="formatHeading('h3')">
                        三级标题
                    </button>
                    <button @click="formatHeading('h4')">
                        四级标题
                    </button>
                    <button @click="formatQuote">
                        注释
                    </button>
                    <button @click="formatBulletBlockFormat">
                        无序列表
                    </button>
                </div>
                <div class="lext-editor-toolbar">
                    <button v-for="font in textStyles" :key="font.value" @click="() => triggerTextStyle(font.value)">
                        {{ font.label }}
                    </button>
                    <button v-for="font in elementStyles" :key="font.value" @click="() => updateElementStyle(font.value)">
                        {{ font.label }}
                    </button>
                    <button @click="updateColor(colorList[Math.floor(Math.random() * colorList.length)])">
                        随机颜色
                    </button>
                    <input
                        ref="fileInputRef"
                        type="file"
                        style="display: none"
                        accept="image/*"
                        @change="
                            (e) =>
                                insertImage(e, () => {
                                    if (fileInputRef) fileInputRef.value = '';
                                })
                        "
                    >
                    <button :loading="imageLoading" @click="fileInputRef?.click">
                        插入图片
                    </button>
                </div>
                <div class="lext-editor-divider" />
                <div class="lext-editor-container">
                    <div
                        :id="name"
                        ref="rootRef"
                        class="lext-editor-area"
                        :contenteditable="editable"
                    />
                    <div v-if="placeholderDisplay" class="lext-editor-placeholder">
                        {{ placeholder }}
                    </div>
                </div>
                <div class="lext-editor-divider" />
                <div class="lext-editor-test-info">
                    <div class="lext-editor-test-button">
                        <button @click="exportHTML">
                            HTML 导入
                        </button>
                        <button @click="exportHTML">
                            HTML 导出（控制台）
                        </button>
                    </div>
                    <div>
                        <div>字符数：{{ charactersLen }} / 字数限制：{{ limit }}</div>
                        <div>是否应该显示占位文字：{{ placeholderDisplay }}</div>
                        <!-- <div>当前选区字体样式：{{ currentFontStyle }}</div> -->
                        <div>当前选区文本样式：{{ textStyleList }}</div>
                        <div>当前选区块元素样式：{{ currentValue }}</div>
                        <div>当前文本色笔颜色：{{ currentColor }}</div>
                        <div>当前选区块类型：{{ blockType }}</div>
                    </div>
                </div>
            </div>
            <LextDecorators />
        </Lext>
    </div>
</template>

<script setup lang="ts">
import {
    BlockFormatPlugin,
    CharacterCountPlugin,
    ColorPlugin,
    EditableRootPlugin,
    ElementStylePlugin,
    ExtendedTextNode,
    HeadingNode,
    HistoryPlugin,
    ImagePlugin,
    Lext,
    ListItemNode,
    ListNode,
    OverflowNode,
    QuoteNode,
    ReplaceTextNodeWithExtendedTextNode,
    RichTextPlugin,
    TextStylePlugin,
    useBlockFormat,
    useCharacterCount,
    useColor,
    useEditableRoot,
    useElementStyle,
    useImage,
    useLextProvider,
    useRichText,
    useTextStyle,
} from '@crm/lext-core'

import { ImageNode } from './ImageNode'

const name = 'SimpleEditor'
const limit = 10000
const defaultValue = 'Hello, World!'
const placeholder = 'Type something...'
const rootRef = ref<HTMLElement>()
const fileInputRef = ref<HTMLInputElement>()

const textStyles = [
    {
        value: 'bold',
        label: '粗体',
    },
    {
        value: 'italic',
        label: '斜体',
    },
    {
        value: 'underline',
        label: '下划线',
    },
] as const
const elementStyles = [
    {
        value: 'left',
        label: '居左',
    },
    {
        value: 'center',
        label: '居中',
    },
    {
        value: 'right',
        label: '居右',
    },
] as const
const colorList = ['#C4C4C4', '#000000', '#FF0000', '#FF7800', '#FFD900', '#A3E043', '#38D9F0', '#4DA8EE', '#956EE7']

const lext = useLextProvider({
    name,
    defaultValue,
    nodes: [HeadingNode, QuoteNode, ListItemNode, ListNode, ImageNode, ExtendedTextNode, ReplaceTextNodeWithExtendedTextNode, OverflowNode],
    plugins: [
        HistoryPlugin(),
        RichTextPlugin({ plainPaste: true }),
        EditableRootPlugin({ rootRef }),
        CharacterCountPlugin({
            limit,
            forbiddenOverLimitInput: true,
        }),
        TextStylePlugin({ list: textStyles.map(i => i.value) }),
        ColorPlugin({ default: '#000' }),
        ElementStylePlugin(),
        // 这个用的是基础的 ImageNode，可以自定义 ImageNode 类来实现更多功能
        ImagePlugin({ ImageNode }),
        BlockFormatPlugin(),
    ],
})

async function exportHTML() {
    console.log(await lext.getEditorValue())
}

// 建议实际使用的时候，将特定的功能封装成组件，并使用自动依赖注入
const LextDecorators = useRichText(lext)
const { charactersLen, placeholderDisplay } = useCharacterCount(lext)
const { editable } = useEditableRoot(lext)
const { textStyleList, triggerTextStyle } = useTextStyle(lext)
const { currentColor, updateColor } = useColor(lext)
const { currentValue, updateElementStyle } = useElementStyle(lext)
const { insertImage, imageLoading } = useImage(lext)
const { blockType, formatBulletBlockFormat, formatParagraph, formatQuote, formatHeading } = useBlockFormat(lext)

// onMounted(() => {
//   lext.setEditorValue(
//     '<img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/416cecf0a3fb8cbd3nVz3di7/3c459505-7784-4eb5-b739-94639f6a1b38" width="417" height="200.64347826086956" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:332,&quot;defaultWidth&quot;:871,&quot;heightLimit&quot;:999,&quot;widthLimit&quot;:690}">'
//   );
// });
</script>

<style>
.lext-editor {
    display: flex;
    flex-direction: column;
    gap: 12px;
    width: 800px;
    padding: 12px;
    position: relative;
    z-index: 1;
    background: white;
    border-radius: 15px;
}
.gradient-border {
    position: relative;
    padding: 2px;
    border-radius: 15px;
    text-align: center;
}
.gradient-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;
    border-radius: 17px;
    background: linear-gradient(
        270deg,
        #ff7e5f,
        #feb47b,
        #00c6ff,
        #0072ff,
        #833ab4,
        #fd1d1d,
        #fcb045,
        #00c9ff,
        #92fe9d
    );
    background-size: 1800% 1800%;
    animation: borderAnimation 60s ease infinite; /* 动画效果 */
}

@keyframes borderAnimation {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}
.lext-editor-toolbar {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}
.lext-editor-container {
    position: relative;
    height: 550px;
}
.lext-editor-area {
    outline: none;
    overflow: scroll;
    height: 100%;
}
.lext-editor-placeholder {
    position: absolute;
    top: 0;
    color: #ccc;
    pointer-events: none;
    z-index: -1;
}
.lext-editor-test-info {
    color: #666;
    text-align: left;
    font-size: 12px;
    display: flex;
    flex-direction: column;
    gap: 8px;
}
.lext-editor-test-button {
    display: flex;
    gap: 8px;
}
.lext-editor-divider {
    border-bottom: 1px solid #ccc;
}
.lext-editor-divider-dashed {
    border-bottom: 1px dashed #ddd;
}
button {
    padding: 4px 8px;
    border: 1px solid #ccc;
    border: none;
    border-radius: 4px;
    background-color: #f5f5f5;
    cursor: pointer;
}
button:active {
    background-color: #e5e5e5;
}
button:hover {
    background-color: #e5e5e5;
}
</style>
