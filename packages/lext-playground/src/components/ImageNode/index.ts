

import type { ImageComponentProps, ImageConfig, SerializedImageNode } from './type'
import { DecoratorBlockNode, Lexical } from '@crm/lext-core'

import ImageComponent from './ImageComponent.vue'

function convertImageElement(domNode: HTMLElement): null | Lexical.DOMConversionOutput {
    const imgSrc = domNode.getAttribute('src')
    const config = JSON.parse(domNode.getAttribute('data-lexical-img-config') || '{}')
    const width = Number(domNode.getAttribute('width') || 0) || undefined
    const height = Number(domNode.getAttribute('height') || 0) || undefined

    if (imgSrc && config) {
        const node = new ImageNode({ src: imgSrc, config, width, height })

        return { node }
    }
    return null
}

export class ImageNode extends DecoratorBlockNode {
    #src: string
    #config: Required<ImageConfig>
    #width: number
    #height: number

    // Getters
    get src(): string {
        return this.#src
    }

    set src(value: string) {
        this.#src = value
    }

    get config(): Required<ImageConfig> {
        return this.#config
    }

    set config(value: Required<ImageConfig>) {
        this.#config = value
    }

    get width(): number {
        return this.#width
    }

    set width(value: number) {
        this.#width = value
    }

    get height(): number {
        return this.#height
    }

    set height(value: number) {
        this.#height = value
    }

    constructor(props: ImageComponentProps, format?: Lexical.ElementFormatType, key?: Lexical.NodeKey) {
        super(format, key)
        this.#src = props.src
        this.#config = {
            defaultHeight: props.config.defaultHeight,
            defaultWidth: props.config.defaultWidth,
            heightLimit: props.config.heightLimit || 2000,
            widthLimit: props.config.widthLimit || 2000,
        }

        this.#height = props.height || Math.min(this.#config.defaultHeight, this.#config.heightLimit)
        this.#width = props.width || Math.min(this.#config.defaultWidth, this.#config.widthLimit)
    }

    static getType(): string {
        return 'img'
    }

    static clone(node: ImageNode): ImageNode {
        return new ImageNode({ src: node.#src, config: node.#config }, node.__format, node.__key)
    }

    static importJSON(serializedNode: SerializedImageNode): ImageNode {
        const node = $createImageNode(serializedNode)
        node.setFormat(serializedNode.format)
        return node
    }

    static importDOM(): Lexical.DOMConversionMap | null {
        return {
            img: (domNode: HTMLElement) => {
                if (!domNode.hasAttribute('data-lexical-img')) {
                    return null
                }

                return {
                    conversion: convertImageElement,
                    priority: 1,
                }
            },
        }
    }

    getTextContent(_includeInert?: boolean | undefined, _includeDirectionless?: false | undefined): string {
        // return this.#src;
        return ''
    }

    onChange(state: { src: string, height: number, width: number }): void {
        this.#src = state.src
        this.#height = state.height
        this.#width = state.width
    }

    exportJSON(): SerializedImageNode {
        return {
            ...super.exportJSON(),
            type: 'img',
            src: this.#src,
            config: this.#config,
            width: this.#width,
            height: this.#height,
            format: this.__format,
        }
    }

    createDOM(): HTMLElement {
        return document.createElement('div')
    }

    exportDOM(): Lexical.DOMExportOutput {
        const element = document.createElement('img')
        element.setAttribute('src', this.#src)
        element.setAttribute('width', String(this.#width || ''))
        element.setAttribute('height', String(this.#height || ''))
        element.setAttribute('format', String(this.__format || ''))
        element.setAttribute('data-lexical-img', String(''))
        element.setAttribute('data-lexical-img-config', JSON.stringify(this.#config))

        return { element }
    }

    updateDOM(): false {
        return false
    }

    decorate(_editor: Lexical.LexicalEditor, config: Lexical.EditorConfig): Component {
        return h(ImageComponent, { nodeState: this })
    }
}

export function $createImageNode(props: ImageComponentProps): ImageNode {
    return Lexical.$applyNodeReplacement(new ImageNode(props))
}

export function $isImageNode(node: ImageNode | Lexical.LexicalNode | null | undefined): node is ImageNode {
    return node instanceof ImageNode
}
