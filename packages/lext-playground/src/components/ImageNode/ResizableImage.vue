<template>
    <div
        ref="containerRef"
        :data-resize="isResizing ? '' : undefined"
        class="resizable-image-container"
        :class="{ editing: editable && selected, selected }"
    >
        <img
            draggable="false"
            :src="src"
            :style="{ width: `${width}px`, height: `${height}px` }"
            alt="图片"
        >
        <template v-if="editable && selected">
            <div
                v-for="corner in corners"
                :key="corner"
                class="resize-handle"
                :class="[corner]"
                @mousedown="startResize($event, corner)"
            />
        </template>
        <div
            v-if="selected"
            ref="toolbarRef"
            :class="`toolbar ${toolbarPosition}`"
            :style="{ top }"
        >
            <b-input-number
                class="number-input"
                :modelValue="width"
                size="xsmall"
                hideButton
                :max="config.maxWidth"
                :min="config.minWidth"
                @update:modelValue="(newWidth: number) => countRealImageSize(newWidth, height, 'width')"
                @keydown.enter.prevent
                @keydown.stop
                @blur="handleInputBlur"
            >
                <template #prefix>
                    宽：
                </template>
            </b-input-number>
            <div ref="lockButtonRef" class="lock-button" @click.stop="handleLock">
                <Icon v-if="isLocked" name="link-connect" />
                <template v-else>
                    <Icon name="link-break" :color="isHover ? `rgb(31, 31, 31)` : `rgb(196, 196, 196)`" />
                </template>
            </div>
            <b-input-number
                class="number-input"
                :modelValue="height"
                size="xsmall"
                hideButton
                :max="config.maxHeight"
                :min="config.minHeight"
                @update:modelValue="(newHeight: number) => countRealImageSize(width, newHeight, 'height')"
                @keydown.enter.prevent
                @keydown.stop
                @blur="handleInputBlur"
            >
                <template #prefix>
                    高：
                </template>
            </b-input-number>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useHover } from '@crm/lext-core'
import { useElementDistance } from '@crm/vueuse-pro'

import Icon from './assets/icon.vue'

const width = defineModel<number>('width', { required: true })

const height = defineModel<number>('height', { required: true })

const isResizing = defineModel<boolean>('isResizing', { required: true })

const { config, src, editable } = defineProps<Props>()

const emit = defineEmits<{
    resize: [width: number, height: number]
}>()

interface Props {
    src: string
    editable: boolean
    nodeKey: string
    selected: boolean
    config: {
        minWidth: number
        minHeight: number
        maxWidth: number
        maxHeight: number
    }
}
const isLocked = ref(true)

const editorContainerRef = ref<HTMLDivElement>()
const containerRef = ref<HTMLDivElement>()
const toolbarRef = ref<HTMLDivElement>()

const imageWithEditorDistances = useElementDistance(editorContainerRef, containerRef)

const toolbarPosition = computed(() => {
    const relativeContainerPosition = imageWithEditorDistances.value.left < 519 ? 'display' : 'hidden'

    if (relativeContainerPosition !== 'display') {
        return 'toolbar-right'
    } else {
        return 'toolbar-left'
    }
})

const top = computed(() => {
    if (imageWithEditorDistances.value.top <= 2) {
        return `${height.value + 4}px`
    }
    return imageWithEditorDistances.value.top < 40 ? `${-imageWithEditorDistances.value.top}px` : '-40px'
})

onMounted(() => {
    editorContainerRef.value = document.querySelector('div[data-lexical-editor]') as HTMLDivElement
})

const lockButtonRef = ref<HTMLDivElement>()
const isHover = useHover(lockButtonRef)

const corners = ['top-left', 'top-right', 'bottom-left', 'bottom-right']

function countRealImageSize(w?: number, h?: number, toggle: 'width' | 'height' | 'all' = 'all') {
    console.log(w, h)

    let newWidth = w || width.value
    let newHeight = h || height.value
    const aspectRatio = width.value / height.value

    const clamp = (value: number, min: number, max: number) => Math.max(min, Math.min(max, value))

    if (isLocked.value) {
        if (toggle === 'width') {
            newWidth = clamp(newWidth, config.minWidth, config.maxWidth)
            newHeight = newWidth / aspectRatio
            newHeight = clamp(newHeight, config.minHeight, config.maxHeight)
            newWidth = newHeight * aspectRatio
        } else if (toggle === 'height') {
            newHeight = clamp(newHeight, config.minHeight, config.maxHeight)
            newWidth = newHeight * aspectRatio
            newWidth = clamp(newWidth, config.minWidth, config.maxWidth)
            newHeight = newWidth / aspectRatio
        } else {
            newWidth = clamp(newWidth, config.minWidth, config.maxWidth)
            newHeight = newWidth / aspectRatio
            if (newHeight > config.maxHeight) {
                newHeight = clamp(newHeight, config.minHeight, config.maxHeight)
                newWidth = newHeight * aspectRatio
            }
        }
    } else {
        newWidth = clamp(newWidth, config.minWidth, config.maxWidth)
        newHeight = clamp(newHeight, config.minHeight, config.maxHeight)
    }

    width.value = newWidth
    height.value = newHeight
}

function startResize(event: MouseEvent, corner: string) {
    event.stopPropagation()
    event.preventDefault()
    isResizing.value = true

    // 设置鼠标样式
    document.body.style.cursor = getCursorForCorner(corner)

    const startX = event.clientX
    const startY = event.clientY
    const startWidth = width.value
    const startHeight = height.value

    const onMouseMove = (moveEvent: MouseEvent) => {
        let newWidth = startWidth
        let newHeight = startHeight

        if (corner.includes('right')) {
            newWidth = startWidth + (moveEvent.clientX - startX)
        }
        if (corner.includes('left')) {
            newWidth = startWidth - (moveEvent.clientX - startX)
        }
        if (corner.includes('bottom')) {
            newHeight = startHeight + (moveEvent.clientY - startY)
        }
        if (corner.includes('top')) {
            newHeight = startHeight - (moveEvent.clientY - startY)
        }

        countRealImageSize(newWidth, newHeight, 'all')
    }

    const onMouseUp = (event: MouseEvent) => {
        event.preventDefault()
        event.stopPropagation()

        // 移除鼠标样式
        document.body.style.cursor = ''

        document.removeEventListener('mousemove', onMouseMove)
        document.removeEventListener('mouseup', onMouseUp)
        isResizing.value = false
    }

    document.addEventListener('mousemove', onMouseMove)
    document.addEventListener('mouseup', onMouseUp)
}

// 根据角点获取对应的鼠标样式
function getCursorForCorner(corner: string) {
    switch (corner) {
        case 'top-left':
        case 'bottom-right':
            return 'nwse-resize'
        case 'top-right':
        case 'bottom-left':
            return 'nesw-resize'
        default:
            return ''
    }
}

function handleLock(e: MouseEvent) {
    e.preventDefault()
    isLocked.value = !isLocked.value
}

function handleInputBlur() {
    countRealImageSize()
    emit('resize', width.value, height.value)
}
</script>

<style scoped>
.lock-button {
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border-radius: 4px;
    width: 20px;
    height: 20px;
    color: #0a6cff;
}
:deep(.number-input) {
    width: 70px;
    background: #f2f2f2;
    border-radius: 2px;
    height: 24px;
    border: none;
    box-shadow: none;
    .b-input-prefix {
        padding: 0;
    }
}
.toolbar {
    user-select: none;
    position: absolute;
    top: -40px;
    padding: 5px;
    z-index: 1;
    width: 180px;
    display: flex;
    align-items: center;
    gap: 4px;
    background: #fff;
    box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #ececec;
    border-radius: 6px;
}
.toolbar-right {
    right: 0;
}
.toolbar-left {
    left: 0;
}
.resizable-image-container {
    position: relative;
    display: inline-block;
    border: 1px solid transparent;
}

.selected {
    border: 1px solid #0a6cff;
}

.editing {
    border: 1px solid #0a6cff;
}

.resizable-image-container img {
    display: block;
    user-select: none;
}

.resize-handle {
    --handle-width: -3px;

    position: absolute;
    width: 5px;
    height: 5px;
    background: #fff;
    border: 1px solid #0a6cff;
    cursor: pointer;
}

.resize-handle.top-left {
    top: var(--handle-width);
    left: var(--handle-width);
    cursor: nwse-resize;
}

.resize-handle.top-right {
    top: var(--handle-width);
    right: var(--handle-width);
    cursor: nesw-resize;
}

.resize-handle.bottom-left {
    bottom: var(--handle-width);
    left: var(--handle-width);
    cursor: nesw-resize;
}

.resize-handle.bottom-right {
    bottom: var(--handle-width);
    right: var(--handle-width);
    cursor: nwse-resize;
}
</style>
