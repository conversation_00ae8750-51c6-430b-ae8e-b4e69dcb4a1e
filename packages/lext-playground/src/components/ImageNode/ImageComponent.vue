<template>
    <div ref="containerRef" :style="`text-align: ${nodeState.__format || 'center'}`">
        <ResizableImage
            v-model:width="width"
            v-model:height="height"
            v-model:isResizing="isResizing"
            :src="src"
            :nodeKey="nodeState.__key"
            :selected="isSelected"
            :isLoading="isLoading"
            :isError="isError"
            editable
            :config="{
                minWidth: 10,
                minHeight: 10,
                maxWidth: nodeState.config.widthLimit!,
                maxHeight: nodeState.config.heightLimit!,
            }"
            @resize="handleResize"
        />
    </div>
</template>

<script lang="ts" setup>
import type { ImageNode } from '.'
import { useLexicalNodeSelection, useNodeClickEvent, useNodeDeleteEvent, useNodeFormatEvent } from '@crm/lext-core'

import ResizableImage from './ResizableImage.vue' // 可缩放图片组件

defineOptions({
    name: 'ImageComponent',
})

const { nodeState } = defineProps<{
    nodeState: ImageNode
}>()

const isLoading = ref(false)
const isError = ref(false)

const src = ref(nodeState.src)
const height = ref(nodeState.height)
const width = ref(nodeState.width)

const { isSelected } = useLexicalNodeSelection(nodeState.__key)

const isResizing = ref(false)

// 节点内容容器
const containerRef = ref<HTMLDivElement>()
useNodeDeleteEvent(nodeState.__key)
useNodeFormatEvent(nodeState.__key)
useNodeClickEvent(containerRef, nodeState.__key, isResizing, {
    deep: true,
    containerDisabled: true,
})

async function onImageUpload() {
    isLoading.value = true
    try {
        const response = await fetch(src.value)
        const blob = await response.blob()
        const formData = new FormData()
        formData.append('file', blob, src.value.split('/').pop() || '')
        formData.append('biz', 'IMG')
        const response2 = await fetch('/wapi/admin/file/upload', {
            method: 'POST',
            body: formData,
        })
        const raw = await response2.json()
        src.value = raw.data.uri as string
        return src
    } catch (e) {
        console.error(e)
        isError.value = true
    } finally {
        isLoading.value = false
    }
}

async function handleResize(w: number, h: number) {
    height.value = h
    width.value = w
}

watchEffect(() => {
    nodeState.onChange({
        src: src.value,
        width: width.value,
        height: height.value,
    })
})

onMounted(async () => {
    if (nodeState.src.startsWith('blob')) {
        isLoading.value = true
        try {
            const newSrc = await onImageUpload?.()
            if (newSrc) {
                src.value = newSrc.value
            }
        } catch (e) {
            isError.value = true
        } finally {
            isLoading.value = false
        }
    }
})
</script>
