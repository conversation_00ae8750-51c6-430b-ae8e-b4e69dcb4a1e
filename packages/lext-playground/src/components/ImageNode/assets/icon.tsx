export const icons = {
    'link-break-active': (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
            <path
                transform="matrix(1 0 0 1 3.98624 4.01349)"
                d="M3.69836 1.0909L6.29109 3.68363C6.48635 3.8789 6.80294 3.8789 6.9982 3.68363C7.19346 3.48837 7.19346 3.17179 6.9982 2.97653L4.40547 0.383794Q3.98484 -0.0368285 3.44363 -0.265724Q2.9193 -0.487485 2.35479 -0.499357Q1.78721 -0.511293 1.26629 -0.309283Q0.72429 -0.0990978 0.312596 0.312593Q-0.0990981 0.72429 -0.309283 1.26629Q-0.511293 1.78721 -0.499357 2.35479Q-0.487485 2.9193 -0.265724 3.44363Q-0.036828 3.98484 0.383791 4.40546L2.97653 6.9982C3.17179 7.19346 3.48837 7.19346 3.68363 6.9982C3.8789 6.80294 3.8789 6.48635 3.68363 6.29109L1.0909 3.69836Q0.517051 3.1245 0.500422 2.33377Q0.484052 1.55535 1.0197 1.0197Q1.55535 0.484052 2.33377 0.500422Q3.1245 0.517051 3.69836 1.0909Z"
                fill-rule="evenodd"
                fill="current"
            />
            <path
                transform="matrix(1 0 0 1 9.32413 9.35687)"
                d="M2.96101 -0.353553C2.76575 -0.158293 2.76575 0.158293 2.96101 0.353554L5.55375 2.94629Q6.14833 3.54087 6.17893 4.30328Q6.20957 5.0669 5.63332 5.61676Q5.03796 6.18488 4.2814 6.16148Q3.5308 6.13826 2.94629 5.55375L0.353553 2.96101C0.158293 2.76575 -0.158293 2.76575 -0.353553 2.96101C-0.548814 3.15627 -0.548814 3.47286 -0.353553 3.66812L2.23918 6.26085Q2.65923 6.6809 3.17911 6.91289Q3.69676 7.14387 4.25048 7.161Q4.81272 7.17839 5.34029 6.97294Q5.88147 6.7622 6.32368 6.34024Q6.77106 5.91333 6.99073 5.36394Q7.20117 4.83764 7.17812 4.26318Q7.15582 3.70734 6.91818 3.18296Q6.6808 2.65912 6.26085 2.23918L3.66812 -0.353553C3.47286 -0.548814 3.15627 -0.548814 2.96101 -0.353553Z"
                fill-rule="evenodd"
                fill="current"
            />
        </svg>
    ),
    'link-break': (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
            <path
                transform="matrix(1 0 0 1 3.98624 4.01349)"
                d="M3.69836 1.0909L6.29109 3.68363C6.48635 3.8789 6.80294 3.8789 6.9982 3.68363C7.19346 3.48837 7.19346 3.17179 6.9982 2.97653L4.40547 0.383794Q3.98484 -0.0368285 3.44363 -0.265724Q2.9193 -0.487485 2.35479 -0.499357Q1.78721 -0.511293 1.26629 -0.309283Q0.72429 -0.0990978 0.312596 0.312593Q-0.0990981 0.72429 -0.309283 1.26629Q-0.511293 1.78721 -0.499357 2.35479Q-0.487485 2.9193 -0.265724 3.44363Q-0.036828 3.98484 0.383791 4.40546L2.97653 6.9982C3.17179 7.19346 3.48837 7.19346 3.68363 6.9982C3.8789 6.80294 3.8789 6.48635 3.68363 6.29109L1.0909 3.69836Q0.517051 3.1245 0.500422 2.33377Q0.484052 1.55535 1.0197 1.0197Q1.55535 0.484052 2.33377 0.500422Q3.1245 0.517051 3.69836 1.0909Z"
                fill-rule="evenodd"
                fill="current"
            />
            <path
                transform="matrix(1 0 0 1 9.32413 9.35687)"
                d="M2.96101 -0.353553C2.76575 -0.158293 2.76575 0.158293 2.96101 0.353554L5.55375 2.94629Q6.14833 3.54087 6.17893 4.30328Q6.20957 5.0669 5.63332 5.61676Q5.03796 6.18488 4.2814 6.16148Q3.5308 6.13826 2.94629 5.55375L0.353553 2.96101C0.158293 2.76575 -0.158293 2.76575 -0.353553 2.96101C-0.548814 3.15627 -0.548814 3.47286 -0.353553 3.66812L2.23918 6.26085Q2.65923 6.6809 3.17911 6.91289Q3.69676 7.14387 4.25048 7.161Q4.81272 7.17839 5.34029 6.97294Q5.88147 6.7622 6.32368 6.34024Q6.77106 5.91333 6.99073 5.36394Q7.20117 4.83764 7.17812 4.26318Q7.15582 3.70734 6.91818 3.18296Q6.6808 2.65912 6.26085 2.23918L3.66812 -0.353553C3.47286 -0.548814 3.15627 -0.548814 2.96101 -0.353553Z"
                fill-rule="evenodd"
                fill="current"
            />
        </svg>
    ),
    'link-connect': (
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20">
            <path
                transform="matrix(1 0 0 1 3.98624 4.01349)"
                d="M3.69836 1.0909L6.29109 3.68363C6.48635 3.8789 6.80294 3.8789 6.9982 3.68363C7.19346 3.48837 7.19346 3.17179 6.9982 2.97653L4.40547 0.383794Q3.98484 -0.0368285 3.44363 -0.265724Q2.9193 -0.487485 2.35479 -0.499357Q1.78721 -0.511293 1.26629 -0.309283Q0.72429 -0.0990978 0.312596 0.312593Q-0.0990981 0.72429 -0.309283 1.26629Q-0.511293 1.78721 -0.499357 2.35479Q-0.487485 2.9193 -0.265724 3.44363Q-0.036828 3.98484 0.383791 4.40546L2.97653 6.9982C3.17179 7.19346 3.48837 7.19346 3.68363 6.9982C3.8789 6.80294 3.8789 6.48635 3.68363 6.29109L1.0909 3.69836Q0.517051 3.1245 0.500422 2.33377Q0.484052 1.55535 1.0197 1.0197Q1.55535 0.484052 2.33377 0.500422Q3.1245 0.517051 3.69836 1.0909Z"
                fill-rule="evenodd"
                fill="current"
            />
            <path
                transform="matrix(1 0 0 1 9.32413 9.3569)"
                d="M2.96101 -0.353553C2.76575 -0.158293 2.76575 0.158293 2.96101 0.353554L5.55375 2.94629Q6.14833 3.54087 6.17893 4.30328Q6.20957 5.0669 5.63332 5.61676Q5.03796 6.18488 4.2814 6.16148Q3.5308 6.13826 2.94629 5.55375L0.353553 2.96101C0.158293 2.76575 -0.158293 2.76575 -0.353553 2.96101C-0.548814 3.15627 -0.548814 3.47286 -0.353553 3.66812L2.23918 6.26085Q2.65923 6.6809 3.17911 6.91289Q3.69676 7.14387 4.25048 7.161Q4.81272 7.17839 5.34029 6.97294Q5.88147 6.7622 6.32368 6.34024Q6.77106 5.91333 6.99073 5.36394Q7.20117 4.83764 7.17812 4.26318Q7.15582 3.70734 6.91818 3.18296Q6.6808 2.65912 6.26085 2.23918L3.66812 -0.353553C3.47286 -0.548814 3.15627 -0.548814 2.96101 -0.353553Z"
                fill-rule="evenodd"
                fill="current"
            />
            <path
                transform="matrix(1 0 0 1 7.7831 7.72894)"
                d="M-0.353553 0.353554C-0.548814 0.158293 -0.548814 -0.158293 -0.353554 -0.353553C-0.158293 -0.548814 0.158293 -0.548814 0.353553 -0.353554L-0.353553 0.353554ZM4.57369 3.92077L1.64992 0.942813L0.353553 -0.353553L-0.353553 0.353553L0.942813 1.64992L3.86012 4.62135C4.05358 4.81839 4.37015 4.8213 4.56719 4.62784C4.76424 4.43438 4.76715 4.11781 4.57369 3.92077Z"
                fill-rule="evenodd"
                fill="current"
            />
        </svg>
    ),
    'insert-img': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="4 4 20 18">
            <path
                transform="matrix(1 0 0 1 5 5)"
                d="M0 1.875Q0 1.0918 0.53326 0.541333Q1.05767 0 1.79501 0L16.205 0C16.6966 0 17.1171 0.180445 17.4667 0.541336Q18 1.0918 18 1.875L18 14.125Q18 14.9082 17.4667 15.4587Q16.9423 16 16.205 16L1.79501 16Q1.05767 16 0.533263 15.4587Q0 14.9082 0 14.125L0 1.875ZM16.1053 10.995L10.6669 7.11355C10.256 6.82027 9.69092 6.87926 9.34944 7.25109L7.10527 9.69474L5.40649 8.42632C5.01884 8.13688 4.47957 8.16645 4.12589 8.49655L1.89474 10.579L1.89474 2L16.1053 2L16.1053 10.995ZM7 4.5C7 5.3284 6.3284 6 5.5 6C4.6716 6 4 5.3284 4 4.5C4 3.6716 4.6716 3 5.5 3C6.3284 3 7 3.6716 7 4.5Z"
                fill-rule="evenodd"
                fill="rgb(51, 51, 51)"
            />
        </svg>
    ),
    'align-left': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path
                transform="matrix(1 0 0 1 5 5)"
                d="M0 1C0 0.447715 0.447715 0 1 0L13 0C13.5523 0 14 0.447715 14 1L14 1.0015C14 1.55378 13.5523 2 13 2L1 2C0.447715 2 0 1.55378 0 1.0015L0 1ZM0 5C0 4.44772 0.447715 4 1 4L7 4C7.55228 4 8 4.44772 8 5L8 5.0015C8 5.55378 7.55228 6 7 6L1 6C0.447715 6 0 5.55378 0 5.0015L0 5ZM0 9C0 8.44771 0.447715 8 1 8L13 8C13.5523 8 14 8.44771 14 9L14 9.0015C14 9.55378 13.5523 10 13 10L1 10C0.447715 10 0 9.55378 0 9.0015L0 9ZM0 13C0 12.4477 0.447715 12 1 12L13 12C13.5523 12 14 12.4477 14 13L14 13.0015C14 13.5538 13.5523 14 13 14L1 14C0.447715 14 0 13.5538 0 13.0015L0 13Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
        </svg>
    ),
    'align-center': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path
                transform="matrix(1 0 0 1 5 5)"
                d="M0 1C0 0.447715 0.447715 0 1 0L13 0C13.5523 0 14 0.447715 14 1L14 1.0015C14 1.55378 13.5523 2 13 2L1 2C0.447715 2 0 1.55378 0 1.0015L0 1ZM3 5C3 4.44772 3.44772 4 4 4L10 4C10.5523 4 11 4.44772 11 5L11 5.0015C11 5.55378 10.5523 6 10 6L4 6C3.44772 6 3 5.55378 3 5.0015L3 5ZM0 9C0 8.44771 0.447715 8 1 8L13 8C13.5523 8 14 8.44771 14 9L14 9.0015C14 9.55378 13.5523 10 13 10L1 10C0.447715 10 0 9.55378 0 9.0015L0 9ZM3 13C3 12.4477 3.44772 12 4 12L10 12C10.5523 12 11 12.4477 11 13L11 13.0015C11 13.5538 10.5523 14 10 14L4 14C3.44772 14 3 13.5538 3 13.0015L3 13Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
        </svg>
    ),
    'align-right': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path
                transform="matrix(1 0 0 1 5 5)"
                d="M0 1C0 0.447715 0.447715 0 1 0L13 0C13.5523 0 14 0.447715 14 1L14 1.0015C14 1.55378 13.5523 2 13 2L1 2C0.447715 2 0 1.55378 0 1.0015L0 1ZM6 5C6 4.44772 6.44772 4 7 4L13 4C13.5523 4 14 4.44772 14 5L14 5.0015C14 5.55378 13.5523 6 13 6L7 6C6.44772 6 6 5.55378 6 5.0015L6 5ZM0 9C0 8.44771 0.447715 8 1 8L13 8C13.5523 8 14 8.44771 14 9L14 9.0015C14 9.55378 13.5523 10 13 10L1 10C0.447715 10 0 9.55378 0 9.0015L0 9ZM0 13C0 12.4477 0.447715 12 1 12L13 12C13.5523 12 14 12.4477 14 13L14 13.0015C14 13.5538 13.5523 14 13 14L1 14C0.447715 14 0 13.5538 0 13.0015L0 13Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
        </svg>
    ),
    'bold': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path
                transform="matrix(1 0 0 1 7 6)"
                d="M2.10526 10L2.10526 7L6.31578 7C7.18781 7 7.89473 7.67157 7.89473 8.5C7.89473 9.32843 7.18781 10 6.31578 10L2.10526 10ZM6.31578 12L1 12C0.447715 12 0 11.5523 0 11L0 1C0 0.447715 0.447715 0 1 0L6.31579 0C8.35052 0 10 1.567 10 3.5C10 4.47934 9.5766 5.36474 8.8942 6C9.5766 6.63526 10 7.52066 10 8.5C10 10.433 8.35052 12 6.31578 12ZM6.31578 5C7.18781 5 7.89473 4.32843 7.89473 3.5C7.89473 2.67157 7.18781 2 6.31578 2L2.10526 2L2.10526 5L6.31578 5Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
        </svg>
    ),
    'italics': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path
                transform="matrix(1 0 0 1 8 6)"
                d="M2 0C1.44772 0 1 0.447715 1 1L1 1.0015C1 1.55378 1.44772 2 2 2L3.61759 2L2.20698 10L1 10C0.447715 10 0 10.4477 0 11L0 11.0015C0 11.5538 0.447715 12 1 12L6 12C6.55228 12 7 11.5538 7 11.0015L7 11C7 10.4477 6.55228 10 6 10L4.23783 10L5.64844 2L7 2C7.55228 2 8 1.55378 8 1.0015L8 1C8 0.447715 7.55228 0 7 0L2 0Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
        </svg>
    ),
    'underline': (
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
            <path
                transform="matrix(1 0 0 1 7 6)"
                d="M2 1L2 5C2 6.65685 3.34315 8 5 8C6.65685 8 8 6.65685 8 5L8 1C8 0.447715 8.44772 0 9 0L9.0015 0C9.55378 0 10 0.447715 10 1L10 5C10 7.76142 7.76142 10 5 10C2.23858 10 0 7.76142 0 5L0 1C0 0.447715 0.447715 0 1 0L1.0015 0C1.55378 0 2 0.447715 2 1ZM0 12C0 11.4477 0.447715 11 1 11L9 11C9.55229 11 10 11.4477 10 12L10 12.0015C10 12.5538 9.55229 13 9 13L1 13C0.447715 13 0 12.5538 0 12.0015L0 12Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
        </svg>
    ),
    'arrow-down': (
        <svg xmlns="http://www.w3.org/2000/svg" width="6" height="4" viewBox="0 0 6 4">
            <path
                transform="matrix(1 0 0 -1 0 4)"
                d="M2.64772 0.161546L0.0769245 3.41155C-0.11981 3.66026 0.0835787 4 0.429206 4L5.57079 4C5.91642 4 6.11981 3.66026 5.92308 3.41155L3.35228 0.161546C3.1819 -0.0538487 2.8181 -0.0538487 2.64772 0.161546Z"
                fill-rule="nonzero"
                fill="rgb(40, 41, 45)"
            />
        </svg>
    ),
    'text-color': (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
            <path
                transform="matrix(1 0 0 1 1.97739 1)"
                d="M6.5648 0C6.97218 0 7.33883 0.247122 7.49171 0.624729L11.0094 9.31336C11.1425 9.64207 10.9006 10.001 10.5459 10.001L9.44735 10.001C9.25664 10.001 9.08254 9.89251 8.9985 9.72131L7.66278 7L3.38078 7L2.04603 9.72119C1.96203 9.89246 1.78789 10.001 1.59713 10.001L0.500501 10.001C0.145911 10.001 -0.0959997 9.64214 0.0370107 9.31345L3.55292 0.624889C3.70575 0.247198 4.07246 0 4.4799 0L6.5648 0ZM5.22078 2L5.82278 2L6.92678 5L4.11678 5L5.22078 2Z"
                fill-rule="evenodd"
                fill="rgb(49, 50, 51)"
            />
            <path
                transform="matrix(1 0 0 1 1.5 12)"
                d="M0 1C0 0.447715 0.447715 0 1 0L11 0C11.5523 0 12 0.447715 12 1L12 2C12 2.55228 11.5523 3 11 3L1 3C0.447715 3 0 2.55228 0 2L0 1Z"
                fill-rule="evenodd"
                fill="currentColor"
            />
        </svg>
    ),
}
