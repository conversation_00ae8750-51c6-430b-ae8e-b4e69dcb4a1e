<template>
    <IconData :width="size" :height="size" :fill="color" />
</template>

<script setup lang="ts">

import { icons } from './icon'

interface Props {
    name: keyof typeof icons
    size?: number | string
    color?: string
}

const props = withDefaults(defineProps<Props>(), {
    size: 20,
    color: 'currentColor',
})

const IconData = computed(() => icons[props.name])
</script>

<style scoped>
div {
    display: inline-block;
    vertical-align: middle;
}
</style>
