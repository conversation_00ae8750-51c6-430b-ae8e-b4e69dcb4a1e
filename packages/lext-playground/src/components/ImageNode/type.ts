import type { Spread } from 'lexical'
import type { SerializedDecoratorBlockNode } from '../LexicalDecoratorBlockNode'

export interface ImageConfig {
    defaultWidth: number
    defaultHeight: number
    widthLimit?: number
    heightLimit?: number
}

export interface ImageComponentProps {
    src: string
    config: ImageConfig
    loading?: boolean
    width?: number
    height?: number
    key?: string
}

export type SerializedImageNode = Spread<ImageComponentProps, SerializedDecoratorBlockNode>
