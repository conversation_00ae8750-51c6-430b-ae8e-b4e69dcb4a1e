# 包版本升级

1.npm 切换到公司内部源

2.npm login

3.执行升级版本命令 npm version patch

```shell
npm version patch

npm version后面参数说明：
patch：小变动，比如修复bug等，版本号变动 v1.0.0->v1.0.1
minor：增加新功能，不影响现有功能,版本号变动 v1.0.0->v1.1.0
major：破坏模块对向后的兼容性，版本号变动 v1.0.0->v2.0.0
```

4.执行构建命令 ：pnpm build

5.修改 package.json private true=> false

6.发布：npm publish

7.修改 package.json private false=> true

8.把本地 git 变更推送的远程仓库

npm version patch && pnpm build && npm publish && git push