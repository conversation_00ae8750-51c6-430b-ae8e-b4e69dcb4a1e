// 防抖
type Fn = (...args: any[]) => any;

export function debounce<T extends Fn>(fn: T, limit = 300): (...args: Parameters<T>) => ReturnType<T> {
    let id: any;
    let result: any;

    return (...args: any[]) => {
        clearTimeout(id);

        id = setTimeout(() => {
            result = fn(...args);
        }, limit);

        return result;
    };
}

function rotatePoint(x: number, y: number, centerX: number, centerY: number, angleDegrees: number) {
    // 将角度转换为弧度
    const angleRadians = (angleDegrees * Math.PI) / 180;

    // 计算相对于中心点的偏移
    const dx = x - centerX;
    const dy = y - centerY;

    // 应用旋转公式
    const rotatedX = centerX + dx * Math.cos(angleRadians) - dy * Math.sin(angleRadians);
    const rotatedY = centerY + dx * Math.sin(angleRadians) + dy * Math.cos(angleRadians);

    return { x: rotatedX, y: rotatedY };
}

interface IBaseParams {
    startX: number;
    startY: number;
    centerX: number;
    centerY: number;
}

// 动态角度计算坐标
export function calculateRotatedCoordinates(angle: number, baseParams: IBaseParams) {
    const { startX, startY, centerX, centerY } = baseParams || {};
    const result = rotatePoint(startX, startY, centerX, centerY, angle);
    return { x: result.x, y: result.y } as { x: number; y: number };
}
