<template>
    <div v-if="data" class="product-8 report-preview-page">
        <WaterMark
            :content="data.watermarkStr || '001-20240101-163'"
            :waterMarkConfig="props.requestParams ? 2 : props.params?.watermark ? props.params.watermark : data.watermark"
        >
            <Cover v-if="moduleCodes.includes('1')" :data="data" :logo="effectiveParams.reportLogo || data.reportLogo" />
            <div class="content-page single-page-wrap">
                <component
                    :is="item.component"
                    v-for="item of componentsRender"
                    :key="item.moduleCode"
                    :data="data"
                    :staticConfig="staticConfig"
                    :moduleCodes="moduleCodes.filter((x) => x !== '1')"
                    :headerFooterConfig="headerFooterConfig(item.moduleCode, JSON.parse(reportModuleCodes))"
                />
            </div>
        </WaterMark>
    </div>
</template>

<script setup lang="ts">
import WaterMark from '@packages/exam-report/components/watermark/index.vue'
import { debounce } from '@packages/exam-report/utils/index'

import Attach from './components-atom/attach.vue'
import ComprehensiveResult from './components-atom/comprehensive-result.vue'
import { staticConfig } from './components-atom/const'
import Cover from './components-atom/cover.vue'
import DetailResult from './components-atom/detail-result.vue'
import GuanLiPeiYangJianYi from './components-atom/guan-li-pei-yang-jian-yi.vue'
import InterviewAdvise from './components-atom/interview-advise.vue'
import ReportInstruction from './components-atom/report-instruction.vue'
import { res } from './mock'
import { headerFooterConfig } from './util/show-footer'
import { useName } from './util/use-name'

defineOptions({
    name: 'ReportPreview',
    inheritAttrs: false,
})
const props = defineProps({
    productId: {
        type: Number,
        default: 1,
    },
    params: {
        type: Object,
        default: () => ({}),
    },
    getDataFunction: {
        type: Function,
        default: () => {},
    },
    requestParams: {
        type: Object,
    },
    reportModuleCodes: {
        type: String,
        default: '[1, 2, 3, 4, 10, 7, 6]',
    },
})
const { getLocalName } = useName()

const effectiveParams = computed(() => {
    return {
        reportTitle: props.params.reportTitle,
        reportSubTitle: props.params.extraParam?.templateSubTitle,
        matchJobList: JSON.stringify(props.params.extraParam?.matchJobList),
        reportLogo: props.params.reportLogo,
        reportExamineeField: JSON.stringify(props.params.reportExamineeField?.map(x => x.code)),
        reportDimensionField: JSON.stringify(props.params.reportDimensionField?.map(x => ({ code: x.code, showName: x.showName || x.dimName }))),
    }
})
const moduleCodes = ref([])
const ComponentsAll = [
    { moduleCode: '1', component: Cover },
    { moduleCode: '2', component: ReportInstruction },
    { moduleCode: '3', component: ComprehensiveResult },
    { moduleCode: '4', component: DetailResult },
    { moduleCode: '10', component: GuanLiPeiYangJianYi },
    { moduleCode: '7', component: InterviewAdvise },
    { moduleCode: '6', component: Attach },
]
const componentsRender = ref([])
const data = ref()

let originReportName = ''
let originReportSubTitle = ''
async function getDetail() {
    const params = props.requestParams
        ? props.requestParams
        : {
                reportExaminees: effectiveParams.value.reportExamineeField,
                reportModules: effectiveParams.value.reportDimensionField,
                extraParam: {
                    matchJobList: props.params.extraParam?.matchJobList,
                },
            }
    const res = await props.getDataFunction(params, props.productId)
    if (res.code === 0) {
        data.value = undefined
        await nextTick()
        data.value = res.data || {}
        originReportName = data.value.templateName
        originReportSubTitle = data.value.reportSubTitle
        data.value.templateName = effectiveParams.value.reportTitle || originReportName
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle
        getLocalName(data.value)
    }
}

async function init() {
    await Promise.all([getDetail()])
}
function reLayout() {
    setTimeout(() => {
        doLayout()
        doLayout2()
    }, 0)
}
onMounted(async () => {
    await init()
    reLayout()
})
function calcHeight(element) {
    const height = element.offsetHeight + Number.parseFloat(getComputedStyle(element).marginTop) + Number.parseFloat(getComputedStyle(element).marginBottom)
    return height
}
function doLayout() {
    const pageTotalHeight = 1124
    const pageHeaderHeight = 67 + 0
    const pageFooterHeight = 67 + 0
    const avaliableHeight = pageTotalHeight - pageHeaderHeight - pageFooterHeight

    const pageSplitDom = document.createElement('div')
    pageSplitDom.classList.add('page-split')
    const pageHeaderDom = document.querySelector('.page-header-wrap')
    const pageFooterDom = document.querySelector('.page-footer-wrap')

    let tempHeightSum = 0
    const atoms = [...document.querySelectorAll('.single-page-wrap.content-page > *:not(.page-header-wrap):not(.page-footer-wrap)')]
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i]
        const atomHeight = calcHeight(atom)
        const nextAtomHeight = atoms[i + 1] ? calcHeight(atoms[i + 1]) : 0
        tempHeightSum += atomHeight
        const condition1 = atom.nextElementSibling?.classList.contains('page-footer-wrap') // 下个元素是页脚
        const condition2 = tempHeightSum + nextAtomHeight > avaliableHeight // 剩余可用高度不足以放下下一个元素
        let condition3 = false
        if (atom.classList.contains('level-two-dimension') && atoms[i + 1]?.classList.contains('level-one-dimension')) {
            condition3 = tempHeightSum > avaliableHeight / 2
        }
        if (condition1 || condition2 || condition3) {
            tempHeightSum = 0
            if (!condition1) {
                atom.insertAdjacentHTML('afterend', pageHeaderDom.outerHTML)
                atom.insertAdjacentHTML('afterend', pageFooterDom.outerHTML)
            }
        }
    }
    const allPageHeaders = [...document.querySelectorAll('.page-header-wrap')]
    const allPageFooters = [...document.querySelectorAll('.page-footer-wrap')]
    // for (let i = 0; i < allPageHeaders.length; i++) {
    //     // 插入分页符
    //     const element = allPageHeaders[i];
    //     element.insertAdjacentHTML('beforebegin', pageSplitDom.outerHTML);
    // }
    for (let i = 0; i < allPageFooters.length; i++) {
        // 更改页脚的页码
        const element = allPageFooters[i]
        element.querySelector('.page-count').innerText = i + 1
    }
}
function doLayout2() {
    const pageSplitDom = document.createElement('div')
    pageSplitDom.classList.add('page-split')
    const divs = []
    const wrapper = document.querySelector('.single-page-wrap.content-page')
    const atoms = [...document.querySelectorAll('.single-page-wrap.content-page > *')]
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i]
        if (atom.classList.contains('page-header-wrap')) {
            divs[divs.length] = document.createElement('div')
        }
        divs[divs.length - 1].appendChild(atom)
    }
    for (let i = 0; i < divs.length; i++) {
        const div = divs[i]
        div.style.height = '1124px'
        if (i === divs.length - 1) {
            div.style.height = '1117px'
            div.classList.add('last-div')
        }
        div.style.position = 'relative'
        const footer = div.querySelector('.page-footer-wrap')
        footer.style.position = 'absolute'
        footer.style.bottom = 0
        footer.style.marginTop = 0
        wrapper.appendChild(div)
        div.insertAdjacentHTML('beforebegin', pageSplitDom.outerHTML)
    }
}
watch(
    () => effectiveParams.value.reportTitle,
    () => {
        data.value.templateName = effectiveParams.value.reportTitle || originReportName
        const doms = [...document.querySelectorAll('.page-footer-wrap .report-name-filed')]
        for (let i = 0; i < doms.length; i++) {
            const el = doms[i]
            el.innerText = data.value.templateName
        }
    },
    {},
)
watch(
    () => effectiveParams.value.reportSubTitle,
    () => {
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle
    },
    {},
)
watch(
    [() => effectiveParams.value.reportExamineeField, () => effectiveParams.value.reportDimensionField, () => effectiveParams.value.matchJobList],
    debounce(async () => {
        await getDetail()
        reLayout()
    }, 500),
    {},
)
watch(
    () => props.reportModuleCodes,
    debounce(async () => {
        moduleCodes.value = JSON.parse(props.reportModuleCodes).map(x => String(x))
        const localModuleCodes = moduleCodes.value.filter(x => x !== '1')
        componentsRender.value = localModuleCodes.map(x => ComponentsAll.find((y: any) => y.moduleCode === x))
        const tempData = data.value
        data.value = undefined
        await nextTick()
        data.value = tempData
        reLayout()
    }, 500),
    {
        immediate: true,
    },
)
</script>

<style lang="less">
// @import './index.less';
.report-preview-page.product-8 .page-header-wrap {
    margin-bottom: 0;
}
</style>
