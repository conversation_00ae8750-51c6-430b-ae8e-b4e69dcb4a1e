.shadow-text-wrapper-base {
    display: flex;
    flex-wrap: wrap;
}

.shadow-text-base() {
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 26px;
}

.paragraph-dot-father-base(@padding-left, @line-height) {
    --line-height: @line-height;

    padding-left: @padding-left;
    position: relative;
}

.paragraph-dot-child-base() {
    content: '';
    width: 4px;
    height: 4px;
    border-radius: 50%;
    background: #1f1f1f;
    position: absolute;
    left: 0;
    top: calc(var(--line-height) / 2);
    transform: translateY(-50%);
}