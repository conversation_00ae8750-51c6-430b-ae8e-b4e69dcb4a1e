<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="测评介绍" />
    <div class="normal-content">
        {{ data.testDescription }}
    </div>
    <div class="level-img-wrap">
        <img src="https://img.bosszhipin.com/static/file/2024/hs07lejlp11709288492820.png.webp">
    </div>
    <div v-for="item of data.oneLevelDimensionInfoList" :key="item.encryptId" class="level-item">
        <div class="level-name">
            {{ item.dimensionName }}
        </div>
        <div class="level-desc">
            {{ item.description }}
        </div>
    </div>
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import SectionTitle from '../components/section-title.vue'

defineOptions({
    name: 'ReportInstruction',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    moduleCodes: {
        type: Array,
        default: () => ([]),
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
</script>

<style lang="less" scoped>
.level-img-wrap {
    background: #f7f7f7;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
    padding: 32px 0 34px;
    margin-top: 32px;
    display: flex;
    justify-content: center;
    img {
        display: block;
        width: 260px;
        height: 169px;
    }
}
.level-item {
    background: #f7f7f7;
    padding-left: 32px;
    padding-right: 32px;
    padding-bottom: 24px;
    display: flex;
    .level-name {
        font-family: var(--title-sub-font-family);
        width: 80px;
        word-break: break-all;
        font-size: 14px;
        font-weight: normal;
        line-height: 26px;
        flex-shrink: 0;
        margin-right: 16px;
    }
    .level-desc {
        font-size: 12px;
        line-height: 26px;
        flex-shrink: 0;
        flex-grow: 1;
        flex-basis: 0%;
    }
}
.page-header-wrap {
    & + .level-item {
        padding-top: 24px;
    }
}
</style>
