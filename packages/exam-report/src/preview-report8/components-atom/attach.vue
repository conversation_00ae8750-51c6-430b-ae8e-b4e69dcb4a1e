<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="附录" />
    <div style="margin-bottom: 32px">
        <SubTitle title="使用说明" />
        <div class="normal-content">
            {{ data.useIntroduction }}
        </div>
    </div>
    <RefrenceLevel :info="data" />
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import RefrenceLevel from '../components/refrence-level.vue'
import SectionTitle from '../components/section-title.vue'
import SubTitle from '../components/sub-title.vue'

defineOptions({
    name: 'Attach',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    moduleCodes: {
        type: Array,
        default: () => ([]),
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
</script>
