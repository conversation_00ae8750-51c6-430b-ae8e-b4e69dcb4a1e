<template>
    <div
        class="single-page-wrap cover-page"
        style="background-image: url('https://img.bosszhipin.com/static/file/2024/ykldght41c1718874104858.png'); background-position: center bottom"
    >
        <div class="logo-img" v-if="logo"><img :src="logo" /></div>
        <div class="logo-text" v-else>LOGO</div>
        <div class="report-title">{{ data.templateName }}</div>
        <div class="font-wrap">
            <div class="report-sub-title">{{ data.reportSubTitle }}</div>
            <div class="examinee-info">
                <div class="info-wrap">
                    <div class="info-item" v-for="(item, index) of data.examineeInfoList" :key="index">{{ item.fieldShowName }}：{{ item.fieldValue }}</div>
                </div>
            </div>
        </div>
        <Copyright></Copyright>
    </div>
</template>
<script setup lang="ts">
import Copyright from '../components/copyright.vue';
defineOptions({
    name: 'Cover',
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    logo: {
        type: String,
        default: '',
    },
});
</script>
<style lang="less" scoped>
:deep(.copyright-wrap) {
    --copy-right-color: rgba(16, 16, 16, 0.3);
}
.report-preview-page {
    .single-page-wrap.cover-page {
        padding: 0;
        .report-title,
        .font-wrap {
            position: absolute;
            left: 0;
            right: 0;
            padding: 0 50px 0 65px;
        }

        .report-title {
            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 48px;
            line-height: normal;
            margin: 0;
            text-align: right;
            bottom: 349px;
        }
        .font-wrap {
            top: 791px;
        }
        .report-sub-title {
            color: #151515;
            font-family: var(--FZLanTingHeiS-L-GB);
            font-size: 18px;
            line-height: 32px;
            text-align: right;
            opacity: 0.5;
            word-break: normal;
            letter-spacing: 0.5px;
            padding: 0;
        }
        .examinee-info {
            color: #333333;
            font-size: 15px;
            text-align: right;
            justify-content: flex-end;
            line-height: 24px;
            margin-top: 10px;
            font-family: var(--FZLanTingHeiS-R-GB);
            .info-wrap {
                .info-item {
                    & + .info-item {
                        margin-top: 0;
                    }
                }
            }
        }
    }
}
</style>
