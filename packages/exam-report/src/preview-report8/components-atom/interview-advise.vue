<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="岗位素质模型匹配度" />
    <p class="job-matching-explain">
        {{ data.matchJobDesc }}
    </p>

    <template v-for="(matchJobItem, matchJobIndex) of data.matchJobList">
        <p class="job-matching-title">
            {{ matchJobItem?.jobName }}-岗位素质模型匹配结果
        </p>
        <div class="result-tit">
            <p>受测者与{{ matchJobItem?.jobName }}岗位素质模型的匹配度为</p>
            <!-- <div class="level" v-if="info.indicativeResultDesc">{{ info.indicativeResultDesc }}</div> -->
            <div
                class="level-tag"
                :style="{
                    color: staticLevelConfig[matchJobItem.level].color.fontColor,
                    backgroundColor: staticLevelConfig[matchJobItem.level].color.bgColor,
                    borderColor: staticLevelConfig[matchJobItem.level].color.barColor,
                }"
            >
                {{ staticLevelConfig[matchJobItem.level].name }}
            </div>
            <p>分值：</p>
            <p class="number" :style="{ color: staticLevelConfig[matchJobItem.level].color.fontColor }">
                {{ matchJobItem.score }}%
            </p>
        </div>
        <div v-if="matchJobItem.jobImage" class="result-img" :style="{ backgroundImage: `url(${matchJobItem.jobImage})` }" />
        <div v-if="data.matchJobIntroduction" class="result-desc">
            {{ data.matchJobIntroduction }}
        </div>
        <p class="character-feature-describe">
            以下呈现的是{{ matchJobItem.jobName }}岗位优秀人员五维性格特征描述：
        </p>
        <template v-for="(l1Item, l1Index) of matchJobItem.subDimensionScoreList" :key="l1Item.encryptDimensionId">
            <div class="character-feature-item">
                <LevelOneDimension
                    :data="l1Item"
                    :index="l1Index"
                    :score="data.score"
                    :showType="1"
                />
            </div>
        </template>
        <p style="height: 34px;" />
        <RichText :domString="matchJobItem.jobContent" />
        <PageFooter v-if="matchJobIndex < data.matchJobList.length - 1" :info="data" />
        <PageHeader v-if="matchJobIndex < data.matchJobList.length - 1" />
    </template>

    <!-- <div
        class="level-one-dimension"
        :style="{ backgroundImage: `linear-gradient(to right, ${staticConfig[l1Index % staticConfig.length].color.gradientStart} 0%, #fff 100%)` }"
    >
        <div class="bg" :style="{ backgroundImage: `url('${staticConfig[l1Index % staticConfig.length].img}')` }"></div>
        <div class="level-one-title-wrap">
            <div class="level-one-dimension-name">{{ l1Item.dimensionName }}</div>
            <div class="level-one-dimension-score">
                <span class="label">得分</span><span class="number" :style="{ color: staticConfig[l1Index % staticConfig.length].color.fontColor }">{{ l1Item.score }}</span>
            </div>
        </div>
        <div class="level-one-dimension-introduction">{{ l1Item.dimensionDetailInt }}</div>
    </div> -->

    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import RichText from '@packages/exam-report/components/rich-text/index.vue'
import { staticLevelConfig } from './const'
import LevelOneDimension from '../components/level-one-dimension.vue'
import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import SectionTitle from '../components/section-title.vue'

defineOptions({
    name: 'InterviewAdvise',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    moduleCodes: {
        type: Array,
        default: () => ([]),
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
</script>

<style lang="less">
.job-matching-explain {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    // text-indent: 1em;
    white-space: pre-wrap;
}
.job-matching-title {
    margin-top: 34px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    text-align: center;
}

.result-tit {
    margin-top: 22px;
    display: flex;
    align-items: end;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    .level-tag {
        margin: 0 14px;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: #cce9fe;
        border: 1px solid rgba(0, 146, 250, 0.37);
        text-align: center;
        line-height: 32px;
        color: #0092fa;
        font-family: var(--FZLanTingHeiS-B-GB);
        font-size: 22px;
        font-style: normal;
        font-weight: 400;
    }

    .number {
        color: #0092fa;
        font-weight: 600;
    }
}

.result-img {
    margin: 22px auto 10px;
    width: 690px;
    height: 180px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.result-desc {
    color: #808080;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.character-feature-describe {
    margin-top: 34px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.character-feature-item {
    margin-top: 10px;
}
</style>
