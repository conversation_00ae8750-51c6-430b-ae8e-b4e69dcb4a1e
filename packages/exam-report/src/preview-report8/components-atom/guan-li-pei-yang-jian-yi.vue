<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <template v-if="isSpCorp" v-for="(dimensionItem, dimensionIndex) in dataList" :key="dimensionIndex">
        <template v-for="(behaviorItem, behaviorIndex) in formatList(dimensionItem)" :key="behaviorIndex">
            <div class="behavior-item" :style="{ marginTop: calcMarginTop(dimensionIndex, behaviorIndex) }">
                <SectionTitle v-if="dimensionIndex === 0 && behaviorIndex === 0" title="管理培养建议" style="margin-bottom: 12px" />
                <SubTitle v-if="behaviorIndex === 0" :title="dimensionItem.name" style="margin-bottom: 10px" />
                <div
                    class="behavior-title"
                    :class="
                        isSpCorp
                            ? {
                                  good: behaviorIndex === 0,
                                  bad: behaviorIndex === 1,
                                  advice: behaviorIndex === 2,
                              }
                            : {
                                  good: behaviorIndex === 0,
                                  advice: behaviorIndex === 1,
                              }
                    "
                >
                    <span class="triangle">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="9" height="10.3923" viewBox="0 0 9 10.3923">
                            <path transform="matrix(0 1 -1 0 9 -0.803833)" d="M6 0L11.1962 9L0.803847 9L6 0Z" fill-rule="nonzero" fill="var(--color-main)" />
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="9" height="10.3923" viewBox="0 0 9 10.3923">
                            <path transform="matrix(0 1 -1 0 9 -0.803833)" d="M6 0L11.1962 9L0.803847 9L6 0Z" fill-rule="nonzero" fill="var(--color-sub)" />
                        </svg>
                    </span>
                    <span v-if="[0, 1].includes(behaviorIndex)">{{ localName }}</span>
                    <template v-if="isSpCorp">
                        <span v-if="behaviorIndex === 0" class="good">可能展现的优势行为</span>
                        <span v-if="behaviorIndex === 1" class="bad">可能展现的待改善行为</span>
                        <span v-if="behaviorIndex === 2" class="advice">管理培养建议</span>
                    </template>
                    <template v-else>
                        <span v-if="behaviorIndex === 0" class="good">可能展现的突出行为</span>
                        <span v-if="behaviorIndex === 1" class="advice">管理培养建议</span>
                    </template>
                </div>
                <div v-for="(wordsItem, wordsIndex) in behaviorItem" :key="wordsIndex" class="behavior-desc normal-content">
                    <span>{{ wordsItem }}</span>
                </div>
                <div v-if="!behaviorItem?.length" class="behavior-desc-no-content normal-content">
                    <template v-if="isSpCorp">
                        <span v-if="behaviorIndex === 0">目前未展现出明显的行为优势</span>
                        <span v-if="behaviorIndex === 1">目前未展现出明显的待改善行为</span>
                        <span v-if="behaviorIndex === 2">建议继续发挥其优势，同时为其提供更多挑战性任务和成长机会，以进一步提升其价值和潜力</span>
                    </template>
                    <template v-else>
                        <span v-if="behaviorIndex === 0">目前无突出行为</span>
                        <span v-if="behaviorIndex === 1">暂无针对性建议，可继续发挥其优势，同时为其提供更多挑战性任务和成长机会，以进一步提升其价值和潜力。</span>
                    </template>
                </div>
            </div>
        </template>
    </template>
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import PageFooter from '../components/page-footer.vue';
import PageHeader from '../components/page-header.vue';
import SectionTitle from '../components/section-title.vue';
import SubTitle from '../components/sub-title.vue';
import { useName } from '../util/use-name';

defineOptions({
    name: 'GuanLiPeiYangJianYi',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        required: true,
    },
    moduleCodes: {
        type: Array,
        default: () => [],
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean; footer: boolean }>,
    },
});
const { localName } = useName();

const isSpCorp = computed(() => props.data.spCorp === 1);

const dataList = computed(() => {
    return props.data.cultivateSuggestList.map((item: any) => {
        const { advance, weak } = item;
        console.log(item);
        return {
            ...item,
            collection: [...advance, ...weak],
        };
    });
});

const formatList = (itemData: any) => {
    const { advance, weak, suggest, collection } = itemData;
    return isSpCorp.value ? [advance, weak, suggest] : [collection, suggest];
};

function calcMarginTop(dimensionIndex: number, behaviorIndex: number): string {
    if (behaviorIndex === 0) {
        return '23px';
    } else {
        return '10px';
    }
}
</script>

<style lang="less" scoped>
@import url(../assets/mixins.less);
.behavior-item {
    .behavior-title {
        &.good {
            --color-main: #00bebd;
            --color-sub: rgba(0, 190, 189, 0.3);
        }
        &.bad {
            --color-main: #fc9d64;
            --color-sub: rgba(252, 157, 100, 0.3);
        }
        &.advice {
            --color-main: #1a1a1a;
            --color-sub: rgba(26, 26, 26, 0.3);
        }
        .triangle {
            margin-right: 10px;
            svg {
                vertical-align: middle;
                &:nth-child(2) {
                    margin-left: -4px;
                }
            }
        }
        color: var(--color-main);
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 13px;
        line-height: 26px; /* 2 */
        margin-bottom: 10px;
    }
    .behavior-desc {
        .paragraph-dot-father-base(16px, 26px);
        &::before {
            .paragraph-dot-child-base();
        }
    }
    .behavior-desc-no-content {
        color: #cccccc;
        .paragraph-dot-father-base(16px, 26px);
        &::before {
            .paragraph-dot-child-base();
            background: #cccccc;
        }
    }
}
</style>
