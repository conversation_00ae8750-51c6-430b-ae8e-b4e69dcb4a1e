<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="综合结果" />
    <div class="section-intro" style="margin-bottom: 12px; color: inherit">
        {{ data.comprehensiveDesc }}
    </div>
    <div v-for="(item, index) of data.allDimensionScoreList" :key="item.encryptDimensionId" class="level-one-wrap">
        <div class="level-through" :style="{ backgroundImage: `linear-gradient(to right, ${staticConfig[index % staticConfig.length].color.gradientStart} 0%, #fff 100%)` }">
            <div class="bg" :style="{ backgroundImage: `url('${staticConfig[index % staticConfig.length].img}')` }" />
            <div class="dimension-name">
                {{ item.dimensionName }}
            </div>
            <div class="level-one-desc" :style="{ 'color': staticConfig[index % staticConfig.length].color.fontColor, 'text-align': 'right' }">
                {{ item.leftDesc }}
            </div>
            <Progress
                class="progress-wrap"
                :splitArray="data.dimensionLevelArray"
                :colorConfig="staticConfig[index % staticConfig.length].color"
                :value="item.score"
            />
            <div class="level-one-desc" :style="{ 'color': staticConfig[index % staticConfig.length].color.fontColor, 'text-align': 'left' }">
                {{ item.rightDesc }}
            </div>
        </div>
        <div class="level-desc">
            {{ item.levelLanguage }}
        </div>
    </div>
    <div class="comprehensive-introduction">
        {{ data.comprehensiveIntroduction }}
    </div>
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import Progress from '../components/progress.vue'
import SectionTitle from '../components/section-title.vue'

defineOptions({
    name: 'ComprehensiveResult',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    staticConfig: {
        type: Array,
        default: () => [],
    },
    moduleCodes: {
        type: Array,
        default: () => ([]),
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
</script>

<style lang="less" scoped>
.level-one-wrap {
    .level-through {
        height: 76px;
        border-radius: 8px;
        position: relative;
        display: flex;
        align-items: center;
        & > * {
            flex-shrink: 0;
        }
        .bg {
            width: 68px;
            height: 68px;
            position: absolute;
            left: 0;
            bottom: 0;
            background-repeat: no-repeat;
            background-size: contain;
        }
        .dimension-name {
            font-family: var(--title-sub-font-family);
            width: 94px;
            margin-left: 43px;
            margin-right: 26px;
            font-size: 15px;
            position: relative;
            z-index: 1;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        .level-one-desc {
            font-size: 12px;
            line-height: 14px;
            width: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        .progress-wrap {
            margin: 0 13px;
        }
    }
    .level-desc {
        padding: 12px 0 24px;
        font-size: 13px;
        line-height: 26px;
    }
}
.comprehensive-introduction {
    font-size: 12px;
    color: #808080;
    line-height: 20px;
    white-space: pre-wrap;
    word-break: break-all;
}
</style>
