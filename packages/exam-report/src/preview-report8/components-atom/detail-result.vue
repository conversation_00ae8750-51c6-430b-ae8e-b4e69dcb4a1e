<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="详细结果" />
    <template v-for="(l1Item, l1Index) of data.allDimensionScoreList" :key="l1Item.encryptDimensionId">
        <LevelOneDimension :data="l1Item" :index="l1Index" :score="l1Item.score" />
        <div class="level-two-dimension-introduction">
            {{ l1Item.dimensionSubDesc }}
        </div>
        <div v-for="l2Item of l1Item.subDimensionScoreList" :key="l2Item.encryptDimensionId" class="level-two-dimension">
            <div class="level-two-dimension-top">
                <div class="level-two-dimension-name">
                    {{ l2Item.dimensionName }}
                </div>
                <div class="level-two-dimension-desc-wrap" style="justify-content: flex-end">
                    <div
                        class="level-two-dimension-desc"
                        :style="{
                            'color': staticConfig[l1Index % staticConfig.length].color.fontColor,
                            'backgroundColor': staticConfig[l1Index % staticConfig.length].color.tagBgColor,
                            'text-align': 'right',
                        }"
                    >
                        {{ l2Item.leftDesc }}
                    </div>
                </div>
                <Progress
                    class="progress-wrap"
                    :splitArray="data.dimensionLevelArray"
                    :colorConfig="staticConfig[l1Index % staticConfig.length].color"
                    :value="l2Item.score"
                />
                <div class="level-two-dimension-desc-wrap" style="justify-content: flex-start">
                    <div
                        class="level-two-dimension-desc"
                        :style="{
                            'color': staticConfig[l1Index % staticConfig.length].color.fontColor,
                            'backgroundColor': staticConfig[l1Index % staticConfig.length].color.tagBgColor,
                            'text-align': 'left',
                        }"
                    >
                        {{ l2Item.rightDesc }}
                    </div>
                </div>
            </div>
            <div class="level-two-dimension-level-language">
                {{ l2Item.levelLanguage }}
            </div>
        </div>
    </template>
    <PageFooter :info="data" />

    <PageHeader />
    <SectionTitle title="关键潜在素质" />
    <div class="section-intro" style="margin-bottom: 12px">
        {{ data.potentialQualitiesKeyInt }}
    </div>
    <AttachTable :info="data.potentialQualitiesKeyList" :spCorp="data.spCorp" />
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import AttachTable from '../components/attach-table.vue'
import LevelOneDimension from '../components/level-one-dimension.vue'
import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import Progress from '../components/progress.vue'
import SectionTitle from '../components/section-title.vue'

defineOptions({
    name: 'DetailResult',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    staticConfig: {
        type: Array,
        default: () => [],
    },
    moduleCodes: {
        type: Array,
        default: () => ([]),
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
</script>

<style lang="less" scoped>
// .level-one-dimension {
//     position: relative;
//     padding: 22px 24px 20px 43px;
//     border-radius: 8px;
//     .bg {
//         width: 68px;
//         height: 68px;
//         position: absolute;
//         top: 4px;
//         left: 0;
//         background-repeat: no-repeat;
//         background-size: contain;
//     }
//     .level-one-title-wrap {
//         display: flex;
//         position: relative;
//         z-index: 1;
//         margin-bottom: 12px;
//         & > * {
//             flex-shrink: 0;
//         }
//         .level-one-dimension-name {
//             font-family: var(--title-sub-font-family);
//             font-size: 15px;
//             line-height: 32px;
//             margin-right: 18px;
//         }
//         .level-one-dimension-score {
//             display: flex;
//             align-items: center;
//             .label {
//                 margin-right: 4px;
//                 font-size: 13px;
//                 line-height: 26px;
//             }
//             .number {
//                 font-size: 22px;
//                 font-family: kanzhun, sans-serif;
//                 line-height: 26px;
//                 word-break: keep-all;
//                 position: relative;
//                 top: -2px;
//             }
//         }
//     }
//     .level-one-dimension-introduction {
//         font-size: 13px;
//         line-height: 26px;
//     }
// }
.level-two-dimension-introduction {
    margin-top: 24px;
    font-size: 12px;
    color: #808080;
    line-height: 20px;
}
.level-two-dimension {
    padding: 29px 0 12px;
    border-bottom: 1px solid #ececec;
    & + .level-one-dimension {
        margin-top: 39px;
    }
    .level-two-dimension-top {
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        .level-two-dimension-name {
            font-family: var(--title-sub-font-family);
            flex-shrink: 0;
            width: 94px;
            margin-right: 36px;
            font-size: 15px;
            line-height: 26px;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        .level-two-dimension-desc-wrap {
            width: 60px;
            flex-shrink: 0;
            display: flex;
            .level-two-dimension-desc {
                padding: 4px 5px;
                font-size: 12px;
                line-height: 14px;
                border-radius: 4px;
            }
        }
        .progress-wrap {
            margin: 0 8px;
        }
    }
    .level-two-dimension-level-language {
        font-size: 13px;
        line-height: 26px;
    }
}
</style>
