export function headerFooterConfig(currentCode: string, moduleCodes: string[]): { header: boolean, footer: boolean } {
    const currentIndex = moduleCodes.findIndex(x => x == currentCode)
    const result = {
        header: true,
        footer: true,
    }
    if (currentCode === '10') {
        if (moduleCodes[currentIndex - 1] == '1' || currentIndex === 0) {
            result.header = true
        } else {
            result.header = false
        }
    } else {
        if (moduleCodes[currentIndex + 1] == '10') {
            result.footer = false
        }
    }
    return result
}
