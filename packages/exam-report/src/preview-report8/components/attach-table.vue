<template>
    <div class="attach-table-header">
        <div class="td">
            {{ thead[0] }}
        </div>
        <div class="td">
            {{ thead[1] }}
        </div>
        <div class="td">
            <div class="tick-wrap">
                <div
                    v-for="(item, index) of splitArray"
                    :key="index"
                    class="tick-item"
                    :data-number="item"
                />
            </div>
        </div>
    </div>
    <div
        v-for="(item, index) of info"
        :key="index"
        class="attach-table-row"
        :class="`${index % 2 === 0 ? 'attach-table-row-odd' : 'attach-table-row-even'}`"
    >
        <div class="td">
            {{ item.name }}
        </div>
        <div class="td">
            {{ item.description }}
        </div>
        <div class="td">
            <div class="progress-wrap">
                <div class="progress-start" />
                <div class="progress-bar" :style="{ width: `${(item.score) * 15}px`, backgroundColor: colorScoreMap[item.score] }" />
                <div class="progress-number">
                    {{ item.score }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

defineOptions({
    name: 'AttachTable',
})
const props = defineProps({
    info: {
        type: Array<any>,
        default: () => [],
    },
    spCorp: {
        type: Number,
        default: 0,
    },
})
const isSpCorp = computed(() => props.spCorp === 0)

const thead = computed(() => {
    return isSpCorp.value ? ['潜在素质', '典型特征'] : ['潜在素质', '高分特征']
})
const splitArray = computed(() => {
    return isSpCorp.value ? [2, 4, 6, 8, 10, 12] : [2, 4, 6, 8, 10]
})

const colorScoreMap = {
    1: '#B9EAFE',
    2: '#A3E3FE',
    3: '#8AD9FD',
    4: '#79D4FD',
    5: '#5BC4FC',
    6: '#47BDFC',
    7: '#2EABFC',
    8: '#14A1FC',
    9: '#0092FA',
    10: '#0086E6',
    11: '#0072CF',
    12: '#0069BF',
}

</script>

<style lang="less" scoped>
.attach-table-header {
    background-color: var(--b-table-header-background);
    display: flex;
    border-radius: var(--b-table-border-radius) var(--b-table-border-radius) 0 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    font-family: var(--title-sub-font-family);
    .td {
        line-height: 27px;
        padding: 8px;
        flex-shrink: 0;
        position: relative;
        & + .td {
            border-left: 1px solid var(--border-color);
        }
        &:nth-child(1) {
            width: 120px;
        }
        &:nth-child(2) {
            width: 300px;
        }
        &:nth-child(3) {
            flex-grow: 1;
        }
    }
}
.attach-table-row {
    display: flex;
    border: 1px solid var(--border-color);
    border-top: none;
    &.attach-table-row-even {
        background-color: var(--b-table-zebra-bg-color);
    }
    &[style] {
        border-radius: 0 0 var(--b-table-border-radius) var(--b-table-border-radius);
    }
    .td {
        padding: 12px;
        flex-shrink: 0;
        line-height: 24px;
        word-break: break-all;
        white-space: pre-wrap;
        display: flex;
        align-items: center;
        & + .td {
            border-left: 1px solid var(--border-color);
            padding-left: 20px;
            padding-right: 20px;
        }
        &:nth-child(1) {
            width: 120px;
            justify-content: center;
            font-family: var(--title-sub-font-family);
        }
        &:nth-child(2) {
            width: 300px;
        }
        &:nth-child(3) {
            flex-grow: 1;
        }
    }
}
.tick-wrap {
    display: flex;
    position: absolute;
    left: 25px;
    bottom: 7px;

    .tick-item {
        position: relative;
        width: 30px;
        height: 4px;
        border: 1px solid #ccc;
        border-top: none;

        &::before,
        &::after {
            font-size: 13px;
            line-height: 20px;
            position: absolute;
            bottom: 100%;
        }
        &:first-child::before {
            content: '0';
            left: 0;
            transform: translateX(-50%);
        }
        &::after {
            content: attr(data-number);
            right: 0;
            transform: translateX(50%);
        }

        & + .tick-item {
            border-left: none;
        }
    }
}
.progress-wrap {
    display: flex;
    align-items: center;
    margin-left: 4px;
    .progress-start {
        width: 1px;
        height: 40px;
        background-color: #ececec;
    }
    .progress-bar {
        height: 16px;
    }
    .progress-number {
        margin-left: 8px;
        font-size: 13px;
        line-height: 20px;
    }
}
</style>
