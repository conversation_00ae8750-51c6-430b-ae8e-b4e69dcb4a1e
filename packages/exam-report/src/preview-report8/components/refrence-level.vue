<template>
    <div class="refrence-level-wrap">
        <SubTitle class="refrence-level-title" title="报告参考性">
            <div class="level" v-if="info.indicativeResultDesc">{{ info.indicativeResultDesc }}</div>
        </SubTitle>
        <Table
            border
            :columns="[
                {
                    label: '指标',
                    field: 'showName',
                    width: 130,
                    align: 'center'
                },
                {
                    label: '结果',
                    field: 'result',
                    width: 130,
                    align: 'center'
                },
                {
                    label: '指标含义',
                    field: 'indicatorMeaning',
                    width: 250,
                    align: 'center'
                }
            ]"
            :tableData="info.indicativeList"
        >
            <template #td-indicatorMeaning="{ raw }">
                <div v-html="raw.indicatorMeaning"></div>
            </template>
        </Table>
        <div class="extra-desc">{{ info.indicativeIntroduction }}</div>
    </div>
</template>
<script setup lang="ts">
import SubTitle from './sub-title.vue';
import { Table } from '@boss/design';
import '@boss/design/es/table/style.js';

defineOptions({
    name: 'RefrenceLevel'
});
const props = defineProps({
    info: {
        type: Object,
        default: () => ({})
    }
});
</script>
<style lang="less" scoped>
.refrence-level-wrap {
    --b-table-body-font-color: #1f1f1f;
    --b-table-header-font-color: #1f1f1f;
    --b-table-header-font-size: 13px;
    --b-table-body-font-size: 13px;
    --b-table-header-line-height: 24px;
    --b-table-body-line-height: 24px;

    :deep(.b-table) {
        &::before {
            display: none;
        }
        .b-table-container {
            .b-table-element {
                border-bottom-left-radius: var(--b-table-border-radius);
                border-bottom-right-radius: var(--b-table-border-radius);
                overflow: hidden;
            }
        }
        .b-table-th {
            height: 43px;
            padding: 11px 8px 8px;
            .b-table-th-wraper {
                justify-content: center;
            }
        }
        .b-table-td {
            height: 48px;
        }
        .b-table-tbody {
            .b-table-cell {
                text-align: center;
                &::before {
                    border-left: 1px solid var(--border-color);
                    border-bottom: 1px solid var(--border-color);
                }
                &:last-child {
                    &::before {
                        border-right: 1px solid var(--border-color);
                    }
                }
            }
            .b-table-tr:last-child {
                .b-table-cell:first-child {
                    &::before {
                        border-bottom-left-radius: var(--b-table-border-radius);
                    }
                }
                .b-table-cell:last-child {
                    &::before {
                        border-bottom-right-radius: var(--b-table-border-radius);
                    }
                }
            }
            .b-table-tr:nth-child(even),
            .b-table-tr:nth-child(even):hover {
                --b-table-background-color: var(--b-table-zebra-bg-color);
                --b-table-row-hover-background: var(--b-table-zebra-bg-color) !important;
            }
            .b-table-tr:hover {
                --b-table-row-hover-background: #fff !important;
            }
        }
    }
    .refrence-level-title {
        margin-bottom: 20px;
    }
    .extra-desc {
        margin-top: 16px;
    }
}
</style>
