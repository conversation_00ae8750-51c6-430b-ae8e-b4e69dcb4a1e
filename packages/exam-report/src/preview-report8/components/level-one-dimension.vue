<template>
    <div
        class="level-one-dimension"
        :class="{'flex': showType === 1}"
        :style="{ backgroundImage: `linear-gradient(to right, ${staticConfig[index % staticConfig.length].color.gradientStart} 0%, #fff 100%)` }"
    >
        <div class="level-one-title-wrap">
            <div class="bg" :style="{ backgroundImage: `url('${staticConfig[index % staticConfig.length].img}')` }"></div>
            <div class="title-info-wrap">
                <div class="level-one-dimension-name">{{ data.dimensionName }}</div>
                <div v-if="score" class="level-one-dimension-score">
                    <span class="label">得分</span><span class="number" :style="{ color: staticConfig[index % staticConfig.length].color.fontColor }">{{ score }}</span>
                </div>
            </div>
        </div>
        <div class="level-one-dimension-introduction">{{ data.dimensionDetailInt || data.description }}</div>
    </div>
</template>
<script setup lang="ts">
import { staticConfig } from '../components-atom/const';
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    index: {
        type: Number,
        default: 0,
    },
    score: {
        type: String,
        default: '',
    },
    showType: {
        type: Number,
        default: 0,
    }
});
</script>
<style lang="less" scoped>
.level-one-dimension {
    position: relative;
    padding: 22px 24px 20px 43px;
    border-radius: 8px;

    &.flex {
        display: flex;

        .level-one-title-wrap {
            margin-right: 45px;
        }
    }

    .level-one-title-wrap {
        .bg {
            width: 68px;
            height: 68px;
            position: absolute;
            top: 4px;
            left: 0;
            background-repeat: no-repeat;
            background-size: contain;
        }

        .title-info-wrap {
            display: flex;
            position: relative;
            z-index: 1;
            margin-bottom: 12px;
            & > * {
                flex-shrink: 0;
            }
            .level-one-dimension-name {
                font-family: var(--title-sub-font-family);
                font-size: 15px;
                line-height: 32px;
                margin-right: 18px;
            }
            .level-one-dimension-score {
                display: flex;
                align-items: center;
                .label {
                    margin-right: 4px;
                    font-size: 13px;
                    line-height: 26px;
                }
                .number {
                    font-size: 22px;
                    font-family: kanzhun, sans-serif;
                    line-height: 26px;
                    word-break: keep-all;
                    position: relative;
                    top: -2px;
                }
            }
        }
    }

    
    .level-one-dimension-introduction {
        font-size: 13px;
        line-height: 26px;
    }
}
</style>