<template>
    <div class="wrap">
        <div class="content">
            <div class="field-wrap" v-if="info.examineeInfoList?.find((x) => x.fieldName === '姓名')">
                <div class="field-label">{{ info.examineeInfoList?.find((x) => x.fieldName === '姓名')?.fieldShowName }}：</div>
                <div class="field-value">{{ info.showName }}</div>
            </div>
            <div class="field-wrap" v-if="info.examineeInfoList?.find((x) => x.fieldName === '第三方唯一码')">
                <div class="field-label">{{ info.examineeInfoList?.find((x) => x.fieldName === '第三方唯一码')?.fieldShowName }}：</div>
                <div class="field-value">{{ info.reportNum }}</div>
            </div>
            <div class="field-wrap">
                <div class="field-label">位于：</div>
                <div class="field-value" :style="{ color: map[`${info.riskFactorScore}.${info.positiveFactorScore}`]?.color }">
                    {{ map[`${info.riskFactorScore}.${info.positiveFactorScore}`]?.desc }}
                </div>
            </div>
            <div class="field-wrap">
                <div class="field-label">风险因子：</div>
                <div class="field-value">
                    {{ info.weaknessNameList?.length > 0 ? info.weaknessNameList.join('、') : '不存在⻛险项，⽆需特别关注' }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'ResultMain'
});
const props = defineProps({
    info: {
        type: Object,
        default: () => ({})
    },
    map: {
        type: Object,
        default: () => ({})
    }
});
</script>
<style lang="less" scoped>
.wrap {
    background: #f7f7f7;
    border-radius: 5px;
    padding: 12px;
    .content {
        background: #fff;
        border-radius: 5px;
        border: 1px solid var(--border-color);
        padding: 15px 20px;
        .field-wrap {
            & + .field-wrap {
                margin-top: 4px;
            }

            display: flex;
            align-items: center;
            font-size: 13px;
            color: #1f1f1f;
            line-height: 26px;
            .field-label {
                flex-shrink: 0;
            }
            .field-value {
                flex-shrink: 0;
            }
        }
    }
}
</style>
