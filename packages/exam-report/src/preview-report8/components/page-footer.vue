<template>
    <div class="page-footer-wrap">
        <div class="footer-left">
            <Split v-if="itemName">{{ itemName?.fieldValue }}</Split>
            <Split v-if="itemCode">{{ itemCode?.fieldValue }}</Split>
            <Split v-if="itemJob">{{ itemJob?.fieldValue }}</Split>
            <Split class="report-name-filed">{{ info.templateName }}</Split>
            <Split v-if="itemRef">{{ itemRef.fieldShowName }}：{{ itemRef?.fieldValue }}</Split>
        </div>
        <div class="page-count">1</div>
    </div>
</template>
<script setup lang="ts">

import { Split } from '@boss/design';
import '@boss/design/es/split/style.js';

defineOptions({
    name: 'PageFooter'
});
const props = defineProps({
    info: {
        type: Object,
        default: () => ({})
    }
});

const itemName = computed(() => props.info.examineeInfoList.find((x) => x.fieldName === '姓名'));
const itemCode = computed(() => props.info.examineeInfoList.find((x) => x.fieldName === '第三方唯一码'));
const itemJob = computed(() => props.info.examineeInfoList.find((x) => x.fieldName === '应聘职位'));
const itemRef = computed(() => props.info.examineeInfoList.find((x) => x.fieldName === '报告参考性'));
</script>
