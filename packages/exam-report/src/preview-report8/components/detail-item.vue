<template>
    <div class="detail-item-wrap">
        <div class="left">
            <div class="dimension">
                <div class="dimension-name">{{ info.dimensionName }}</div>
                <div :class="['dimension-score', `dimension-score-${level}`]">{{ info.score }}</div>
            </div>
            <div :class="['progress', `progress-${level}`]">
                <div class="total-bar">
                    <div class="content-bar" :style="{ width: `${(info.score / 10) * 100}%` }"></div>
                    <div class="tick" v-for="item of tickPercent" :key="item" :style="{ left: item }"></div>
                </div>
            </div>
        </div>
        <div class="right">{{ info.levelLanguage }}</div>
    </div>
</template>
<script setup lang="ts">

defineOptions({
    name: 'DetailItem'
});
const props = defineProps({
    info: { type: Object, default: () => ({}) },
    dimensionLevelList: { type: Array, default: () => [] },
    type: { type: String, defalut: 'negitive' }
});
const levelMap = {
    1: 'low',
    2: 'middle',
    4: 'high'
};
const level = computed(() => {
    if (props.type === 'negitive') {
        if (props.info.score >= 9) {
            return 'low';
        } else {
            return 'high';
        }
    } else if (props.type === 'positive') {
        if (props.info.score <= 4) {
            return 'low';
        } else {
            return 'high';
        }
    } else { 
        return 'high';
    }
});
const tickPercent = computed(() => {
    let array: any = [];
    if (props.type === 'positive') {
        array = [{ startScore: 4 }];
    } else if (props.type === 'negitive') {
        array = [{ startScore: 9 }];
    }
    return array.map((x: any) => `${(x.startScore / 10) * 100}%`);
});
</script>
<style lang="less" scoped>
.detail-item-wrap {
    --color-score-high: #00a6a7;
    --color-score-middle: #00a4f2;
    --color-score-low: #e64951;
    --bg-totalbar-high: #ddf5f5;
    --bg-totalbar-middle: #e1f4fc;
    --bg-totalbar-low: #ffeae8;
    --bg-contentbar-high: linear-gradient(90deg, #cef6f6 0%, #00bebd 100%);
    --bg-contentbar-middle: linear-gradient(90deg, #e1f4fc 0%, #0db1ff 100%);
    --bg-contentbar-low: linear-gradient(90deg, #ffe9ea 0%, #e64951 100%);

    margin-top: 16px;
    display: flex;
    align-items: center;
    // border-bottom: 1px solid var(--border-color);
    .left {
        width: 220px;
        margin-right: 20px;
        flex-shrink: 0;
        .dimension {
            margin-bottom: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            .dimension-name {
                font-family: var(--title-sub-font-family);
                word-break: break-all;
                white-space: pre-wrap;
                line-height: 20px;
                font-size: 13px;
            }
            .dimension-score {
                width: 40px;
                flex-shrink: 0;
                text-align: right;
                font-family: var(--number-font-family);
                font-size: 16px;
                line-height: 18px;
                &.dimension-score-high {
                    color: var(--color-score-high);
                }
                &.dimension-score-middle {
                    color: var(--color-score-middle);
                }
                &.dimension-score-low {
                    color: var(--color-score-low);
                }
            }
        }
        .progress {
            height: 8px;
            .total-bar {
                height: 100%;
                border-radius: 2px;
                overflow: hidden;
                position: relative;
                .content-bar {
                    border-radius: 3px;
                    height: 100%;
                }
                .tick {
                    position: absolute;
                    width: 1px;
                    height: 100%;
                    background-color: #fff;
                    top: 0;
                }
            }
            &.progress-high {
                .total-bar {
                    background-color: var(--bg-totalbar-high);
                    .content-bar {
                        background-image: var(--bg-contentbar-high);
                    }
                }
            }
            &.progress-middle {
                .total-bar {
                    background-color: var(--bg-totalbar-middle);
                    .content-bar {
                        background-image: var(--bg-contentbar-middle);
                    }
                }
            }
            &.progress-low {
                .total-bar {
                    background-color: var(--bg-totalbar-low);
                    .content-bar {
                        background-image: var(--bg-contentbar-low);
                    }
                }
            }
        }
    }
    .right {
        word-break: break-all;
        white-space: pre-wrap;
        line-height: 20px;
        min-height: 40px;
        display: flex;
        align-items: center;
    }
}
</style>
