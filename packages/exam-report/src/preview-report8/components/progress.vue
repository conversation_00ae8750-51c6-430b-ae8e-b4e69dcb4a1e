<template>
    <div class="wrap">
        <div
            :class="['bar-item', { last: index === barArray.length - 1 }, { first: index === 0 }]"
            v-for="(item, index) of barArray"
            :key="index"
            :style="{ width: `${item.width}px`, backgroundColor: colorConfig[`progress_${index + 1}`] }"
            :data-step="item.step"
        ></div>
        <div class="value" :style="{ left: `${valueLeftPosition}px` }">
            <div class="number">{{ value }}</div>
            <div class="indicator"></div>
        </div>
    </div>
</template>
<script setup lang="ts">

const props = defineProps({
    splitArray: {
        type: Array,
        default: () => []
    },
    colorConfig: {
        type: Object,
        default: () => ({})
    },
    value: {
        type: Number,
        default: 0
    }
});
const totalWidth = 400;
const minPartWidth = 35;
const splitArrayLocal = computed<any[]>(() => [0, ...props.splitArray, 100]);
const barArray = computed(() => {
    let result: any = [];
    for (let i = 1; i < splitArrayLocal.value.length; i++) {
        const length = splitArrayLocal.value[i] - splitArrayLocal.value[i - 1];
        const stepWidth = 4;
        result.push({
            step: splitArrayLocal.value[i],
            length,
            stepWidth,
            width: length * stepWidth,
            rank: i
        });
    }
    if (result.some((x: any) => x.width < minPartWidth)) {
        result.sort((a: any, b: any) => a.width - b.width);
        let avaliableWidth = totalWidth;
        for (let i = 0; i < result.length; i++) {
            const item = result[i];
            if (item.width < minPartWidth) {
                item.width = minPartWidth;
                item.stepWidth = minPartWidth / item.length;
            } else {
                const rest = result.slice(i);
                const remainLengthTotal = rest.reduce((prev: any, cur: any) => prev + cur.length, 0);
                item.width = (item.length / remainLengthTotal) * avaliableWidth < minPartWidth ? minPartWidth : (item.length / remainLengthTotal) * avaliableWidth;
                item.stepWidth = item.width / item.length;
            }
            avaliableWidth -= item.width;
        }
    }
    result.sort((a: any, b: any) => a.rank - b.rank);
    return result;
});
const valueLeftPosition = computed(() => {
    let result = 0;
    for (let i = 0; i < barArray.value.length; i++) {
        if (Number(props.value) >= barArray.value[i].step) {
            result += barArray.value[i].width;
        } else {
            if (i === 0) {
                result += props.value * barArray.value[i].stepWidth;
            } else {
                result += (props.value - barArray.value[i - 1].step) * barArray.value[i].stepWidth;
            }
            break;
        }
    }
    return result;
});
</script>
<style lang="less" scoped>
.wrap {
    display: inline-flex;
    height: 10px;
    position: relative;
    .bar-item {
        flex-grow: 0;
        flex-shrink: 0;
        height: 100%;
        position: relative;
        &::before,
        &::after {
            position: absolute;
            top: calc(100% + 6px);
            font-size: 12px;
            color: #9fa6b5;
            line-height: 18px;
        }
        &::after {
            content: attr(data-step);
            right: 0;
            transform: translateX(50%);
        }
        &.first {
            border-radius: 4px 0 0 4px;
            &::before {
                content: '0';
                left: 0;
            }
        }
        &.last {
            border-radius: 0 2px 2px 0;
            &::after {
                transform: none;
            }
        }
    }
    .value {
        position: absolute;
        display: flex;
        justify-content: center;
        flex-direction: column;
        align-items: center;
        bottom: 0;
        transform: translate(-50%, -7px);
        .number {
            font-size: 14px;
            font-family: kanzhun, sans-serif;
            color: #363f55;
            line-height: 17px;
            margin-bottom: 3px;
            word-break: keep-all;
        }
        .indicator {
            width: 12px;
            height: 10px;
            background-image: url('https://img.bosszhipin.com/static/file/2024/f28p6xlua41709608515804.png.webp');
            background-repeat: no-repeat;
            background-size: contain;
        }
    }
}
</style>
