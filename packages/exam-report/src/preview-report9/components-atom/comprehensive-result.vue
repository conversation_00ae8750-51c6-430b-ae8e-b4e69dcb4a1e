<template>
    <PageHeader></PageHeader>
    <SectionTitle title="Part2：团队参测情况" style="margin-bottom: 22px"></SectionTitle>
    <div class="intro">
        总体参测人数<span class="number">{{ data.teamInfo.realJoinCount }}</span
        >人（实到人数），占团队总人数的百分比<span class="number">{{ data.teamInfo.joinRate }}%</span>（实到人数/应到总人数*100%）。
    </div>
    <div class="chart-row" v-for="rowItem of pieChartRenderData">
        <div class="single-chart-container" v-for="colItem of rowItem">
            <PieChart :data="colItem"></PieChart>
        </div>
    </div>
    <div class="chart-part-title" data-chart-index="true">图1：团队各类人群属性分布</div>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">

import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import PieChart from '../components/pie-chart.vue';

defineOptions({
    name: 'ComprehensiveResult',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    staticConfig: {
        type: Array,
        default: () => [],
    },
});
const pieChartRenderData = computed(() => {
    let result = [];
    for (let i = 0; i < props.data.teamStatInfo.length; i += 2) {
        result.push(props.data.teamStatInfo.slice(i, i + 2));
    }
    return result;
});
</script>
<style lang="less" scoped>
.intro {
    font-size: 13px;
    line-height: 26px;
    margin-bottom: 11px;
    .number {
        font-family: var(--Kanzhun);
        font-size: 18px;
        line-height: 26px;
    }
}
.chart-row {
    display: flex;
    justify-content: space-between;
    & + .chart-row {
        margin-top: 16px;
    }
}
.single-chart-container {
    width: 337px;
    // height: 300px;
    border: 1px solid #11a1fc;
    display: flex;
    justify-content: center;
}
.chart-part-title {
    margin-top: 10px;
    text-align: center;
    color: #000;
    font-size: 12px;
    line-height: 24px;
}
</style>
