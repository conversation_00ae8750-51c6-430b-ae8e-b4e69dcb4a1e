<template>
    <ComprehensiveResult :data="data"></ComprehensiveResult>
    <PageHeader></PageHeader>
    <SectionTitle title="Part3：团队重点结果"></SectionTitle>
    <RichText :domString="data.importantResultIntr" :richTextIndex="2" />
    <div style="display: flex; align-items: center; margin-top: 34px; margin-bottom: 22px">
        <p class="sub-title" style="margin-bottom: 0; margin-right: 10px">团队职业心理健康指数</p>
        <p class="desc-font desc-font-flex">
            <span class="highlight-number">{{ data.teamImportantInfo.score }}</span>
            <LevelIcon :level="data.teamImportantInfo.level"></LevelIcon>
        </p>
    </div>
    <Bar
        :legends="['团队', '常模']"
        :color="['#9B6DBF', '#F6BD2E']"
        :data="[
            { label: '团\n队', value: Number(data.teamImportantInfo.score) },
            { label: '常\n模', value: Number(data.teamImportantInfo.normalScore) },
        ]"
    ></Bar>
    <div class="desc-font" style="margin-top: 22px; margin-bottom: 11px">
        <p>
            【指数范围：最低
            <span class="highlight-number" style="color: #de952f">{{ data.teamImportantInfo.minScore }}</span>
            ~最高
            <span class="highlight-number">{{ data.teamImportantInfo.maxScore }}</span
            >】
        </p>
        <p>
            团队职业心理健康指数为
            <span class="highlight-number" style="color: #9b6dbf">{{ data.teamImportantInfo.score }}</span>
            ，职业心理健康指数常模为
            <span class="highlight-number" style="color: #f6bd2e">{{ data.teamImportantInfo.normalScore }}</span>
        </p>
        <p>
            <span>本团队此指数 </span>
            <span v-if="data.teamImportantInfo.score > data.teamImportantInfo.normalScore">高</span>
            <span v-if="data.teamImportantInfo.score === data.teamImportantInfo.normalScore">中</span>
            <span v-if="data.teamImportantInfo.score < data.teamImportantInfo.normalScore">低</span>
            <span>于常模，其职业心理健康指数属于 </span>
            <span>{{ levelStrMaps[`${data.teamImportantInfo.level}`] }}</span>
            <span>水平。</span>
        </p>
        <p style="background: #f1f9ff; border-radius: 10px; padding: 10px; margin-top: 7px">{{ data.teamImportantInfo.levelLanguage }}</p>
    </div>
    <div class="g2-seize" style="height: 250px; margin-top: 60px; outline: none">
        <!-- 数据：{{ data.teamImportantInfo.teamLevelStat }} -->
        <GradeLevelDistribution title="图2  团队职业心理健康指数各等级水平分布" :data="data.teamImportantInfo.teamLevelStat"> </GradeLevelDistribution>
    </div>
    <!-- TUDO: teamLevelStat 字段遍历匹配 -->
    <p class="desc-font" style="margin-top: 38px">
        团队职业心理健康指数各等级水平分布为，
        <template v-for="item in data.teamImportantInfo.teamLevelStat" :key="item.name">
            <span>{{ item.name }}水平</span>
            <span class="highlight-number" :style="{ color: teamImportantInfoTextColorConfig[item.name] }">{{ item.count }}</span
            >人， 占比为<span class="highlight-number" :style="{ color: teamImportantInfoTextColorConfig[item.name] }">{{ item.percent }}%</span>。
        </template>
    </p>

    <PageFooter :info="data"></PageFooter>
    <PageHeader></PageHeader>
    <p class="sub-title" style="margin-top: 35px">团队核心测验结果</p>
    <RichText :domString="data.coreResultIntr" :richTextIndex="3" />
    <div style="padding-top: 22px">
        <p style="margin-bottom: 22px" class="desc-font">{{ initDataSource(data.coreTestStat).length }}个核心测验结果情况与相应的常模的比较，呈现与如下图：</p>
        <GroupBar
            :legends="['团队整体指数', '常模指数']"
            :color="['#9B6DBF', '#F6BD2E']"
            title="图2  团队整体指数与常模指数对比"
            :dataSource="initDataSource(data.coreTestStat)"
        ></GroupBar>
    </div>

    <template v-for="(item, index) in data.coreTestStat" :key="item.name">
        <QuizDetailChart :index="`${index + 1}. `" :data="item" titleLevel="3">
            <!-- <template v-if="index < 1" #title>
                <p class="desc-font" style="margin-bottom: 34px;">每个核心测验的详细结果，展示如下：</p>
            </template> -->
        </QuizDetailChart>
        <template v-if="item.subDetailInfo && item.subDetailInfo.length" v-for="(obj, i) in item.subDetailInfo" :key="obj.name">
            <div class="sub-quiz-detail-chart">
                <QuizDetailChart :index="`${index + 1}. ${i + 1} `" :data="obj" titleLevel="4"></QuizDetailChart>
            </div>
        </template>
    </template>

    <PageFooter :info="data"></PageFooter>
    <PageHeader></PageHeader>

    <div class="desc-font">
        <p>团队成员对组织的满意度从低到高排序：</p>
        <p>{{ data.workSatisfactionTag }}</p>
    </div>
    <p class="sub-title" style="margin-top: 23px">团队各类人群差异情况</p>
    <p class="desc-font" style="margin-bottom: 23px">
        <span>关于团队职业心理健康、核心测验结果在各类人群中差异情况的详细解读，共包括{{ data.teamDiffDesc.length }}部分，即</span>
        <template v-for="(item, index) in data.teamDiffDesc" :key="item">
            <span>{{ item.key }}差异情况</span>
            <span v-if="index < data.teamDiffDesc.length - 1">{{ index < data.teamDiffDesc.length - 2 ? '、' : '和' }}</span>
        </template>
        <span>。具体如下：</span>
    </p>
    <template v-for="(item, index) in data.teamDiffDesc" :key="item.key">
        <TeamMentalHealthResult :data="item"></TeamMentalHealthResult>
        <template v-if="index < data.teamDiffDesc.length - 1">
            <PageFooter :info="data"></PageFooter>
            <PageHeader></PageHeader>
        </template>
    </template>

    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">
import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import QuizDetailChart from '../components/quiz-detail-chart.vue';
import TeamMentalHealthResult from '../components/team-mental-health-result.vue';
import GroupBar from '../components/group-bar.vue';
import Bar from '../components/bar.vue';
import GradeLevelDistribution from '../components/grade-level-distribution.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';
import ComprehensiveResult from './comprehensive-result.vue';
import LevelIcon from '../components/level-icon.vue';
const teamImportantInfoTextColorConfig: any = {
    低: '#DE952F',
    中: '#49C7C7',
    高: '#0092FA',
};

const levelStrMap: any = {
    '1': '低',
    '2': '等',
    '4': '高',
};

const levelStrMaps: any = {
    '1': '低',
    '2': '中',
    '4': '高',
};

defineOptions({
    name: 'DetailResult',
    inheritAttrs: false,
});

type Iitem = {
    name: string;
    score: string;
    normalScore: string;
};

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    staticConfig: {
        type: Array,
        default: () => [],
    },
});

const initDataSource = (list: []) => {
    return [
        ...list.map((item: Iitem) => ({
            label: item.name,
            children: [
                { type: '团队整体指数', value: Number(item.score).toFixed(2) },
                { type: '常模指数', value: Number(item.normalScore).toFixed(2) },
            ],
        })),
    ];
};
</script>
<style lang="less" scoped>
.desc-font-flex {
    display: flex;
    align-items: center;

    .highlight-number {
        margin-right: 10px;
    }
}
.sub-quiz-detail-chart {
    margin-top: 16px;
    padding: 12px 12px 12px;
    background: #f6f6f6;
    :deep(.desc-tag-wrap) {
        margin: 6px 0 6px 0;
        padding: 6px;
    }

    & + .sub-quiz-detail-chart {
        margin-top: 0;
    }

    .quiz-detail-chart-wrap:first-child {
        margin-top: 0 !important;
    }
}

.level-two-dimension-introduction {
    margin-top: 24px;
    font-size: 12px;
    color: #808080;
    line-height: 20px;
}
.level-two-dimension {
    padding: 29px 0 12px;
    border-bottom: 1px solid #ececec;
    & + .level-one-dimension {
        margin-top: 39px;
    }
    .level-two-dimension-top {
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        .level-two-dimension-name {
            font-family: var(--title-sub-font-family);
            flex-shrink: 0;
            width: 94px;
            margin-right: 36px;
            font-size: 15px;
            line-height: 26px;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        .level-two-dimension-desc-wrap {
            width: 60px;
            flex-shrink: 0;
            display: flex;
            .level-two-dimension-desc {
                padding: 4px 5px;
                font-size: 12px;
                line-height: 14px;
                border-radius: 4px;
            }
        }
        .progress-wrap {
            margin: 0 8px;
        }
    }
    .level-two-dimension-level-language {
        font-size: 13px;
        line-height: 26px;
    }
}
</style>
