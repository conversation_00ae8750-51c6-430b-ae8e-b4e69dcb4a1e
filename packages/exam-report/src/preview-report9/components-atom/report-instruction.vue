<template>
    <PageHeader></PageHeader>
    <SectionTitle title="团队测评信息概要" style="margin-bottom: 34px"></SectionTitle>
    <div class="team-info-wrap">
        <div class="row-wrap">
            <div class="col-item">团队名称</div>
            <div class="col-item">{{ data.teamInfo.teamName }}</div>
        </div>
        <div class="row-wrap">
            <div class="col-item">团队实际人数</div>
            <div class="col-item">{{ data.teamInfo.needJoinCount }}</div>
        </div>
        <div class="row-wrap">
            <div class="col-item">团队参测人数</div>
            <div class="col-item">{{ data.teamInfo.realJoinCount }}</div>
        </div>
        <div class="row-wrap">
            <div class="col-item">团队参测占比</div>
            <div class="col-item">{{ data.teamInfo.joinRate }}%</div>
        </div>
    </div>
    <SectionTitle title="报告阅读结构概览" style="margin-bottom: 34px"></SectionTitle>
    <div class="read-img"><img :src="data.readImage" alt="" /></div>
    <PageFooter :info="data"></PageFooter>

    <PageHeader></PageHeader>
    <SectionTitle title="Part1：测评介绍" data-has-attendants="1"></SectionTitle>
    <RichText :domString="data.testDescription" :richTextIndex="1"></RichText>
    <div class="normal-content attendant-footer" data-attendants-belong-to="1">{{ data.testDescriptionFooter }}</div>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">
import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';

defineOptions({
    name: 'ReportInstruction',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});
</script>
<style lang="less" scoped>
.team-info-wrap {
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    line-height: 26px;
    margin-bottom: 50px;
    .row-wrap {
        display: flex;
        border: 1.33px solid #ececec;
        & + .row-wrap {
            border-top: none;
        }
        &:last-child {
            border-radius: 0px 0px 6px 6px;
        }
        .col-item {
            &:first-child {
                width: 155px;
                flex-grow: 0;
                padding-left: 30px;
                border-left: none;
            }
            flex-grow: 1;
            padding: 11px 0 11px 40px;
            border-left: 1px solid #ececec;
        }
    }
}
.read-img {
    display: flex;
    justify-content: center;
    img {
        max-width: 690px;
        height: 114px;
    }
}
.attendant-footer {
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 321px;
        border-top: 1px solid #979797;
    }
}
[data-attendants-belong-to] {
    position: absolute;
    padding-top: 6px;
    padding-left: 6px;
    font-size: 12px;
    color: #808080;
    line-height: 26px;
}
</style>
