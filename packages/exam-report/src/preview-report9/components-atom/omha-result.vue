<template>
    <PageHeader></PageHeader>
    <SectionTitle title="Part4：模型详细结果"></SectionTitle>
    <p class="desc-font">
        <span>OMHA模型从</span>
        <span>{{allDimensionData.classStr.join('、')}}这</span>
        <span>{{ allDimensionData.classStr.length }}个方面刻画心理健康，</span>
        <span>共计{{allDimensionData.length}}个维度。</span>
    </p>
    <OmhaChart :data="data.allDimensionScore"></OmhaChart>
    <p class="text-tit-sub" style="margin-top: 34px;" data-title-level="2" data-title-text="模型详细结果分析">模型详细结果分析：</p>
    <template v-for="item in data.teamDetailInfo" :key="item.name">
        <template v-for="(obj, index) in item.subDetailInfo" :key="obj.name">
            <TargetExplain :title="index < 1 ? {name: item.name, desc: item.desc }: {}" :info="item" :data="obj"></TargetExplain>
        </template>
    </template>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">

import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import TargetExplain from '../components/target-explain.vue';
import OmhaChart from '../components/omha-chart.vue';

defineOptions({
    name: 'InterviewAdvise',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});

interface IallDimensionData {
    length: number;
    classStr: string[];
}

const allDimensionData = ref<IallDimensionData>({
    classStr: [],
    length: 0,
});

const allDimensionScoreInit = (data: { subDimensionScore: any; }[]) => {
    const newData = <IallDimensionData>{
        classStr: [],
        length: 0,
    };

    let list = [...data[0].subDimensionScore, ...data[1].subDimensionScore];

    list.forEach((item:{ name: string, subDimensionScore: Array<any> }) => {
        if (!newData.classStr.includes(item.name)) {
            newData.classStr.push(item.name);
        }
        newData.length += item.subDimensionScore.length;
    });
    allDimensionData.value = newData;
    return newData;
};

onMounted(() => {
    allDimensionScoreInit(props.data.allDimensionScore);
});

</script>
<style lang="less">
.job-matching-explain {
    margin-top: 24px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    // text-indent: 1em;
    white-space: pre-wrap;
}
.job-matching-title {
    margin-top: 34px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    text-align: center;
}

.result-tit {
    margin-top: 22px;
    display: flex;
    align-items: end;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    .level-tag {
        margin: 0 14px;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: #cce9fe;
        border: 1px solid rgba(0, 146, 250, 0.37);
        text-align: center;
        line-height: 32px;
        color: #0092fa;
        font-family: var(--FZLanTingHeiS-B-GB);
        font-size: 22px;
        font-style: normal;
        font-weight: 400;
    }

    .number {
        color: #0092fa;
    }
}

.result-img {
    margin: 22px auto 10px;
    width: 690px;
    height: 180px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.result-desc {
    color: #808080;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.character-feature-describe {
    margin-top: 34px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.character-feature-item {
    margin-top: 10px;
}
</style>
