<template>
    <PageHeader></PageHeader>
    <SectionTitle title="附录" style="margin-bottom: 32px"></SectionTitle>
    <SubTitle title="模型各维度定义" style="margin-bottom: 12px" data-module-index="6"></SubTitle>
    <AttachTableAttachSelf :info="data.dimensionInfoNewList"></AttachTableAttachSelf>
    <SubTitle title="关于本报告" style="margin-top: 34px; margin-bottom: 19px" data-module-index="6"></SubTitle>
    <RichText :domString="data.aboutThisReport" :richTextIndex="6"></RichText>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">
import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import SubTitle from '../components/sub-title.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';
import AttachTableAttachSelf from '../components/attach-table-attach-self.vue';

defineOptions({
    name: 'Attach',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});
</script>
