<template>
    <div
        class="level"
        v-if="level"
        :style="{
            color: levelMap[`${level}`].fontColor,
            backgroundColor: levelMap[`${level}`].bgColor,
            borderColor: levelMap[`${level}`].borderColor,
        }"
    >
        {{ levelMap[`${level}`].font }}
    </div>
</template>
<script setup lang="ts">
const props = defineProps({
    level: {
        type: Number,
        default: () => (1),
    },
});
const levelMap: any = {
    '1': {
        font: '低',
        fontColor: '#E7A13E',
        borderColor: 'rgba(231, 161, 62, 0.3)',
        bgColor: '#FAECD8',
        desc: '（报告结果，需谨慎参考）',
    },
    '2': {
        font: '中',
        fontColor: '#49C7C7',
        borderColor: 'rgba(0, 166, 167, 0.37)',
        bgColor: '#E5F9F9',
        desc: '（报告结果，可选择性参考使用）',
    },
    '4': {
        font: '高',
        fontColor: '#0092FA',
        borderColor: 'rgba(0, 146, 250, 0.37)',
        bgColor: '#CCE9FE',
        desc: '（报告结果，具有较高参考性）',
    },
};
</script>
<style lang="less" scoped>
.level {
    width: 32px;
    height: 32px;
    font-family: var(--title-main-font-family);
    font-size: 21px;
    line-height: 32px;
    padding: 0 5px;
    border-radius: 5px;
    border: 1px solid rgba(0, 166, 167, 0.37);
}
</style>
