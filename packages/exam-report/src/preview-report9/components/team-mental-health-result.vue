<template>
    <p class="text-tit" data-title-level="3">{{ data?.key }}差异情况</p>
    <p class="desc-font" style="margin-bottom: 22px">
        <span>{{ data.key }}差异情况，即团队的职业心理健康情况及核心测验结果在不同</span>
        <span>{{ data.key }}间差异情况的比较与分析，共区分</span>
        <span>{{ childrenClass.length - 1 }}个</span>
        <span>{{ data.key }}，</span>
        <span>即</span>
        <span>{{ childrenClass.slice(1, childrenClass.length).join('、') }}</span>
        <span>。本部分包含两方面，即{{ data.key }}差异在各指数的比较、{{ data.key }}差异在各等级水平的人数分布情况。 </span>
    </p>
    <p style="margin-bottom: 10px; color: #1f1f1f; font-family: var(--FZLanTingHeiS-DB-GB); font-size: 15px; line-height: 26px" data-title-level="4">{{ data?.key }}差异在各指数的比较与解读</p>
    <GroupBar :legends="childrenClass" :splitNum="4" :dataSource="groupBarChartList.slice(0, 1)"></GroupBar>
    <div style="margin: 10px 0">
        <GroupBar :legends="childrenClass" :splitNum="4" :dataSource="groupBarChartList.slice(1)" :title="`图12  ${data.key}差异在各指数的比较`"></GroupBar>
    </div>
    <template v-for="(obj, index) in data.featureStat" :key="obj.key">
        <ClassTable :data="obj" :classKey="data.key">
            <template v-if="index < 1" #title>
                <p class="text-tit-sub" style="margin-bottom: 20px">对{{ data.key }}差异在各指数的解读及其指数范围：</p>
            </template>
        </ClassTable>
        <template v-if="obj.subFeatureStat" v-for="o in obj.subFeatureStat">
            <div class="sub-class-table-wrap">
                <ClassTable :data="o" :classKey="data.key"></ClassTable>
            </div>
        </template>
    </template>

    <div style="margin-top: 34px">
        <p class="text-tit-sub" style="margin-bottom: 8px">
            <span data-title-level="4">{{ data.key }}差异在各等级水平的人数分布情况与解读</span>
        </p>
        <StackBar
            :color="['#DE952F', '#49C7C7', '#0092FA']"
            :maxNum="stackBarChartMaxNum"
            :legends="['低', '中', '高']"
            :dataSource="stackBarChartListInit(data.featureStat, 0)"
        ></StackBar>
    </div>
    <div style="margin-top: 5px">
        <StackBar
            :color="['#DE952F', '#49C7C7', '#0092FA']"
            :maxNum="stackBarChartMaxNum"
            :legends="['低', '中', '高']"
            :title="`图13  ${data.key}差异在各等级水平的人数分布情况`"
            :dataSource="stackBarChartListInit(data.featureStat, 1)"
        ></StackBar>
    </div>

    <template v-for="(obj, index) in data.featureStat" :key="obj.key">
        <ClassTable :data="obj" :classKey="data.key" :hideDesc="true">
            <template v-if="index < 1" #title>
                <p class="text-tit-sub" style="margin-bottom: 20px">对{{ data.key }}差异在各等级水平的人数分布情况解读：</p>
            </template>
            <template v-slot:content="{ contentData }">
                <div class="class-table-solt-val">
                    <p class="item" v-for="{ name, count, percent } in contentData.teamLevelStat">{{ name }}水平 {{ count }}人，占比 {{ percent }}%</p>
                </div>
            </template>
        </ClassTable>
    </template>
</template>
<script setup lang="ts">

import GroupBar from './group-bar.vue';
import ClassTable from './class-table.vue';
import StackBar from './stack-bar.vue';

const childrenClass = ref<string[]>([]); // 子集分类数
const groupBarChartList = ref<Array<TSourceData>>([]);
const stackBarChartMaxNum = ref(0);
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});

type TSourceData = {
    type?: string;
    label?: string;
    value?: number;
    children?: Array<TSourceData> | undefined;
};

const stackBarChartListInit = (list: any, flag = 0) => {
    let newList: Array<TSourceData> = [];
    let maxNum = 0;

    (list || []).forEach((item: { key?: any; valueList: any }, index: number) => {
        const { valueList = [] } = item || {};
        if ((flag === 0 && index < 1) || (flag === 1 && index > 0)) {
            const children: Array<TSourceData> = [];
            valueList.forEach((obj: { teamLevelStat?: never[] | undefined; name: any }, i: number) => {
                const { teamLevelStat = [], name } = obj;
                let flagNumber = 0;
                if (i > 0) {
                    children.push({
                        label: name,
                        children: teamLevelStat.map((o: { count: number; name: any }) => {
                            flagNumber += o.count;
                            return {
                                value: o.count,
                                type: o.name,
                            };
                        }),
                    });
                }
                maxNum = flagNumber > maxNum ? flagNumber : maxNum;
            });

            newList.push({
                label: item.key,
                children,
            });

            stackBarChartMaxNum.value = Math.ceil((maxNum + 1) / 5) * 5;
        }
    });

    return newList;
};

const chartListInit = (list: any) => {
    let newList: Array<TSourceData> = [];

    (list || []).forEach((item: any, index: number) => {
        const { valueList = [], key, teamLevelStat } = item || {};

        let data = <TSourceData>{
            label: key,
            children: [],
        };

        let newChildrenClass: string[] = [];

        (valueList || []).forEach((obj: { name: string; score: string }, i: any) => {
            if (index < 1) {
                newChildrenClass.push(obj.name);
            }

            data.children?.push({
                type: obj.name,
                value: Number(obj.score),
            });
        });

        if (index < 1) {
            childrenClass.value = newChildrenClass;
        }

        newList.push(data);
    });

    groupBarChartList.value = [...newList];
};

onMounted(() => {
    chartListInit(props.data.featureStat);
});
</script>
<style scoped lang="less">
.sub-class-table-wrap {
    margin-top: 16px;
    padding: 10px 33px;
    background: #f6f6f6;
    border-radius: 10px 10px 0 0;

    & + .sub-class-table-wrap {
        margin-top: 0px;
        border-radius: 0 0 10px 10px;
    }

    &.blue {
        background: #eef7ff;

        :deep(.calss-table-wrap) {
            .class {
                background: #fff !important;
            }
        }
    }

    .calss-table-wrap {
        &:first-child {
            margin-top: 0 !important;
        }

        .class-table {
            outline: none !important;
        }
    }
}

:deep(.class-table-solt-val) {
    flex: 1;
    display: flex;

    & > .item {
        flex: 1;
        border-right: 1px solid #ececec;
        display: flex;
        flex-direction: column;
        justify-content: center;

        &:last-child {
            border: none;
        }
    }
}
</style>
