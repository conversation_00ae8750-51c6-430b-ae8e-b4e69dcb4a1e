<template>
    <div class="target-explain-wrap">
        <p v-if="Object.keys(title).length" style="margin: 10px 0;">
            <span class="text-subtitle">{{info.name}}：</span>
            <span class="desc-font">{{info.desc}}</span>
            <span v-if="info.subDetailInfo">，包含{{ info.subDetailInfo.map((sub: any) => `${sub.name}`).join('、') }}，共{{ info.subDetailInfo.length }}个维度。</span>
        </p>
        <div class="target-explain-content-wrap">
            <div class="tit-wrap">
                <p class="tit" style="margin: 0 auto; width: 58px">{{ data.name }}</p>
                <div style="margin: 5px auto 0"><LevelIcon :level="data.level"></LevelIcon></div>
            </div>
            <div class="chart-info-wrap">
                <div class="item-wrap">
                    <div class="chart">
                        <TargetExplainChart1 :data></TargetExplainChart1>
                    </div>
                    <div class="text-wrap desc-font">
                        <p>{{ data.name }}：{{ data.levelLanguage }}</p>
                        <p>左图解：本团队{{ data.name }} {{ levelStrMap[`${data.level}`] }}于常模，处于常模群体的 {{ levelStrMaps[`${data.level}`] }}水平。</p>
                    </div>
                </div>
                <div class="item-wrap">
                    <div class="chart">
                        <TargetExplainChart2 :data="{
                            exampleData: data.teamLevelStat.map(o =>({label: o.name, value: Number(o.count) }) ),
                            lineData: data.teamLevelStat.map(o =>({label: o.name, value: Number(o.percent) }) ),
                        }"></TargetExplainChart2>
                    </div>
                    <div class="text-wrap desc-font">
                        左图解：本团队在{{ data.name }}各等级水平的分布情况为：
                        <template v-for="item in data.teamLevelStat">
                            <p>·{{ item.name }}水平： {{ item.count }}人，占比为 {{ item.percent }}%；</p>
                        </template>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">

import TargetExplainChart1 from './target-explain-chart-1.vue';
import TargetExplainChart2 from './target-explain-chart-2.vue';
import LevelIcon from './level-icon.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    title: {
        type: Object,
        default: () => ({}),
    },
    info: {
        type: Object,
        default: () => ({}),
    }
});

const levelStrMap = {
    '1': '低',
    '2': '等',
    '4': '高',
};

const levelStrMaps = {
    '1': '低',
    '2': '中',
    '4': '高',
};
</script>
<style lang="less" scoped>
.target-explain-wrap {
    margin-top: 22px;

    .target-explain-content-wrap {
        display: flex;
        outline: 1px solid #dcdcdc;

        .tit-wrap {
            display: flex;
            flex-direction: column;
            justify-content: center;
            width: 90px;
            text-align: center;
            border-right: 1px solid #dcdcdc;
        }

        .chart-info-wrap {
            flex: 1;
            display: flex;
            flex-direction: column;

            .item-wrap {
                display: flex;
                border-bottom: 1px solid #dcdcdc;

                &:last-child {
                    border-bottom: none;
                }

                .chart {
                    width: 266px;
                }
                .text-wrap {
                    flex: 1;
                }

                .chart {
                    border-right: 1px solid #dcdcdc;
                }

                .text-wrap {
                    padding: 10px;
                }
            }
        }
    }
}
</style>
