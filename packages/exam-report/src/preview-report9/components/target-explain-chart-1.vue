<template>
    <div class="g-wrap">
        <div ref="columnContainerRef"></div>
    </div>
</template>
<script setup lang="ts">

import { Bar, Column, DualAxes } from '@antv/g2plot';
import { getMaxScaleValue } from './shared';

defineOptions({ name: 'FlexIndexNumber' });

const { data } = defineProps<{ data: Record<string, any> }>();

const columnContainerRef = ref();

const exampleData = computed(() => [
    { label: '团队', value: Number(data.score) },
    { label: '常模', value: Number(data.normalScore) },
]);

const columnContainer = ref();

function init() {
    columnContainer.value?.destroy();
    columnContainer.value = new Bar(columnContainerRef.value, {
        data: exampleData.value,
        xField: 'value',
        yField: 'label',
        isStack: true,
        color: (datum) => (datum.label === '团队' ? '#9B6DBF' : '#F6BD2E'),
        label: {
            position: 'right', // 将标签放在柱子中间
            offset: 4, // 确保标签不偏移
            formatter: (datum) => {
                return datum.value.toFixed(2);
            },
            style: {
                fill: '#1f1f1f',
                fontFamily: 'FZLT-HJ',
                fontSize: 10,
                fontWeight: 400,
                lineHeight: 26,
            },
        },
        maxBarWidth: 15,
        minBarWidth: 15,
        xAxis: {
            min: 0,
            max: 100,
            tickInterval: 100 / 5,
            label: {
                offset: 10,
                style: {
                    fill: '#000000',
                    fontFamily: 'FZLT-HJ',
                    fontSize: 10,
                    fontWeight: 400,
                    lineHeight: 26,
                },
            },
            grid: {
                line: {
                    style: (item: any, index: number) => {
                        return {
                            stroke: '#dcdcdc',
                            lineWidth: 1,
                        };
                    },
                },
            },
        },
        yAxis: {
            label: {
                rotate: 0,
                style: {
                    fill: '#000000',
                    fontFamily: 'Kanzhun',
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: 26,
                },
            },
            tickLine: {
                length: 0,
            },
        },

        renderer: 'svg',
        width: 260,
        height: 160,
        padding: [10, 35, 50, 42],
        autoFit: false,
        tooltip: false,
    });
    columnContainer.value.render();
}
watch(
    () => [exampleData.value, columnContainerRef.value],
    () => {
        if (columnContainerRef.value) init();
    },
    {
        immediate: true,
    },
);
</script>
<style scoped lang="less">
.g-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    .title {
        color: #1f1f1f;
        font-family: FZLT-HJ;
        font-size: 12px;
        font-weight: 400;
        line-height: 26px;
        text-align: center;
        white-space: pre-wrap;
    }
}
.content {
    width: 325px;
    height: 84px;
    flex-shrink: 0;
    background: #f1f9ff;
    padding: 8px 12px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
.big-text {
    color: #1f1f1f;
    font-family: 'Kanzhun';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
</style>
