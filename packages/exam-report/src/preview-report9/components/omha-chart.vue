<template>
    <div class="omha-chart-wrap">
        <div class="bg" :style="{
            width: width + 'px'
        }">
            <div v-for="line in lineList" :key="line" class="line-wrap">
                <span class="number">{{ line }}</span>
                <p class="line"></p>
            </div>
        </div>
        <div id="omha-chart" class="content">
            <div v-if="data[0]?.subDimensionScore.length" class="left">
                <OmhaChartContent :listData="data[0]?.subDimensionScore" title="分数越高越好"></OmhaChartContent>
            </div>
            <div v-if="data[1]?.subDimensionScore.length" class="right">
                <OmhaChartContent :listData="data[1]?.subDimensionScore" barColor="#DE952F" title="分数越低越好"></OmhaChartContent>
            </div>
        </div>

        <p class="omha-chart-name" data-chart-index="true">图23  团队职业心理健康各维度总览图</p>
    </div>
</template>
<script setup lang="ts">

import OmhaChartContent from './omha-chart-content.vue';
const lineList = ref([100,80,60,40,20,0])
const width = ref();
const leftListData = ref([
    {
        class: '特质',
        list: [
            {name: '团队',val: 89,},
            {name: '团队',val: 89,}
        ],
    },
    {
        class: '能力',
        list: [
            {name: '团队',val: 79,},
            {name: '团队',val: 100,},
            {name: '寻求支援力',val: 95,}
        ],
    },
    {
        class: '状态需要十个字来填充',
        list: [
            {name: '员工满意度',val: 83,},
        ],
    }
])

const rightListData = ref([
    {
        class: '特质',
        list: [
            {name: '焦虑',val: 89,},
            {name: '焦虑',val: 19,},
            {name: '焦虑',val: 79,},
            {name: '低成就感',val: 100,},
            {name: '工作冷漠感',val: 95,}
        ],
    },
    {
        class: '状态需要十个字来填充',
        list: [
            {name: '工作冷漠感',val: 15,},
            {name: '工作冷漠感',val: 45,}
        ],
    }
])

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    tag: {
        type: String,
        default: () => ('')
    },
});

onMounted(() => {
    const omhaLeftDom = document.querySelector('#omha-chart .left');
    const omhaRightDom = document.querySelector('#omha-chart .right');
    width.value = omhaLeftDom?.clientWidth + omhaRightDom?.clientWidth + 32;
})

</script>
<style lang="less" scoped>
.omha-chart-wrap {
    position: relative;
    height: 310px;

    .omha-chart-name {
        position: absolute;
        left: 50%;
        bottom: 0;
        transform: translateX(-50%);
        color: #1F1F1F;
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    .bg {
        .line-wrap {
            display: flex;

            .number {
                transform: translateY(20px);
                margin-right: 4px;
                width: 26px;
                text-align: right;
                color: #808080;
                font-family: var(--FZLanTingHeiS-R-GB);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
            }

            .line {
                flex: 1;
                margin-top: 24px;
                border-top: 1px solid #DEDEDE;
            }
        }
    }

    .content {
        display: flex;
        position: absolute;
        left: 30px;
        right: 0;
        top: 24px;
        // border-left: 1px solid #DEDEDE;
        // border-right: 1px solid #DEDEDE;
        // color: #000000;
        // font-family: var(--FZLanTingHeiS-R-GB);
        // font-size: 12px;
        // font-style: normal;
        // font-weight: 400;
        // line-height: 26px;
        // text-align: right;

        &> .left,.right {
            border-left: 1px solid #DEDEDE;
            border-right: 1px solid #DEDEDE;
        }

        & > .right {
            border-left: none;
        }
    }
}

</style>
