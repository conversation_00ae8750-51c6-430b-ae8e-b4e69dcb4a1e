<template>
    <div class="chart-info-wrap" v-if="listData.length">
        <div class="chart-wrap">
            <div v-for="item in listData" class="class-item">
                <div class="data-wrap">
                    <div v-for="obj in item.subDimensionScore" class="data">
                        <div class="data-bar">
                            <p class="bar" :style="{height: `${obj.score}%`, background: barColor}">
                                <span class="namber">{{obj.score }}</span>
                            </p>
                        </div>
                        <p class="data-name">{{ obj.name }}</p>
                    </div>
                </div>
                <div class="class-name" :class="{'min': item.subDimensionScore.length < 3 && item.name.length > 8}">{{ item.name }}</div>
            </div>
        </div>
        <p class="chart-info-name">{{ title }}</p>
    </div>
</template>
<script setup lang="ts">

type IlistData = {
    name: Array<string>,
    subDimensionScore: any,
}

const props = defineProps({
    listData: {
        type: Array<IlistData>,
        default: () => ([])
    },
    title: {
        type: String,
        default: () => ('')
    },
    barColor: {
        type: String,
        default: () => ('#0092FA')
    }
});
</script>
<style lang="less" scoped>
    .chart-info-wrap {
        color: #000000;
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        text-align: right;

        &:first-child {
            border-right: none;
        }

        .chart-wrap {
            display: flex;
            
            .class-item {
                border-right: 1px solid #DEDEDE;

                &:last-child {
                    border-right: none;
                }

                .data-wrap {
                    display: flex;
                    justify-content: space-around;

                    .data {
                        margin: 0 17.5px;
                        position: relative;

                        .data-bar {
                            display: flex;
                            flex-direction: column;
                            justify-content: end;
                            margin: 0 auto;
                            width: 15px;
                            height: 124px;

                            .bar {
                                position: relative;
                                .namber {
                                    position: absolute;
                                    left: 50%;
                                    top: -20px;
                                    width: 48px;
                                    text-align: center;
                                    white-space: nowrap;
                                    transform: translateX(-50%);
                                    // line-height: 26px;
                                }
                            }
                        }

                        .data-name {
                            white-space: nowrap;
                            position: absolute;
                            right: 4px;
                            bottom: -18px;
                            transform-origin: right center;
                            transform: rotate(-40deg);
                            // transform: matrix(0.74, -0.68, 0.74, 0.68, 0, 0);
                        }
                    }
                }

                .class-name {
                    height: 80px;
                    // padding-top: 60px;
                    display: flex;
                    flex-direction: column;
                    justify-content: end;
                    text-align: center;
                    line-height: 16px;

                    &.min {
                        transform: translateY(7px) scale(0.85);
                        // font-size: 10px;
                    }
                }
            }
        }

        .chart-info-name {
            padding-top: 5px;
            text-align: center;
            color: #000000;
            font-family: var(--FZLanTingHeiS-R-GB);
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
        }
    }

    // background: #DE952F !important;
</style>
