<template>
    <div class="page-footer-wrap">
        <div class="footer-left">
            <Split v-if="itemName">{{ itemName?.fieldValue }}</Split>
            <Split class="report-name-filed">{{ info.templateName }}</Split>
            <Split v-if="itemRef">{{ itemRef.fieldShowName }}：{{ itemRef?.fieldValue }}</Split>
        </div>
        <div class="page-count">1</div>
    </div>
</template>
<script setup lang="ts">

import { Split } from '@boss/design';
import '@boss/design/es/split/style.js';

defineOptions({
    name: 'PageFooter',
});
const props = defineProps({
    info: {
        type: Object,
        default: () => ({}),
    },
});
type teamInfoItem = {
    encryptFieldId: string;
    fieldName: string;
    fieldShowName: string;
    fieldValue: string;
};
const itemName = computed(() => props.info.teamInfoList.find((x: teamInfoItem) => x.fieldName === '团队名称'));
const itemRef = computed(() => props.info.teamInfoList.find((x: teamInfoItem) => x.fieldName === '团队报告参考性'));
</script>
