<template>
    <div class="calss-table-wrap">
        <slot name="title"></slot>
        <p class="tit">{{data.key}}指数：</p>
        <div v-if="!hideDesc" class="desc-font" >
            <p>
                <span>该指数在</span>
                <template v-for="(item, index) in data.valueList">
                    <span>{{index > 0 ? item.name: '团队整体上'}}为</span>
                    <span class="highlight-number" style="color: #5470C6; margin-left: 2px;">{{ item.score }}</span>
                    {{ index < data.valueList.length - 1? '，': "。" }}
                </template>
            </p>
            <p>不同{{classKey}}在该指数的范围：</p>
        </div>
        <div class="class-table">
            <template v-for="(item, index) in data.valueList" :key="item.class">
                <div v-if="index > 0" class="item-wrap">
                    <p class="class">{{ item.name }}</p>
                    <slot name="content" :content-data="item">
                        <div class="val">最低 {{item.minScore}}~最高 {{ item.maxScore }}</div>
                    </slot>
                </div>
            </template>
        </div>
    </div>
</template>
<script setup lang="ts">

const list = ref([
    {
        class: '女性',
        value: ['最低60~最高97'],
    },
    {
        class: '男性',
        value: ['最低35~最高95'],
    },
])

const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    classKey: {
        type: String,
        default: () => ('')
    },
    hideDesc: {
        type: Boolean,
        default: () => (false)
    }
});
</script>
<style lang="less" scoped>
.calss-table-wrap {
    margin-top: 20px;

    .tit {
        color: #1F1F1F;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    .class-table {
        margin-top: 7px;
        outline: 1px solid #ECECEC;
        border-radius: 0 0 6px 6px;
        text-align: center;
        background: #FFF;

        .item-wrap {
            border-bottom: 1px solid #ECECEC;
            display: flex;
            height: 40px;
            line-height: 40px;

            &:last-child {
                border-bottom: none;
            }

            .class {
                width: 155px;
                background: #EEF7FF;
                border-right: 1px solid #ECECEC;
            }

            .val {
                display: flex;
                padding: 0 40px;
            }
        }
    }
}

</style>
