<template>
    <div class="bar-wrap">
        <div ref="groupBarContainerRef" class="chart-wrap">
            <div class="line-group">
                <div v-for="(item, index) in 6" :key="index" class="line-item" :style="{ width: `${ unitWidth * Math.ceil(scaleNum) }px`}">
                    <p class="num">{{ Math.ceil(index * scaleNum) }}</p>
                </div>
            </div>
            <ul class="y-axis-label">
                <li v-for="(item, index) in dataSource" :key="index">
                    <span class="main-label">{{ item.label }}</span>
                    <div class="group-item">
                        <div class="sub-label" v-for="(subItem, i) in (item.children || [])" :key="i" >
                            <span class="text" >{{ subItem.label }}</span>
                            <template v-for="(dataItem, x) in (subItem.children || [])" :key="x">
                                <span
                                    v-if="dataItem.value"
                                    class="grid-line"
                                    :class="{[`no-width-${x}`]: !dataItem.value && !(subItem.children || [])[x - 1]?.value}"
                                    :style="{width: `${unitWidth * dataItem.value}px`, background: color[x]}"

                                >{{ dataItem.value }}</span>
                            </template>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <div v-if="legends.length" class="legend-box">
            <span v-for="(item, index) in legends" :key="index" class="legend-item">
                <span :style="{ backgroundColor: color[index] }"></span>{{ item }}
            </span>
        </div>
        <div v-if="title" class="title" data-chart-index="true">{{ title }}</div>
    </div>
</template>
<script setup lang="ts">

type TSourceData = {
    label: string;
    value: number;
    children?: Array<TSourceData>;
};

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    groupItems: {
        type: Number,
        default: 2
    },
    color: {
        type: Array<string>,
        default: () => []
    },
    dataSource: {
        type: Array<TSourceData>,
        default: () => []
    },
    splitNum: {
        type: Number,
        default: 3
    },
    itemHeight: {
        type: Number,
        default: 15
    },
    legends: {
        type: Array,
        default: () => []
    },
    maxNum: {
        type: Number,
        default: null
    }
});
const chartWidth = 555;
const unitWidth = computed(() => chartWidth / props.maxNum);
const scaleNum = computed(() => props.maxNum / 5);
</script>
<style scoped lang="less">
.bar-wrap {
    .title {
        margin-bottom: 10px;
        color: #1f1f1f;
        font-family: FZLT-HJ;
        font-size: 12px;
        font-weight: 400;
        line-height: 26px;
        text-align: center;
        transform: translate(-20px, 8px);
        white-space: pre-wrap;
    }
    .legend-box {
        text-align: center;
        margin-top: 36px;
        .legend-item {
            color: #000000;
            font-family: FZLT-HJ;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
            padding-right: 28px;
            span {
                width: 12px;
                height: 10px;
                display: inline-block;
                margin-right: 9px;
                transform: translateY(1px);
            }
        }
    }
    .chart-wrap {
        height: 100%;
        position: relative;
        .line-group {
            height: 100%;
            position: absolute;
            left: 135px;
            display: flex;
            .line-item {
                height: 100%;
                border-left: 1px solid #dcdcdc;
                transform: translateX(-1px);
                position: relative;
                .num {
                    width: 27px;
                    text-align: center;
                    color: #808080;
                    font-family: FZLT-HJ;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: 26px;
                    position: absolute;
                    bottom: -33px;
                    left: -13px;
                }
            }
        }
        .y-axis-label {
            width: 700px;
            position: relative;
            display: flex;
            justify-content: space-between;
            flex-direction: column;
            li {
                display: flex;
                justify-content: space-between;
                flex: 1;
            }
            .main-label {
                width: 54px;
                min-width: 54px;
                background: #f5f5f5;
                color: #000000;
                font-family: Kanzhun;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
                text-align: center;
                padding: 0 6px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-right: 1px solid #dcdcdc;
            }
            .group-item > :first-child {
                .text {
                    padding-top: 10px;
                }
                .grid-line {
                    margin-top: 10px;
                }
            }
            .group-item > :last-child {
                .text {
                    padding-bottom: 10px;
                }
                .grid-line {
                    margin-bottom: 10px;
                }
            }
            .group-item {
                flex: 1;
                &::before {
                    content: '';
                    width: 1px;
                    height: 100%;
                    background: #dcdcdc;
                    position: absolute;
                    top: 0;
                    left: 135px;
                }
            }
            .sub-label {
                line-height: 20px;
                display: flex;
                flex-direction: row;
                align-items: center;
                .text {
                    max-width: 81px;
                    color: #000000;
                    background: #f5f5f5;
                    font-family: FZLT-HJ;
                    font-size: 12px;
                    font-weight: 400;
                    text-align: end;
                    padding-right: 13px;
                    flex: 1;
                    display: flex;
                    align-items: center;
                    justify-content: end;
                }
                .grid-line {
                    height: 15px;
                    line-height: 15px;
                    text-align: center;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 400;
                    font-family: FZLT-HJ;
                    display: inline-block;
                }
                .no-width-0 {
                    transform: translateX(-3px);
                    z-index: 1;
                }
                .no-width-1 {
                    transform: translateX(7px);
                    z-index: 1;
                }
                .no-width-2 {
                    transform: translateX(17px);
                    z-index: 1;
                }
            }
        }
    }
}
</style>
