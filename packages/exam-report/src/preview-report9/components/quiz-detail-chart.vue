<template>
    <div class="quiz-detail-chart-wrap" :style="{ marginTop: $slots.title ? '0px' : '34px' }">
        <slot name="title"></slot>
        <div class="tit-wrap" :data-title-level="titleLevel" :data-title-text="`${index}${data.name}指数`">
            <p class="text-tit" style="margin-right: 10px">{{ index }}{{ data.name }}： {{ data.score }}</p>
            <LevelIcon :level="data.level"></LevelIcon>
        </div>
        <p v-if="data.desc" class="desc-font" style="margin-top: 10px">{{ data.desc }}</p>
        <p v-if="data.levelLanguage" class="desc-tag-wrap desc-font">
            团队{{data.name}}指数处于{{levelStrMaps[data.level]}}水平，表现为{{data.levelLanguage}}
        </p>
        <div class="chart-wrap">
            <div class="chart-item">
                <div>
                    <!-- <p>
                        数据1:{{
                            [
                                { name: '团队', val: data.score },
                                { name: '常模', val: data.normalScore },
                            ]
                        }}
                    </p> -->
                    <FlexIndexSingle
                        :title="`图4  团队${data.name}指数与常模对比`"
                        :data="[
                            { label: '团队', value: Number(data.score) },
                            { label: '常模', value: Number(data.normalScore) },
                        ]"
                    ></FlexIndexSingle>
                </div>
                <div class="info-wrap">
                    <p>
                        【指数范围：最低 <span class="number">{{ data.minScore }}</span
                        >~最高 <span class="number">{{ data.maxScore }}</span
                        >】
                    </p>
                    <p>
                        <span>团队{{ data.name }}指数为 {{ data.score }}，</span>
                        <span>该测验的常模为 {{ data.normalScore }}，本团队此指数 </span>
                        <span v-if="data.score > data.normalScore">高</span>
                        <span v-if="data.score === data.normalScore">等</span>
                        <span v-if="data.score < data.normalScore">低</span>
                        <span> 于常模。</span>
                    </p>
                </div>
            </div>
            <div style="width: 10px"></div>
            <div class="chart-item">
                <div>
                    <!-- <p>数据2:{{ data.teamLevelStat }}</p> -->
                    <FlexIndexNumber :title="`图4  团队${data.name}指数各等级水平分布`" :data="data.teamLevelStat"></FlexIndexNumber>
                </div>
                <div class="info-wrap">
                    团队各等级水平分布为，
                    <template v-for="item in data.teamLevelStat"> {{ item.name }}水平 {{ item.count }}人，占比为 {{ item.percent }}%； </template>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import FlexIndexNumber from './flex-index-number.vue';
import FlexIndexSingle from './flex-index-single.vue';

import LevelIcon from './level-icon.vue';

const levelStrMap = {
    '1': '低',
    '2': '等',
    '4': '高',
};
const levelStrMaps: any = {
    '1': '低',
    '2': '中',
    '4': '高',
};
const props = defineProps({
    index: {
        type: String,
        default: () => '1',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    titleLevel: {
        type: String,
    },
});
</script>
<style lang="less" scoped>
.quiz-detail-chart-wrap {

    .tit-wrap {
        display: flex;
        align-items: center;
    }

    .desc-tag-wrap {
        margin: 6px 0 10px 0;
        padding: 10px;
        background: #f1f9ff;
        border-radius: 10px;
    }

    .chart-wrap {
        display: flex;
        justify-content: space-between;

        .chart-item {
            flex: 1;
            padding: 5px;
            border: 1px solid #11a1fc;
            background: #FFF;

            .info-wrap {
                padding: 9px 12px;
                background: #edf7ff;
                color: #1f1f1f;
                font-family: var(--FZLanTingHeiS-R-GB);
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 22px;

                .number {
                    font-family: var(--Kanzhun);
                    font-size: 18px;
                }
            }
        }
    }
    .flex-box {
        display: flex;
        gap: 11px;
        justify-content: center;
    }
    .container {
        width: 337px;
        height: 296px;
        flex-shrink: 0;
        background: #ffffff;
        border: 1px solid #11a1fc;
        box-sizing: content-box;
    }
}
</style>
