<template>
    <div class="g-wrap">
        <div ref="columnContainerRef"></div>
        <div class="title" data-chart-index="true">{{ title }}</div>
    </div>
</template>
<script setup lang="ts">

import { DualAxes } from '@antv/g2plot';
import { getMaxScaleValue } from './shared';

defineOptions({ name: 'FlexIndexNumber' });

const props = defineProps<{
    title: string;
    data: any[];
}>();
const columnContainerRef = ref();

const exampleData = computed(() => props.data.map((i) => ({ label: i.name, value: Number(i.count) })));
const lineData = computed(() => props.data.map((i) => ({ label: i.name, value: Number(i.percent) })));

function init() {
    const columnContainer = new DualAxes(columnContainerRef.value, {
        data: [exampleData.value, lineData.value],
        xField: 'label',
        yField: ['value', 'value'],
        geometryOptions: [
            {
                geometry: 'column',
                isGroup: true,
                color: '#6394F9',
                label: {
                    position: 'middle', // 将标签放在柱子中间
                    offset: 0, // 确保标签不偏移
                    style: {
                        fill: '#1f1f1f',
                        fontFamily: 'FZLT-HJ',
                        fontSize: 12,
                        fontWeight: 400,
                        lineHeight: 26,
                    },
                },
                maxColumnWidth: 15,
                minColumnWidth: 15,
            },
            {
                geometry: 'line',
                color: '#F6B18C',
                label: {
                    formatter: (value) => value.value + '%',
                    offset: 12,
                    style: {
                        fill: '#000000',
                        fontFamily: 'FZLT-HJ',
                        fontSize: 12,
                        fontWeight: 400,
                        lineHeight: 22,
                    },
                },
                point: {
                    size: 3.5, // 设置点的大小
                    shape: 'circle',
                    style: {
                        fill: '#F6B18C',
                        stroke: '#ffffff',
                        lineWidth: 0,
                        zIndex: 100,
                    },
                },
                style: {
                    lineWidth: 3, // 设置折线的线宽
                },
            },
        ],
        renderer: 'svg',
        width: 305,
        height: 160,
        padding: [10, 20, 18, 20],
        autoFit: false,
        tooltip: false,
        xAxis: {
            label: {
                rotate: 0,
                style: {
                    fill: '#000000',
                    fontFamily: 'Kanzhun',
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: 26,
                },
            },
            tickLine: {
                length: 0,
            },
        },
        yAxis: [
            {
                min: 0,
                max: getMaxScaleValue(exampleData.value) * 1.05,
                tickInterval: getMaxScaleValue(exampleData.value) / 5,
                label: {
                    offset: 10,
                    style: {
                        fill: '#808080',
                        fontFamily: 'FZLT-HJ',
                        fontSize: 12,
                        fontWeight: 400,
                        lineHeight: 26,
                    },
                },
                grid: {
                    line: {
                        style: (item: any, index: number) => {
                            return {
                                stroke: '#dcdcdc',
                                lineWidth: 1,
                            };
                        },
                    },
                },
            },
            {
                min: 0,
                max: getMaxScaleValue(lineData.value, 100) * 1.05,
                tickInterval: getMaxScaleValue(lineData.value, 100) / 5,
                label: {
                    offset: 10,
                    formatter: (value) => value + '%',
                    style: {
                        fill: '#808080',
                        fontFamily: 'FZLT-HJ',
                        fontSize: 12,
                        fontWeight: 400,
                        lineHeight: 22,
                    },
                },
                grid: null,
            },
        ],
        legend: {
            position: 'bottom', // 设置图例的位置
            offsetY: 9, // 设置图例距离坐标轴的距离，可以根据需要调整数值
            itemName: {
                formatter: (text, item, index) => (index === 0 ? '人数' : '比例'),
                style: {
                    fill: '#000000', // 设置图例文字颜色
                    fontSize: 12, // 设置图例文字大小
                    fontWeight: 400, // 设置图例文字粗细
                },
            },
            marker: {
                symbol: 'square', // 可以设置图例标记的形状，如 'circle', 'square', 'line', 等
                style: {
                    r: 5, // 设置图例标记的大小
                },
            },
        },
    });

    columnContainer.render();
}
watch(
    () => [exampleData.value, lineData.value, columnContainerRef.value],
    () => {
        if (columnContainerRef.value) init();
    },
    {
        immediate: true,
    },
);
</script>
<style scoped lang="less">
.g-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    .title {
        color: #1f1f1f;
        font-family: FZLT-HJ;
        font-size: 12px;
        font-weight: 400;
        line-height: 26px;
        text-align: center;
        white-space: pre-wrap;
    }
}
.content {
    width: 325px;
    height: 84px;
    flex-shrink: 0;
    background: #f1f9ff;
    padding: 8px 12px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
</style>
