<template>
    <div class="g-wrap">
        <div ref="columnContainerRef"></div>
        <div class="title" data-chart-index="true">{{ title }}</div>
    </div>
</template>
<script setup lang="ts">

import { Column } from '@antv/g2plot';
import { getMaxScaleValue } from './shared';

defineOptions({ name: 'FlexIndexNumber' });

const props = defineProps<{
    title: string;
    data: Array<any>;
}>();
const columnContainerRef = ref();

function init() {
    const columnContainer = new Column(columnContainerRef.value, {
        data: props.data,
        xField: 'label',
        yField: 'value',

        isGroup: true,
        color: (datum) => (datum.label === '团队' ? '#9B6DBF' : '#F6BD2E'),
        label: {
            position: 'top', // 将标签放在柱子中间
            offset: 0, // 确保标签不偏移
            formatter: (value) => value.value.toFixed(2),
            style: {
                fill: '#1f1f1f',
                fontFamily: 'FZLT-HJ',
                fontSize: 12,
                fontWeight: 400,
                lineHeight: 26,
            },
            // formatter: (value) => value.value.toFixed(2),
        },
        maxColumnWidth: 15,
        minColumnWidth: 15,
        xAxis: {
            label: {
                rotate: 0,
                style: {
                    fill: '#000000',
                    fontFamily: 'Kanzhun',
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: 26,
                },
            },
            tickLine: {
                length: 0,
            },
        },
        yAxis: {
            min: 0,
            max: 100,
            tickInterval: 100 / 5,
            label: {
                offset: 10,
                style: {
                    fill: '#808080',
                    fontFamily: 'FZLT-HJ',
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: 26,
                },
            },
            grid: {
                line: {
                    style: (item: any, index: number) => {
                        return {
                            stroke: '#dcdcdc',
                            lineWidth: 1,
                        };
                    },
                },
            },
        },

        renderer: 'svg',
        width: 305,
        height: 160,
        padding: [25, 0, 25, 30],
        autoFit: false,
        tooltip: false,
    });

    columnContainer.render();
}
watch(
    () => [props.data, columnContainerRef.value],
    () => {
        if (columnContainerRef.value) init();
    },
    {
        immediate: true,
    },
);
</script>
<style scoped lang="less">
.g-wrap {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 10px;
    .title {
        color: #1f1f1f;
        font-family: FZLT-HJ;
        font-size: 12px;
        font-weight: 400;
        line-height: 26px;
        text-align: center;
        white-space: pre-wrap;
    }
}
.content {
    width: 325px;
    height: 84px;
    flex-shrink: 0;
    background: #f1f9ff;
    padding: 8px 12px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
.big-text {
    color: #1f1f1f;
    font-family: 'Kanzhun';
    font-size: 18px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
</style>
