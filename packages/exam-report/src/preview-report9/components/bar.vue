<template>
    <div class="bar-wrap">
        <div ref="barContainerRef"></div>
        <div class="title" data-chart-index="true">图2  团队职业心理健康指数与常模对比</div>
    </div>
</template>
<script setup lang="ts">

import { Bar } from '@antv/g2plot';

const props = defineProps({
    data: {
        type: Array,
        default: () => [
            { label: '团\n队', value: 100 },
            { label: '常\n模', value: 85.11 },
        ]
    }
});
const barContainerRef = ref();
function init() {
    const barContainer = new Bar(barContainerRef.value, {
        data: props.data,
        xField: 'value',
        yField: 'label',
        renderer: 'svg',
        width: 700,
        height: 100,
        autoFit: false,
        tooltip: false,
        color: ({ label }) => {
            return label === '团\n队' ? '#9B6DBF' : '#F6BD2E';
        },
        yAxis: {
            label: {
                rotate: 0,
                offsetX: -18,
                style: {
                    fill: '#000000',
                    fontFamily: 'Kanzhun',
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: 26
                }
            },
            tickLine: {
                length: 0
            }
        },
        xAxis: {
            min: 0,
            max: 110,
            tickInterval: 10,
            label: {
                formatter: value => Number(value) <= 100 ? value : '',
                style: {
                    fill: '#808080',
                    fontFamily: 'FZLT-HJ',
                    fontSize: 12,
                    fontWeight: 400,
                    lineHeight: 26
                }
            },
            grid: {
                line: {
                    style: (item: any, index: number) => {
                        if (index > 10) return {}
                        return {
                            stroke: '#dcdcdc',
                            lineWidth: 1
                        }
                    }
                }
            }
        },
        minBarWidth: 15,
        maxBarWidth: 15,
        label: {
            position: 'right',
            offset: 10,
            formatter: (datum) => {
                return datum.value.toFixed(2);
            },
            style: {
                fill: '#1f1f1f',
                fontFamily: 'FZLT-HJ',
                fontSize: 12,
                fontWeight: 400,
                lineHeight: 26
            }
        },
    });

    barContainer.render();
}
onMounted(init);
</script>
<style scoped lang="less">
.title {
     color: #1f1f1f;
     font-family: FZLT-HJ;
     font-size: 12px;
     font-weight: 400;
     line-height: 26px;
     text-align: center;
     transform: translate(-27px, 17px);
     white-space: pre-wrap;
}
</style>
