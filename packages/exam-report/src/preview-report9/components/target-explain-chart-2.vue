<template>
    <div class="g-wrap">
        <div class="legend-wrap">
            <div class="legend-container">
                <div class="legend"></div>
                <div style="font-size: 12px">人数</div>
            </div>
            <div class="legend-container">
                <div class="legend" style="background: #f6b18c"></div>
                <div style="font-size: 12px">比例</div>
            </div>
        </div>
        <div class="grid-container">
            <template v-for="(column, index) in grid">
                <div class="grid-box">
                    <div class="top-number">{{ (maxTopNumber / 5) * index }}</div>
                    <div class="bottom-number">{{ (maxBottomNumber / 5) * index }}%</div>
                </div>
                <div v-if="index !== grid.length - 1" style="width: 30px" />
            </template>
            <div class="bar-container">
                <div v-for="(ex, index) in data.exampleData" class="bar" :style="{ width: (ex.value / maxTopNumber) * 155 + 'px' }">
                    {{ ex.value }}
                    <div class="bar-text">{{ ex.label }}</div>
                </div>
            </div>
            <div class="polyline">
                <svg width="187" height="101" xmlns="http://www.w3.org/2000/svg">
                    <!-- 数据折线 -->
                    <polyline fill="none" stroke="#F6B18C" stroke-width="3" :points />

                    <!-- 数据点 -->
                    <circle v-for="(column, index) in data.lineData" :cx="(column.value / maxBottomNumber) * 155" :cy="23 + 26.5 * index" r="3" fill="#F6B18C" />
                    <text
                        v-for="(column, index) in data.lineData"
                        :x="(column.value / maxBottomNumber) * 155 + 8"
                        :y="26 + 26.5 * index"
                        font-family="FZLT-HJ"
                        font-size="10px"
                        fill="fill-color"
                        text-anchor="anchor"
                    >
                        {{ column.value.toFixed(0) + '%' }}
                    </text>
                </svg>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">

import { getMaxScaleValue } from './shared';

defineOptions({ name: 'FlexIndexNumber' });
const { data } = defineProps<{ data: Record<string, any> }>();

const grid = ref(new Array(6));

const maxTopNumber = computed(() => getMaxScaleValue(data.exampleData));
const maxBottomNumber = computed(() => getMaxScaleValue(data.lineData, 100));
const points = computed(() => {
    return data.lineData
        .map((column: { value: number }, index: number) => {
            return `${(column.value / maxBottomNumber.value) * 156},${23 + 26.5 * index}`;
        })
        .join(' ');
});
</script>
<style scoped lang="less">
.g-wrap {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 30px;
    .title {
        color: #1f1f1f;
        font-family: FZLT-HJ;
        font-size: 12px;
        font-weight: 400;
        line-height: 26px;
        text-align: center;
        white-space: pre-wrap;
    }
}
.content {
    width: 325px;
    height: 84px;
    flex-shrink: 0;
    background: #f1f9ff;
    padding: 8px 12px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
}
.bar {
    position: relative;
    height: 12.66px;
    margin-bottom: 13.53px;
    font-size: 10px;
    background: #6394f9;
    text-align: center;
}
.grid-container {
    position: relative;
    display: flex;
    justify-content: space-between;
    height: 101.26px;
    margin: 24.5px 0;
}
.grid-box {
    position: relative;
    border-left: #dcdcdc solid 1px;
}
.bar-text {
    position: absolute;
    top: -6px;
    left: -18px;

    font-size: 12px;
    color: #000000;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
.bar-container {
    position: absolute;
    top: 17px;
    left: 0;
}
.top-number {
    text-align: center;
    width: 40px;
    font-size: 10px;
    position: absolute;
    top: -15px;
    left: -20px;
    z-index: 1;
}
.bottom-number {
    text-align: center;
    width: 40px;
    font-size: 10px;
    position: absolute;
    bottom: -15px;
    left: -20px;
    z-index: 1;
}
.legend {
    width: 10px;
    height: 10px;
    transform: rotate(-90deg);
    background: #46a3eb;
}
.legend-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}
.legend-wrap {
    margin-left: 7px;
    display: flex;
    flex-direction: column;
    gap: 18px;
}
.polyline {
    position: absolute;
}
</style>
