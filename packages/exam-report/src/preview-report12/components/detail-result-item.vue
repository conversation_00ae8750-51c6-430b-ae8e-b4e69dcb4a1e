<template>
    <div
        v-for="(item, index) of data.subDetailInfo"
        :key="index"
        class="part-item"
        :class="[{ 'part-item-with-l1': index === 0 }]"
    >
        <template v-if="index === 0">
            <div class="l1-title-wrap" :style="{ '--line-color': colorSet[index % colorSet.length].line, '--gradient': colorSet[index % colorSet.length].gradient }">
                {{ data.dimensionInfo.dimensionShowName }}
            </div>
            <div class="definition-wrap">
                <!-- <span style="font-family: var(--FZLanTingHeiS-DB-GB)">维度定义：</span> -->
                <span class="normal-content">{{ data.definition }}</span>
            </div>
        </template>
        <div class="sub-item-wrap">
            <div class="l2-top">
                <div class="l2-title">
                    {{ item.dimensionInfo.dimensionName }}
                </div>
                <div class="l2-level" :style="{ backgroundColor: getColor(item).tag_bg, color: getColor(item).font }">
                    <span>{{ getColor(item).levelName }}</span>
                </div>
                <Progress
                    class="progress-wrap"
                    :style="{ '--value-bar-border-radius-amount': Number(item.score) < 100 ? '4px 0 0 4px' : '4px 2px 2px 4px' }"
                    :borderRadius="4"
                    :footerConfig="{ showPercent: false }"
                    :height="10"
                    :minPartWidth="45"
                    :partColors="['#F2F2F2']"
                    :showTick="true"
                    :splitArray="splitArray"
                    :totalWidth="400"
                    type="grow"
                    :value="item.score"
                    :valueBarStyle="{ color: getColor(item).bar_front }"
                >
                    <template #indicator-extra>
                        <div class="detail-wrap" :class="Number(item.score) > 50 ? 'left' : 'right'">
                            超越<span class="number">{{ item.beyondPercent }}%</span>的人
                        </div>
                    </template>
                </Progress>
            </div>
            <div class="l2-bottom">
                <div class="dingyi">
                    <span>定义：</span><span class="normal-content">{{ item.definition }}</span>
                </div>
                <div class="biaoxian">
                    <div class="normal-content">
                        <span>表现：</span>
                    </div>
                    <div class="biaoxian-inner">
                        <p v-for="(statement, stateIndex) of item.dimensionInfo.levelLanguage?.split('\n')" :key="stateIndex" class="normal-content">
                            <span>·</span>{{ statement }}
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import Progress from '@packages/exam-report/components/progress/index.vue'

import { levelType, titleColor } from '../util/config'

import type { IDimensionItem } from '../type/type'

defineOptions({
    name: 'DetailResultItem',
})
const props = defineProps({
    data: {
        type: Object as PropType<IDimensionItem>,
        default: () => ({}),
    },
    index: {
        type: Number,
        default: 0,
    },
    splitArray: {
        type: Array as PropType<number[]>,
        default: () => [],
    },
})

const colorSet = [
    { line: '#00BEBD', gradient: 'linear-gradient(to right, #CEF5F4, #FFFFFF)' },
    { line: '#56BD22', gradient: 'linear-gradient(to right, #D6F0C9, #FFFFFF)' },
    { line: '#7046FA', gradient: 'linear-gradient(to right, #DED4FF, #FFFFFF)' },
]

function getColor(item: IDimensionItem) {
    return titleColor[item.dimensionInfo.dimensionLevel]
}
</script>

<style lang="less" scoped>
.l1-title-wrap {
    background-image: var(--gradient);
    display: flex;
    align-items: center;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 18px;
    line-height: 31px;
    padding: 4.5px 0;
    border-radius: 4px;
    &::before {
        content: '';
        width: 5px;
        height: 18px;
        margin-right: 8px;
        border-radius: 2.67px;
        background-color: var(--line-color);
    }
}
.definition-wrap {
    margin: 12px 0;
    color: #808080;
    font-size: 12px;
    line-height: 26px;
}
.sub-item-wrap {
    border: 3px solid #f7f7f7;
    border-radius: 4px;
    padding-top: 29px;
    padding-left: 17px;
    padding-right: 17px;
    padding-bottom: 9px;
    .l2-top {
        display: flex;
        align-items: center;
        padding-bottom: 26px;
        border-bottom: 1px solid #ececec;
        .l2-title {
            color: #1f1f1f;
            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 15px;
            line-height: 32px;
            width: 60px;
            margin-right: 10px;
        }
        .l2-level {
            width: 47px;
            height: 24px;
            border-radius: 2px;

            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 15px;
            line-height: 20px;

            display: flex;
            align-items: center;
            justify-content: center;

            margin-right: 81px;
        }
        :deep(.progress-wrap) {
            .value-indicator-wrap {
                top: 3px;
                transform: translate(-50%, -100%);
                .number {
                    color: #1f1f1f;
                }

                .detail-wrap {
                    padding: 0 8px;
                    background: #f2f2f2;
                    border-radius: 4px;
                    display: flex;
                    align-items: center;
                    font-size: 12px;
                    line-height: 24px;
                    white-space: nowrap;
                    position: absolute;
                    top: -3.5px;
                    .number {
                        font-family: var(--number-font-family);
                        font-size: 14px;
                        line-height: 17px;
                        position: relative;
                        // top: -2px;
                        margin: 0 4px;
                    }

                    &::before {
                        content: '';
                        width: 0;
                        height: 0;
                        border-top: 4px solid transparent;
                        border-bottom: 4px solid transparent;
                        position: absolute;
                    }

                    &.left {
                        right: calc(100% + 11px);
                        &::before {
                            border-left: 7px solid #f2f2f2;
                            right: 0;
                            transform: translateX(100%);
                        }
                    }
                    &.right {
                        left: calc(100% + 11px);
                        &::before {
                            border-right: 7px solid #f2f2f2;
                            left: 0;
                            transform: translateX(-100%);
                        }
                    }
                }
            }
            .bar-item.last {
                border-radius: 0px 2px 2px 0px !important;
            }
            .value-bar {
                border-radius: var(--value-bar-border-radius-amount) !important;
            }
            // .value-leader-wrap {
            // }
        }
    }
    .l2-bottom {
        padding-top: 12px;
        .dingyi {
            color: #808080;
            margin-bottom: 12px;
        }
        .biaoxian {
            display: flex;
            div {
                flex-shrink: 0;
            }
            .biaoxian-inner {
                flex-basis: 0;
                flex-grow: 1;
            }
        }
    }
}

.part-item {
    & + .part-item {
        margin-top: 12px;
    }
    & + .part-item-with-l1 {
        margin-top: 32px;
    }
}
</style>
