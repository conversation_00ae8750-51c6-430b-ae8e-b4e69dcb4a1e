<template>
    <div class="coordinate-container">
        <!-- <div class="axis-desc axis-y-desc">成长力 + 思维力</div>
        <div class="axis-desc axis-x-desc">成长力+领导力</div>
        <div class="axis-label axis-label-x axis-label-l1">低</div>
        <div class="axis-label axis-label-x axis-label-l2">中</div>
        <div class="axis-label axis-label-x axis-label-l3">较高</div>
        <div class="axis-label axis-label-x axis-label-l4">高</div>
        <div class="axis-label axis-label-y axis-label-t1">高</div>
        <div class="axis-label axis-label-y axis-label-t2">较高</div>
        <div class="axis-label axis-label-y axis-label-t3">中</div>
        <div class="axis-label axis-label-y axis-label-t4">低</div>
        <div class="axis-wrap axis-x-wrap"></div>
        <div class="axis-wrap axis-y-wrap"></div> -->
        <SvgIcon :icon-string="coordinateSvgString" :width="380" :height="379" class="svg"></SvgIcon>
        <div class="box-wrapper">
            <div
                class="sub-wrap"
                v-for="(value, key) of renderData"
                :style="{
                    '--font-color': key === props.data.talentEvaluation.talentType ? '#fff' : typeColor[key].font,
                    '--button-fill-color': key === props.data.talentEvaluation.talentType ? typeColor[key].buttonFill : '#fff',
                    '--button-border-color': typeColor[key].buttonBorder,
                    '--cube-bg': key === props.data.talentEvaluation.talentType ? typeColor[key].cubeBg : '#f2f2f2',
                    '--point-border': typeColor[key].pointBorder,
                    '--point-bg': typeColor[key].pointBg,
                }"
            >
                <div class="square-self" v-for="square of value">
                    <div class="point" v-if="square.x === props.data.talentEvaluation.XLevel && square.y === props.data.talentEvaluation.YLevel"></div>
                </div>
                <div class="type-button">{{ key }}</div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">

import SvgIcon from '@packages/exam-report/components/svg-icon/index.vue';
import coordinateSvgString from '../assets/coordinate.svg?raw';
import { typeColor } from '../util/config';
import type { IData } from '../type/type';

defineOptions({
    name: 'Coordinate',
});
const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        default: () => ({}),
    },
});
type type = keyof typeof typeColor;
const typeKeys = Object.keys(typeColor) as (keyof typeof typeColor)[];
console.log('%c [ typeKeys ]-53', 'font-size:13px; background:pink; color:#bf2c9f;', typeKeys);
const renderData: Record<keyof typeof typeColor, { x: number; y: number }[]> = {
    专家型人才: [
        { x: 1, y: 4 },
        { x: 2, y: 4 },
        { x: 1, y: 3 },
        { x: 2, y: 3 },
    ],
    全能型人才: [
        { x: 3, y: 4 },
        { x: 4, y: 4 },
        { x: 3, y: 3 },
        { x: 4, y: 3 },
    ],
    成长预备军: [
        { x: 1, y: 2 },
        { x: 2, y: 2 },
        { x: 1, y: 1 },
        { x: 2, y: 1 },
    ],
    管理型人才: [
        { x: 3, y: 2 },
        { x: 4, y: 2 },
        { x: 3, y: 1 },
        { x: 4, y: 1 },
    ],
} as const;
const localConfig = computed(() => typeColor[props.data.talentEvaluation.talentType]);
</script>
<style lang="less" scoped>
.coordinate-container {
    position: relative;
    margin-top: 20px;
    padding-left: 153px;
    // .axis-desc {
    //     background: #00bebd;
    //     border-radius: 117px;
    //     color: #fff;
    //     font-family: var(--FZLanTingHeiS-M-GB);
    //     position: absolute;
    //     &.axis-x-desc {
    //         padding: 8px 16px;
    //         top: 363px;
    //         left: 325px;
    //     }
    //     &.axis-y-desc {
    //         width: 36px;
    //         text-align: center;
    //         padding: 16px 8px;
    //         top: 85px;
    //         left: 153px;
    //     }
    // }
    // .axis-label {
    //     position: absolute;
    //     color: #4d4d4d;
    //     font-size: 12px;
    //     line-height: 14px;
    //     &.axis-label-x {
    //         top: 329px;
    //         &.axis-label-l1 {
    //             left: 289px;
    //         }
    //         &.axis-label-l2 {
    //             left: 353px;
    //         }
    //         &.axis-label-l3 {
    //             left: 413px;
    //         }
    //         &.axis-label-l4 {
    //             left: 483px;
    //         }
    //     }
    //     &.axis-label-y {
    //         width: 12px;
    //         left: 210px;
    //         &.axis-label-t1 {
    //             top: 60px;
    //         }
    //         &.axis-label-t2 {
    //             top: 117px;
    //         }
    //         &.axis-label-t3 {
    //             top: 190px;
    //         }
    //         &.axis-label-t4 {
    //             top: 254px;
    //         }
    //     }
    // }
    // .axis-wrap {
    //     position: absolute;
    // }
    .svg {
        display: block;
    }
    .box-wrapper {
        --gap-small: 1px;
        --gap-large: 2px;
        --square-size: 64px;
        position: absolute;
        width: calc(var(--square-size) * 4 + var(--gap-small) * 2 + var(--gap-large));
        height: calc(var(--square-size) * 4 + var(--gap-small) * 2 + var(--gap-large));
        top: 15px;
        left: 263px;
        display: flex;
        flex-wrap: wrap;
        gap: var(--gap-large);
        .sub-wrap {
            display: flex;
            flex-wrap: wrap;
            gap: var(--gap-small);
            width: calc(var(--square-size) * 2 + var(--gap-small));
            position: relative;
            .square-self {
                width: var(--square-size);
                height: var(--square-size);
                background-color: var(--cube-bg);
                display: flex;
                align-items: center;
                justify-content: center;
                .point {
                    width: 26px;
                    height: 26px;
                    border-radius: 50%;
                    background-color: var(--point-bg);
                    border: 7px solid var(--point-border);
                    background-clip: content-box;
                }
            }
            .type-button {
                font-family: 'PingFang SC';
                font-size: 12px;
                line-height: 20px;
                color: var(--font-color);
                padding: 3px 11px;
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                background: var(--button-fill-color);
                border: 1px solid var(--button-border-color);
                border-radius: 51px;
                white-space: nowrap;
            }
        }
    }
}
</style>
