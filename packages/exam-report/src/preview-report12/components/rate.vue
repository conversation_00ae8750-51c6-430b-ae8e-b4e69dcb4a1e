<template>
    <div class="rate-container" :style="{ gap: `${props.gap}px` }">
        <SvgIcon :icon-string="StarSvg" :width="14" :height="14" v-for="item of max" :style="{ color: item <= modelValue ? props.color.front : props.color.back }"></SvgIcon>
    </div>
</template>
<script setup lang="ts">

import SvgIcon from '@packages/exam-report/components/svg-icon/index.vue';
import StarSvg from '../assets/star.svg?raw';

defineOptions({
    name: 'Rate',
});
const props = defineProps({
    modelValue: {
        type: Number,
        default: 1,
    },
    max: {
        type: Number,
        default: 4,
    },
    gap: {
        type: Number,
        default: 2,
    },
    color: {
        type: Object as PropType<{ front: string; back: string }>,
        default: {
            front: '#F24955',
            back: '#DFDFDF',
        },
    },
});
</script>
<style lang="less" scoped>
.rate-container {
    display: flex;
}
</style>
