import { parser } from 'posthtml-parser';

interface ITreeDataItem {
    attrs?: {
        class?: string;
        style?: string;
    };
    content?: Array<ITreeDataItem> | Array<string>;
    tag: string;
}
type ITreeDataList = Array<ITreeDataItem>;

function useSplitDom(domString: string) {
    const domTree: ITreeDataList = parser(domString) as ITreeDataList;
    let result: Array<HTMLDivElement> = [];
    let styleString: string = '';
    function flatDomTree(treeData: ITreeDataList, treeDataItemParent?: ITreeDataItem) {
        for (let i = 0; i < treeData.length; i++) {
            const treeDataItem = treeData[i];
            if (['p', 'img', 'br'].includes(treeDataItem.tag)) {
                // 创建一个父级标签
                const divDom: HTMLDivElement = document.createElement('div');
                divDom.dataset.isRichText = 'true';
                if (['p'].includes(treeDataItem.tag)) {
                    if (treeDataItem.attrs?.style) {
                        treeDataItem.attrs.style += 'position: relative;';
                        divDom.setAttribute('style', treeDataItem.attrs?.style);
                    }
                    result.push(divDom);
                    treeDataItem.content && flatDomTree(treeDataItem.content as ITreeDataList, treeDataItem);
                }
                if (['img', 'br'].includes(treeDataItem.tag)) {
                    const dom = document.createElement(treeDataItem.tag);
                    if (treeDataItem.attrs) {
                        for (let [key, value] of Object.entries(treeDataItem.attrs)) {
                            if (key === 'src') {
                                value = value.replace('&amp;', '&');
                            }
                            dom.setAttribute(key, value);
                        }
                    }
                    divDom.style.display = 'flex';
                    divDom.style.justifyContent = 'center';
                    divDom.appendChild(dom);
                    result.push(divDom);

                    // 补一个 div，放同一个 p 标签下，['img', 'br']标签之后的文本标签
                    if (treeData[i + 1] && !['img', 'br'].includes(treeData[i + 1].tag)) {
                        const divDomForNext: HTMLDivElement = document.createElement('div');
                        divDomForNext.dataset.isRichText = 'true';
                        if (treeDataItemParent && ['p'].includes(treeDataItemParent.tag)) {
                            if (treeDataItemParent.attrs?.style) {
                                divDomForNext.setAttribute('style', treeDataItemParent.attrs?.style);
                            }
                            result.push(divDomForNext);
                        }
                    }
                }
            } else {
                if (treeDataItem.attrs?.style) {
                    styleString += treeDataItem.attrs?.style;
                    // styleString = styleString.replace('white-space: pre-wrap;', '');
                    if (!styleString.includes('line-height')) {
                        styleString += 'line-height: 26px;';
                    }
                }
                if (treeDataItem.tag === 'u') {
                    styleString += 'text-decoration: underline;';
                }
                if (treeDataItem.tag === 'b' || treeDataItem.tag === 'strong') {
                    styleString += 'font-weight: bold;';
                }
                if (treeDataItem.tag === 'i' || treeDataItem.tag === 'em') {
                    styleString += 'font-style: italic;';
                }
                if (treeDataItem.content) {
                    if (typeof treeDataItem.content[0] === 'string') {
                        const singleStringArray = treeDataItem.content[0].split('').map((x) => `<span style='${styleString}'>${x}</span>`);
                        result[result.length - 1].innerHTML += singleStringArray.join('');
                        styleString = '';
                    } else {
                        flatDomTree(treeDataItem.content as ITreeDataList);
                    }
                }
            }
        }
    }


    return {
        domTree,
        result,
        flatDomTree
    };
}

export { useSplitDom };
// flatDomTree(domTree);
// console.log(result.map((x) => x.outerHTML).join(''));
// result = [];