<template>
    <svg width="28px" height="28px" viewBox="0 0 28 28" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <defs>
            <circle id="path-1" cx="10" cy="10" r="10"></circle>
            <filter x="-37.5%" y="-32.5%" width="165.0%" height="165.0%" filterUnits="objectBoundingBox" id="filter-2">
                <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
                <feGaussianBlur stdDeviation="2" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
                <feComposite in="shadowBlurOuter1" in2="SourceAlpha" operator="out" result="shadowBlurOuter1"></feComposite>
                <feColorMatrix values="0 0 0 0 0.105377354   0 0 0 0 0.7005409   0 0 0 0 0.981048882  0 0 0 0.382184222 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
            </filter>
        </defs>
        <g id="------领导力测评（正在做）" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="4、part1" transform="translate(-477.000000, -202.000000)">
                <g id="编组-5" transform="translate(482.000000, 206.000000)">
                    <g id="椭圆形">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <circle stroke="#FFFFFF" stroke-width="1" stroke-linejoin="square" fill="#1AB3FA" fill-rule="evenodd" cx="10" cy="10" r="9.5"></circle>
                    </g>
                    <g id="跑步" transform="translate(5.000000, 5.000000)" fill="#FFFFFF" fill-rule="nonzero">
                        <path
                            d="M4.48742525,1.2828906 C4.61156814,1.22496177 4.75957975,1.22121908 4.89346507,1.28466938 L6.95141374,2.29490736 C6.98353526,2.31013024 7.01315779,2.32848974 7.04010377,2.3494552 C7.06735213,2.36620425 7.09275778,2.38639726 7.11563407,2.41016648 C7.22332957,2.52106134 7.25238649,2.68535934 7.21028194,2.85109107 C7.20283233,2.88127396 7.19255935,2.91046336 7.17933279,2.93901763 L7.20511943,2.87017872 C7.17235833,2.98437255 7.10596248,3.09830011 7.00837632,3.19497133 L5.22,4.937 L6.78717376,5.79515945 C7.00168273,5.90966265 7.10231915,6.15810505 7.03493855,6.37737262 L7.00168928,6.4579118 C6.98057484,6.49831432 6.95445735,6.53434367 6.9244532,6.56567385 L6.91118349,6.58503948 L5.42471648,8.74282327 C5.25544895,8.99369187 4.94039218,9.07505358 4.72252823,8.92418878 C4.50299499,8.77333391 4.46444386,8.44788705 4.63371139,8.19702839 L5.873,6.397 L4.24993817,5.5086951 C4.0354292,5.39418308 3.93479278,5.14574754 4.00218027,4.92647464 L4.00934866,4.90561545 C4.04200796,4.79108597 4.10850679,4.67677355 4.20637912,4.57981883 L5.951,2.878 L4.688,2.259 L3.38017134,3.02377604 C3.17758536,3.14129851 2.92335057,3.09274673 2.7763694,2.91741342 L2.72658933,2.84579729 C2.59419381,2.61526912 2.67296536,2.31863455 2.90255372,2.18473335 L4.36054435,1.33212376 C4.3858676,1.31743345 4.41199786,1.30533805 4.43862478,1.29576082 Z M3.47709904,5.28176226 L4.20636931,5.90845206 L3.52755608,6.72006766 C3.47715555,6.78621842 3.41294457,6.84680668 3.33660248,6.89692936 L0.842925922,8.50890834 C0.554686044,8.69705731 0.2061057,8.66824501 0.0619857608,8.44449036 C-0.0821439975,8.21905723 0.0334897424,7.8851385 0.32172962,7.69698954 L2.787,6.102 L3.47709904,5.28176226 Z M9.21716143,2.28642556 C9.37636403,2.12540048 9.65623774,2.1491376 9.84392654,2.33727664 C10.029946,2.52543553 10.0534145,2.81020154 9.89420208,2.97122662 L8.50324877,4.34929067 C8.39592916,4.45783882 8.23377413,4.48243177 8.08198745,4.42929732 L8.04900581,4.41645011 C7.98744513,4.38999708 7.92843324,4.35051654 7.87648366,4.29843959 C7.86365759,4.28547663 7.85160622,4.27206844 7.84033437,4.25829365 L7.05380105,3.6587788 L7.69395006,2.79688292 L8.264,3.23 Z M7.15746152,0.139743557 C7.47577482,-0.0465811858 7.86828151,-0.0465811858 8.18659481,0.139743557 C8.50490811,0.326068301 8.70046042,0.670288733 8.69932831,1.04227265 C8.70046042,1.41425657 8.50490811,1.758477 8.18659481,1.94480174 C7.86828151,2.13112649 7.47577482,2.13112649 7.15746152,1.94480174 C6.83914822,1.758477 6.64359591,1.41425657 6.64472802,1.04227265 C6.64359591,0.670288733 6.83914822,0.326068301 7.15746152,0.139743557 Z"
                            id="形状结合"
                        ></path>
                    </g>
                </g>
            </g>
        </g>
    </svg>
</template>
<script setup lang="ts">

defineOptions({
    name: 'SvgRunningMan'
});
const props = defineProps({
    propName: {
        type: Object,
        default: () => ({})
    }
});
</script>
