<template>
    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="9.9854" height="9.8095" viewBox="0 0 9.9854 9.8095">
        <path transform="matrix(1 0 0 1 0.000183105 0)" d="M4.99595 0C5.09191 2.67635 7.285 4.80955 9.98518 4.80955C7.2272 4.8136 4.99268 7.05062 4.99268 9.80955L4.9925 9.80955C4.9925 7.04812 2.76142 4.80955 0 4.80954C2.69439 4.8056 4.8892 2.67758 4.99595 0Z" fill-rule="evenodd" fill="rgb(30, 30, 30)"/>
    </svg>
</template>
<script setup lang="ts">

defineOptions({
    name: 'SvgStarFicker'
});
</script>
