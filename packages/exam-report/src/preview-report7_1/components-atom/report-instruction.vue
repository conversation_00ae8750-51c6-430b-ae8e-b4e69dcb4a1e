<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="阅读指引" style="margin-bottom: 22px" />
    <RichText :domString="data.testDescription" :richTextIndex="1" />
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import RichText from '@packages/exam-report/components/rich-text/index.vue';
import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import type { IData } from '../type/type';

defineOptions({
    name: 'ReportInstruction',
    inheritAttrs: false,
});

const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        default: () => ({}),
    },
    moduleCodes: {
        type: Object,
        default: () => [],
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean; footer: boolean }>,
    },
});
</script>
