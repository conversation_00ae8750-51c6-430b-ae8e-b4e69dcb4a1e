<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="组织管理建议" style="margin-top: 35px; margin-bottom: 11px;" />
    <SubTitle>
        沟通建议
    </SubTitle>
    <Suggestions :userName="userName" :data="data" />
    <Collaborate :data="data" />
    <TaskManagement :userName="userName" :data="data" />
    <LeaderSuggestions :userName="userName" :data="data" />
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import type { IData } from '../type/type'

import Collaborate from '../components/collaborate.vue'
import LeaderSuggestions from '../components/leader-suggestions.vue'
import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import SectionTitle from '../components/section-title.vue'
import SubTitle from '../components/sub-title.vue'
import Suggestions from '../components/suggestions.vue'

import TaskManagement from '../components/task-management.vue'

defineOptions({
    name: 'ChosenAdvice',
    inheritAttrs: false,
})

const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        required: true,
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})

const userName = computed(() => {
    const list = props.data?.examineeInfoList || []
    const nameData = list.find(item => item.encryptFieldId === 'cb1494e0c1011eccynU~')
    const userCode = list.find(item => item.encryptFieldId === '6b7ef36aa852049bynM~')
    return nameData?.fieldValue || userCode?.fieldValue || '参评者'
})

function initTable() {
    const dom = document.querySelectorAll('div[data-is-page-wrapper]')
    dom.forEach((item) => {
        const domList = []
        item.childNodes.forEach((item) => {
            const classNmae = item.getAttribute('class')
            if (classNmae && classNmae.split(' ').includes('table-for')) {
                domList.push(item)
            }
        })
        if (domList.length) {
            domList[domList.length - 1].setAttribute('data-table-last', 'true')
        }
    })
}
onMounted(() => {
    setTimeout(() => {
        initTable()
    }, 0)
})
</script>
