<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="类型描述" style="margin-bottom: 22px" />
    <div
        class="overview-wrap"
        :style="{
            background: backgroundColor.bgcolor,
        }"
    >
        <div
            class="illustration"
            :style="{
                backgroundImage: `url(${illustration})`,
            }"
        />
        <div class="info-list">
            <div v-for="item in dimensionScoreList" :key="item.personalityCode" class="info-item">
                <p class="tit">
                    {{ item.dimensionDesc }}
                </p>
                <p class="tag-info">
                    <span :class="{ flag: item.isLeft }">{{ item.leftCode }}{{ item.leftName }}</span>
                    <span :class="{ flag: item.isRight }">{{ item.rightCode }}{{ item.rightName }}</span>
                </p>
                <div class="bar">
                    <p :class="{ flag: item.isLeft }" :style="{ width: `${item.leftPercent}%` }">
                        <span>{{ item.leftPercent }}%</span>
                    </p>
                    <p :class="{ flag: item.isRight }">
                        <span>{{ item.rightPercent }}%</span>
                    </p>
                </div>
            </div>
        </div>
    </div>
    <RichText :domString="data.dimensionScoreIntroduction" :richTextIndex="199" />
    <PageFooter :info="data" />

    <!-- 第二页 -->
    <PageHeader />
    <FourKnight :info="data.personalityDescResult.dimensionScoreList || []" />
    <SubTitle title="类型特点" style="margin-top: 23px; margin-bottom: 18px;" />
    <CharacteKeyWord :data="data" />
    <PageFooter :info="data" />

    <CareerCharacter :data="data" />
    <CareerPrefer :data="data" :headerFooterConfig="headerFooterConfig" />
</template>

<script setup lang="ts">

import RichText from '@packages/exam-report/components/rich-text/index.vue'

import type { IData } from '../type/type'
import CareerCharacter from '../components/career-character.vue'
import CareerPrefer from '../components/career-prefer.vue'
import CharacteKeyWord from '../components/character-key-word.vue'
import FourKnight from '../components/four-knight.vue'
import PageFooter from '../components/page-footer.vue'
import PageHeader from '../components/page-header.vue'
import SectionTitle from '../components/section-title.vue'
import SubTitle from '../components/sub-title.vue'

import { colorConfig } from '../util/color-config'

defineOptions({
    name: 'ComprehensiveResult',
    inheritAttrs: false,
})

const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        default: () => ({}),
    },
    moduleCodes: {
        type: Object,
        default: () => [],
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
const index = ref(0)

const illustration = computed(() => {
    const { personalityType } = props.data.personalityDescResult
    return colorConfig.imgUrlMap[personalityType]
})

const backgroundColor = computed(() => {
    const { personalityType } = props.data.personalityDescResult
    const data = colorConfig.getCharacterColor(personalityType)
    return {
        ...data,
        bgcolor: `linear-gradient(90deg, ${data.linearGradientColorStart} 44%, ${data.linearGradientColorEnd} 76%)`,
    }
})

const dimensionScoreList = computed(() => {
    const list = props.data.personalityDescResult.dimensionScoreList ?? []
    return list.map((item, index) => {
        return {
            ...item,
            isLeft: item.leftCode === item.personalityCode && item.leftPercent > 0,
            isRight: item.rightCode === item.personalityCode && item.rightPercent > 0,
        }
    })
})
</script>

<style lang="less" scoped>
@import url(../assets/mixins.less);

.overview-wrap {
    margin-bottom: 22px;
    display: flex;
    align-items: center;
    background: linear-gradient(
        89.99999deg,
        #e3fcff 44%,
        rgba(227, 252, 255, 0) 76%
    );
    font-family: var(--FZLanTingHeiS-R-GB);
    font-style: normal;
    font-weight: 400;
    line-height: normal;

    .illustration {
        width: 292px;
        height: 432px;
        background-repeat: no-repeat;
        background-size: 100% auto;
    }

    .info-list {
        margin-left: 21px;
        flex: 1;

        .tit {
            margin-bottom: 10px;
            color: #000;
            font-family: var(--FZLanTingHeiS-R-GB);
            font-size: 13px;
        }

        .info-item {
            margin-bottom: 18px;

            &:last-child {
                margin-bottom: 0;
            }

            .tag-info {
                color: #979797;
                font-size: 12px;
                display: flex;
                justify-content: space-between;

                span.flag {
                    color: #00bebd;
                }
            }

            .bar {
                margin-top: 3px;
                display: flex;
                background: #ececec;
                border-radius: 2px;
                height: 16px;
                overflow: hidden;

                & > p {
                    padding-left: 2px;
                    padding-right: 2px;
                    min-width: 46px;
                    &.flag {
                        // flex: 1;
                        background: #00bebd;

                        & > span {
                            color: #ffffff;
                        }
                    }

                    &:last-child {
                        flex: 1;
                        text-align: right;
                    }

                    & > span {
                        color: #1f1f1f;
                        font-size: 13px;
                    }
                }
            }
        }
    }
}

.overview-desc {
    color: #808080;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 24px;
    span {
        &:first-child {
            font-family: var(--FZLanTingHeiS-DB-GB);
        }

        &:last-child {
            font-family: var(--FZLanTingHeiS-L-GB);
        }
    }
}
</style>
