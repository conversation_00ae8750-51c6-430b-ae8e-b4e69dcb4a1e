<template>
    <PageHeader v-if="headerFooterConfig?.header"></PageHeader>
    <SectionTitle title="附录" style="margin-top: 34px; margin-bottom: 19px"></SectionTitle>
    <div style="margin-top: 23px">
        <SubTitle style="margin-bottom: 10px">报告参考性<LevelIcon :level="data.indicativeResultDesc"></LevelIcon></SubTitle>
        <RefrenceLevel :info="data" />
    </div>
    <div style="margin-top: 34px"></div>
    <RichText :domString="data.aboutThisReport" :richTextIndex="6"></RichText>
    <PageFooter :info="data" v-if="headerFooterConfig?.footer"></PageFooter>
</template>
<script setup lang="ts">

import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import SubTitle from '../components/sub-title.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';
import RefrenceLevel from '../components/reference-level.vue';
import LevelIcon from '../components/level-icon.vue';

import { IData } from '../type/type';

defineOptions({
    name: 'Attach',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        default: () => ({}),
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean; footer: boolean }>,
    },
});
</script>
