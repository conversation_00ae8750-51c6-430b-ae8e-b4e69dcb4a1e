<template>
    <div
        class="cover-page single-page-wrap"
        style="background-image: url('https://img.bosszhipin.com/static/zhipin/498127918537115488.svg'); background-position: center bottom"
    >
        <div v-if="logo" class="logo-img">
            <img :src="logo">
        </div>
        <div v-else class="logo-text">
            LOGO
        </div>
        <div class="report-title">
            {{ data.templateName }}
        </div>
        <div class="report-sub-title">
            {{ data.reportSubTitle }}
        </div>
        <div class="spliter" />
        <div class="examinee-info">
            <div class="info-wrap">
                <div v-for="(item, index) of data.examineeInfoList" :key="index" class="info-item">
                    {{ item.fieldShowName }}：{{ item.fieldValue }}
                </div>
            </div>
        </div>
        <Copyright />
    </div>
</template>

<script setup lang="ts">
import Copyright from '../components/copyright.vue'

defineOptions({
    name: 'Cover',
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    logo: {
        type: String,
        default: '',
    },
})
</script>

<style lang="less" scoped>
:deep(.copyright-wrap) {
    --copy-right-color: rgba(0, 0, 0, 0.3);

    font-size: 14px !important;
    bottom: 17px !important;
}
.report-preview-page {
    .single-page-wrap.cover-page {
        padding-top: 161px;
        word-break: break-all;
        white-space: pre-wrap;
        .report-title {
            width: 608px;
            color: #151515;
            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 60px;
            line-height: 71px;
            text-align: left;
            padding: 0;
            margin-left: calc(83px - var(--page-padding-horizontal));
        }
        .report-sub-title {
            width: 576px;
            font-family: var(--FZLanTingHeiS-L-GB);
            padding: 0;
            margin-top: 5px;
            margin-left: calc(83px - var(--page-padding-horizontal));
            font-size: 24px;
            color: #151515;
            line-height: 32px;
            text-align: left;
            opacity: 0.5;
            word-break: normal;
            letter-spacing: 0.5px;
        }
        .spliter {
            width: 183px;
            height: 2px;
            background: #d8d8d8;
            opacity: 0.5;
            margin: 10px 0 10px 31px;
        }
        .examinee-info {
            font-family: var(--FZLanTingHeiS-R-GB);
            color: #333333;
            text-align: left;
            justify-content: flex-start;
            margin: 15px 0 0 calc(83px - var(--page-padding-horizontal));
            font-size: 15px;
            line-height: 24px;
            .info-wrap {
                .info-item {
                    & + .info-item {
                        margin-top: 0;
                    }
                }
            }
        }
    }
}
</style>
