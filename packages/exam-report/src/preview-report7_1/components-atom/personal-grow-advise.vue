<template>
    <PageHeader v-if="headerFooterConfig?.header" />
    <SectionTitle title="个人发展建议" style="margin-bottom: 10px" />
    <p class="text">
        <!-- 为{{ userName }}提供以下三条发展建议： -->
        为参评者提供以下三条发展建议：
    </p>
    <template v-for="(item, index) in data.developmentAdvice" :key="index">
        <p class="title">
            建议{{ index + 1 }}：{{ item.name }}
        </p>
        <SimpleText :data="item.showName"></SimpleText>
    </template>
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import PageHeader from '../components/page-header.vue'
import PageFooter from '../components/page-footer.vue'
import SectionTitle from '../components/section-title.vue'
import SimpleText from '@packages/exam-report/components/simple-text/index.vue'

import type { IData } from '../type/type'

defineOptions({
    name: 'InterviewAdvise',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        required: true,
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})

const userName = computed(() => {
    const list = props.data?.examineeInfoList || []
    const nameData = list.find(item => item.encryptFieldId === 'cb1494e0c1011eccynU~')
    return nameData?.fieldValue ?? '参评者'
})
</script>

<style lang="less" scoped>
@import url(../assets/mixins.less);
.text {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
.title {
    margin-top: 10px;
    color: #08bfbf;
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
</style>
