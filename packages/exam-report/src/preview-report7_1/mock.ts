export default {
    code: 0,
    message: 'Success',
    data: {
        indicativeResultDesc: '高',
        totalScore: null,
        recommendStar: null,
        competencyDetailList: null,
        indicativeList: [
            {
                encryptIndicatorId: null,
                indicatorName: null,
                showName: '作答时长',
                result: '10分0秒（正常）',
                indicatorMeaning: '',
            },
            {
                encryptIndicatorId: null,
                indicatorName: null,
                showName: '作答一致性',
                result: '正常',
                indicatorMeaning: '',
            },
            {
                encryptIndicatorId: null,
                indicatorName: null,
                showName: '中断次数',
                result: '0次（正常）',
                indicatorMeaning: '',
            },
        ],
        totalScoreLevel: 0,
        answerTimeStr: null,
        excelHeadList: null,
        encryptId: null,
        templateName: '',
        examineeInfoList: [
            {
                encryptFieldId: 'cb1494e0c1011eccynU~',
                fieldName: '姓名',
                fieldShowName: '姓名1',
                fieldValue: '张三',
            },
            {
                encryptFieldId: '2f9ff43ac13564c0ynw~',
                fieldName: '应聘职位',
                fieldShowName: '应聘职位',
                fieldValue: '管培生',
            },
            {
                encryptFieldId: 'f2b441dc736f8db41A~~',
                fieldName: '测评作答结束时间',
                fieldShowName: '测评日期',
                fieldValue: '2024-02-04',
            },
            {
                encryptFieldId: '6b7ef36aa852049bynM~',
                fieldName: '第三方唯一码',
                fieldShowName: '报告编号',
                fieldValue: '202402040001',
            },
            {
                encryptFieldId: '3c30c2010eac1ddbynV62w~~',
                fieldName: '报告参考性',
                fieldShowName: '报告参考性',
                fieldValue: '高',
            },
        ],
        testDescription: '<img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/94a26b4448da790b3nN-0926/ff13bde9-41c5-49b0-8a22-7243218acd50" width="2000" height="2000" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:3513,&quot;defaultWidth&quot;:6989,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}">',
        useIntroduction: '测评指南',
        indicativeIntroduction: '注释：\n报告参考性包括作答时长、作答一致性、中断次数三个指标，判断标准为：\n1、作答时长：正常：>=2分钟且<=20分钟；异常：<2分钟或>20分钟。\n2、作答一致性：在相似题目上作答前后不一致的情况>50%，则显示异常；<=50%，显示正常。\n3、中断次数：正常：0-5次；异常：>5次。\n若出现任意1项为异常，则报告参考性中；2项及以上为异常，则报告参考性低，报告结果需谨慎参考；3项均正常，则报告参考性高。',
        scoreSummarize: null,
        advantageNameList: null,
        weaknessNameList: null,
        dimensionLevelList: null,
        referenceValueName: null,
        groupReferenceValueList: null,
        competencyDetailIntroduction: null,
        dimensionScoreList: null,
        interviewAdviceIntroduction: null,
        interviewAdviceContentList: null,
        dimensionInfoList: null,
        reportLogo: null,
        dimensionOriMap: null,
        dimensionChangeMap: null,
        dimensionShowMap: null,
        dimensionStandardMap: null,
        watermark: 2,
        examineeId: 0,
        examineeName: null,
        reportSubTitle: '16T通用版副标题',
        reportReadImgUrl: '',
        comprehensiveResult: {
            personalityType: 'ISTJ',
            personalityName: null,
            personalityDesc: null,
            professionMatch: '通用技术岗',
            score: null,
            level: null,
            professionalQualityLevel: null,
            professionalInterestLevel: null,
        },
        personalityDescResult: {
            personalityType: 'ISTJ',
            personalityName: 'ISTJ',
            personalityTendency: '健康、1、11111、J名',
            dimensionScoreList: [
                {
                    personalityCode: 'I',
                    dimensionDesc: '',
                    leftCode: 'E',
                    rightCode: 'I',
                    leftName: '健康',
                    rightName: '健康',
                    leftTendencyDesc: '描述',
                    rightTendencyDesc: '描述',
                    leftPercent: '10.0',
                    rightPercent: '90.0',
                    dimensionPerformance: [
                        'I9',
                        'I1',
                        'I3',
                        'I4',
                        'I5',
                    ],
                    communicationAdvice: [
                        'I喜欢描述语句2',
                        'I喜欢描述语句3',
                    ],
                },
                {
                    personalityCode: 'S',
                    dimensionDesc: '',
                    leftCode: 'S',
                    rightCode: 'N',
                    leftName: '1',
                    rightName: '1',
                    leftTendencyDesc: '1',
                    rightTendencyDesc: '1',
                    leftPercent: '80.0',
                    rightPercent: '20.0',
                    dimensionPerformance: [
                        'S9',
                        'S4',
                        'S8',
                        'S5',
                        'S1',
                    ],
                    communicationAdvice: [
                        'S喜欢描述语句2',
                        'S喜欢描述语句4',
                    ],
                },
                {
                    personalityCode: 'T',
                    dimensionDesc: '',
                    leftCode: 'T',
                    rightCode: 'F',
                    leftName: '11111',
                    rightName: '11111',
                    leftTendencyDesc: '1111111',
                    rightTendencyDesc: '1切饿柔肤水的方法没',
                    leftPercent: '70.0',
                    rightPercent: '30.0',
                    dimensionPerformance: [
                        'T7',
                        'T2',
                        'T8',
                        'T5',
                        'T6',
                    ],
                    communicationAdvice: [
                        'T喜欢描述语句1',
                        'T喜欢描述语句2',
                    ],
                },
                {
                    personalityCode: 'J',
                    dimensionDesc: '',
                    leftCode: 'J',
                    rightCode: 'P',
                    leftName: 'J名',
                    rightName: 'P名',
                    leftTendencyDesc: '羊肉汤',
                    rightTendencyDesc: '还挺好',
                    leftPercent: '60.0',
                    rightPercent: '40.0',
                    dimensionPerformance: [
                        'J7',
                        'J4',
                        'J3',
                        'J1',
                        'J2',
                    ],
                    communicationAdvice: [
                        'J喜欢描述语句2',
                        'J喜欢描述语句4',
                    ],
                },
            ],
            personalityKeyWord: '类型特点关键词1',
            personalityPortraitList: null,
            typeFeatureDesc: '类型特点描述1',
        },
        dimensionScoreIntroduction: '<p class="lext-theme-paragraph"><span style="white-space: pre-wrap;">1</span></p><img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/5758f89e739f65073nN-0921/834b002a-bfd8-4881-9cfc-32e4a357dfa6" width="2000" height="2000" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:3513,&quot;defaultWidth&quot;:6989,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}"><p class="lext-theme-paragraph"><br></p>',
        professionMatchResult: null,
        professionalQualityStarIntr: '',
        suggestResult: null,
        pickSuggestionIntroduction: '',
        professionPersonalityTypeList: [
            {
                name: '职业气质1',
                showName: '职业气质1-展示',
            },
            {
                name: '职业气质2',
                showName: '职业气质2-展示',
            },
            {
                name: '职业气质3',
                showName: '职业气质3-展示',
            },
            {
                name: '职业气质4',
                showName: '职业气质4-展示',
            },
        ],
        oriProfessionPersonalityType: '职业气质1',
        professionTemperamentMainFeatureList: [
            '主要特征8-1',
            '主要特征7-1',
            '主要特征6-1',
            '主要特征3-1',
            '主要特征2-1',
            '主要特征1-1',
        ],
        advantageAbilityList: [
            {
                name: '优势能力标题1-1',
                showName: '优势能力正文1-1',
            },
            {
                name: '优势能力标题1-8',
                showName: '优势能力正文1-8',
            },
            {
                name: '优势能力标题1-4',
                showName: '优势能力正文1-4',
            },
            {
                name: '优势能力标题1-2',
                showName: '优势能力正文1-2',
            },
        ],
        advantageBehaviorList: null,
        professionBlindPointList: null,
        professionValues: '职业价值观1',
        professionValuesDesc: '<p class="lext-theme-paragraph"><span style="white-space: pre-wrap;">1</span></p><img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/cee374bac00db9433nN-0920/8a2297ef-6521-4a72-ae99-5b5d55ad8331" width="2000" height="2000" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:3513,&quot;defaultWidth&quot;:6989,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}"><p class="lext-theme-paragraph"><br></p>',
        preferenceFieldIntr: '偏好领域概述1',
        preferenceFieldList: [
            {
                fieldType: '1',
                professionContent: '1',
                fieldDesc: '1',
            },
            {
                fieldType: '1',
                professionContent: '1',
                fieldDesc: '1',
            },
        ],
        aboutPreferenceField: '<p class="lext-theme-paragraph"><span style="white-space: pre-wrap;">1</span></p><img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/6f626db3ee3be9d83nN-09y9/1475d6f7-2096-49c2-86a8-48e0b4b69038" width="2000" height="2000" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:3513,&quot;defaultWidth&quot;:6989,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}"><p class="lext-theme-paragraph"><br></p>',
        likeWorkScene: [
            'I4',
            'S2111111111111111',
            'T1',
            'J3',
        ],
        disLikeWorkScene: [
            'I不喜欢描述语句1',
            'S不喜欢描述语句6',
            'T不喜欢描述语句5',
            'J不喜欢描述语句1',
        ],
        sameFrequencyPartner: [
            {
                name: 'ISTJ',
                showName: 'ISTJ',
            },
            {
                name: 'ISTJ',
                showName: 'ISTJ',
            },
            {
                name: 'ISTJ',
                showName: 'ISTJ',
            },
        ],
        differencePartner: [
            {
                name: 'ISTJ',
                showName: 'ISTJ',
            },
            {
                name: 'ISTJ',
                showName: 'ISTJ',
            },
            {
                name: 'ISTJ',
                showName: 'ISTJ',
            },
        ],
        taskManagerAdviceIntroduction: '<p class="lext-theme-paragraph"><span style="white-space: pre-wrap;">1</span></p><img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/59b81e5a19ddeedd3nN-09y8/28713eae-4eff-4ace-8926-d9f669e7b71a" width="1440" height="960" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:960,&quot;defaultWidth&quot;:1440,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}"><p class="lext-theme-paragraph"><br></p>',
        likeTaskFeature: null,
        likeLeaderStyle: '指令型',
        leaderStyleAdviceIntroduction: '<p class="lext-theme-paragraph"><span style="white-space: pre-wrap;">11</span></p><img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/2622cdfa99a756473nN-09y_/19f817c6-ade2-4f47-a667-b906c7801a99" width="1920" height="1200" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:1200,&quot;defaultWidth&quot;:1920,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}"><p class="lext-theme-paragraph"><br></p>',
        developmentAdvice: null,
        answerStartTime: '2024-01-01 00:00:00',
        answerEndTime: '2024-01-01 00:10:00',
        aboutThisReport: '<p class="lext-theme-paragraph"><span style="white-space: pre-wrap;">1</span></p><img src="https://zhice-qa.weizhipin.com/wapi/cadmin/file/download/auth/456f9305ddce38083nN-09y-/ebde78ba-6253-47f9-82aa-c7cb6df76777" width="2000" height="1600" format="" data-lexical-img="" data-lexical-img-config="{&quot;defaultHeight&quot;:1600,&quot;defaultWidth&quot;:2560,&quot;heightLimit&quot;:2000,&quot;widthLimit&quot;:2000}"><p class="lext-theme-paragraph"><br></p>',
        breakTimes: 0,
        answerConsistencyResult: '正常',
        answerTimeResult: '正常',
        recommendResult: null,
    },
}
