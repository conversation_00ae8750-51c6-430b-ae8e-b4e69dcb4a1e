<template>
    <PageHeader />
    <SectionTitle title="职业性格" style="margin-top: 34px; margin-bottom: 22px" />
    <p class="text-desc">
        职业性格指该性格类型在职场中的综合特征，包含<span>职业气质、职业表现</span>两方面。职业气质是相对稳定的、先天的内在心理特征；职业表现反映了该性格类型在职场中的特有表现，是职业气质的外显反应。
    </p>
    <SubTitle style="margin-top: 23px">
        职业气质
    </SubTitle>
    <p class="text-desc">
        {{ userName }}的职业气质类型为：
        <span class="highlight" :style="{ color: flagData.color }">{{ flagData.name }}</span>
    </p>
    <div class="class-illustration-wrap">
        <div class="illustration">
            <div
                v-for="item, index in data.professionPersonalityTypeList"
                :key="item.name"
                :class="{
                    act: item.name === data.oriProfessionPersonalityType,
                }"
            >
                <p>{{ item.showName }}</p>
            </div>
        </div>
    </div>
    <p class="text-desc">
        该气质类型在职场的<span>主要特征</span>为：
    </p>
    <div
        v-for="item, index in data.professionTemperamentMainFeatureList"
        :key="item"
        class="illustration-desc text-desc"
    >
        <p class="index">
            {{ index + 1 }}<span>·</span>
        </p>
        <p>{{ item }}</p>
    </div>
    <PageFooter :info="data" />

    <!-- 第二页 -->
    <PageHeader />
    <SubTitle>职业表现</SubTitle>
    <CareerPerformance
        :userName="userName"
        :list="data.advantageAbilityList"
        typeStr="优势能力"
        color="#00BEBD"
    />
    <CareerPerformance
        :userName="userName"
        :list="data.advantageBehaviorList"
        typeStr="优势行为表现"
        color="#00BEBD"
        :showType="1"
    />
    <CareerPerformance
        :userName="userName"
        :list="data.professionBlindPointList"
        preTypeStr="要注意以下"
        typeStr="职业盲点"
        color="#FC9D64"
    />
    <PageFooter :info="data" />
</template>

<script setup lang="ts">
import type { IData } from '../type/type'

import CareerPerformance from './career-performance.vue'
import PageFooter from './page-footer.vue'
import PageHeader from './page-header.vue'
import SectionTitle from './section-title.vue'
import SubTitle from './sub-title.vue'

defineOptions({
    name: 'CareerCharacter',
    inheritAttrs: false,
})
const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        required: true,
    },
})

const userName = computed(() => {
    const list = props.data?.examineeInfoList || []

    const nameData = list.find(item => item.encryptFieldId === 'cb1494e0c1011eccynU~')
    const userCode = list.find(item => item.encryptFieldId === '6b7ef36aa852049bynM~')
    return nameData?.fieldValue || userCode?.fieldValue || '参评者'
})

const flagData = computed(() => {
    const list = props.data?.professionPersonalityTypeList || []
    let flagIndex = 0
    const colorList = ['#3BBCF5', '#69C59C', '#FC9D64', '#C098FD']
    const index = list.findIndex(item => item.name === props.data.oriProfessionPersonalityType)
    if (index >= 0) {
        flagIndex = index
    }

    const newData = list[flagIndex]
    return {
        name: newData.showName,
        color: colorList[flagIndex],
    }
})
</script>

<style lang="less" scoped>
.text-desc {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    span {
        font-family: var(--FZLanTingHeiS-DB-GB);
    }

    .highlight {
        color: #3bbcf5;
        font-size: 18px;
    }
}

.class-illustration-wrap {
    padding: 22px;
    display: flex;
    justify-content: center;

    .illustration {
        width: 398px;
        height: 398px;
        background: #f3f3f3;
        border-radius: 249px;
        overflow: hidden;
        padding: 16px;

        & > div {
            float: left;
            width: 180px;
            height: 180px;
            background: #cfcfcf;
            padding-top: 100px;
            // display: flex;
            // flex-direction: column;
            // justify-content: center;
            // text-align: center;

            & > p {
                margin: 0 21px;
                color: #ffffff;
                font-family: var(--FZLanTingHeiS-DB-GB);
                font-size: 15px;
                line-height: 32px;
            }

            &:nth-child(1) {
                margin-right: 1px;
                // margin-bottom: 1px;
                padding-top: 100px;
                text-align: right;
                border-radius: 249px 0 0 0;
                &.act {
                    box-shadow: 0px 0px 10px 0px rgba(107, 146, 161, 0.6);
                    background: linear-gradient(to bottom right, #bce8ff 0%, #44b7f2 100%);
                    // background: radial-gradient(#44b7f2 0%, #bce8ff 100%);
                }
            }

            &:nth-child(2) {
                padding-top: 100px;
                border-radius: 0 249px 0 0;

                &.act {
                    box-shadow: 0px 0px 10px 0px rgba(107, 146, 161, 0.6);
                    background: linear-gradient(to bottom left, #d2ffeb 0%, #69c59c 100%);
                    // background: radial-gradient(#44b7f2 0%, #bce8ff 100%);
                }
            }

            &:nth-child(3) {
                margin-top: 1px;
                margin-right: 1px;
                text-align: right;
                padding-top: 42px;
                border-radius: 0 0 0 249px;

                &.act {
                    box-shadow: 0px 0px 10px 0px rgba(107, 146, 161, 0.6);
                    background: linear-gradient(to top right, #ffdac4 0%, #fc9d64 100%);
                }
            }

            &:nth-child(4) {
                margin-top: 1px;
                // margin-left: 1px;
                padding-top: 42px;
                border-radius: 0 0 249px 0;

                &.act {
                    box-shadow: 0px 0px 10px 0px rgba(107, 146, 161, 0.6);
                    background: linear-gradient(to top left, #e4d2ff 0%, #c098fd 100%);
                }
            }
        }
    }
}
.illustration-desc {
    display: flex;
    color: #000000;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 42px;

    .index {
        white-space: nowrap;
        color: #00bebd;
        font-family: var(--FZLanTingHeiS-R-GB);
    }
}
</style>
