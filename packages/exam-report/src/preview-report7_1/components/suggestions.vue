<template>
    <div class="suggestions-wrap">
        <p class="title">
            {{ userName }}喜欢的沟通方式为：
        </p>
        <div v-for="item, index in list" :key="index" class="item-text">
            <p class="icon" :style="{ backgroundImage: `url(${item.icon})` }" />
            <p class="text">
                <span v-for="text, i in item.textList" :key="i">{{ text }}</span>
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">

const props = defineProps({
    userName: {
        type: String,
        default: '',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
})

const list = computed(() => {
    const newList = [
        {
            icon: 'https://img.bosszhipin.com/static/zhipin/546494312337179900.png',
        },
        {
            icon: 'https://img.bosszhipin.com/static/zhipin/516494312337118959.png',
        },
        {
            icon: 'https://img.bosszhipin.com/static/zhipin/94649431233712964.png',
        },
        {
            icon: 'https://img.bosszhipin.com/static/zhipin/256494312337180307.png',
        },
    ]
    const { dimensionScoreList } = props.data.personalityDescResult

    newList.forEach((item, index) => {
        item.textList = dimensionScoreList[index].communicationAdvice
    })
    return newList
})
</script>

<style lang="less" scoped>
.suggestions-wrap {
    margin-bottom: 18px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    .title {
        margin-bottom: 23px;
    }

    .item-text {
        display: flex;
        align-items: center;
        margin-bottom: 18px;

        &:last-child {
            margin-bottom: 0;
        }

        .icon {
            margin-right: 35px;
            width: 30px;
            height: 30px;
            min-width: 30px;
            // background: linear-gradient(0deg, #c7ffff 0%, #00bebd 90%);
            text-align: center;
            background-size: 100% auto;
            background-repeat: no-repeat;
            background-position: center;
        }

        .text {
            flex: 1;
            border-bottom: 1px solid #f2f2f2;
            display: flex;
            padding-bottom: 12px;
            span {
                flex: 1;
            }
        }
    }
}
</style>
