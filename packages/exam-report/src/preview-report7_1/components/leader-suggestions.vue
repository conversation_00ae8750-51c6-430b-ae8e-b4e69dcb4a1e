<template>
    <div style="margin-top: 44px;">
        <SubTitle>
            领导风格建议
        </SubTitle>
        <p class="text">
            {{ userName }}喜欢的领导风格为：<span class="highlight">{{ typeData?.name }}（{{ typeData?.desc }})</span>
        </p>
        <div class="illustration-wrap">
            <div class="illustration">
                <div class="bg">
                    <div class="arrow-wrap vertical">
                        <div class="arrow-box">
                            <span class="line" />
                            <span class="arrow" />
                            <span class="text">关系</span>
                        </div>
                    </div>
                    <div class="arrow-wrap">
                        <div class="arrow-box">
                            <span class="line" />
                            <span class="arrow" />
                            <span class="text">任务</span>
                        </div>
                    </div>
                    <div class="content">
                        <div
                            v-for="calssVal in typeList"
                            :key="calssVal"
                            class="class-box"
                            :class="{
                                act: calssVal.name === props.data.likeLeaderStyle,
                            }"
                        >
                            <div v-for="val in 4" :key="val" class="class-box-item" />
                            <p class="tag">
                                {{ calssVal.name }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <p class="title">
        四种领导风格的表现
    </p>
    <RichText :domString="data.leaderStyleAdviceIntroduction" :richTextIndex="197" />
</template>

<script setup lang="ts">
import RichText from '@packages/exam-report/components/rich-text/index.vue'

import { colorConfig } from '../util/color-config'
import SubTitle from './sub-title.vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    userName: {
        type: String,
        default: '',
    },
})

const typeList = [
    {
        name: '支持型',
        desc: '低任务导向-高关系导向',
    },
    {
        name: '教练型',
        desc: '高任务导向-高关系导向',
    },
    {
        name: '授权型',
        desc: '低任务导向-低关系导向',
    },
    {
        name: '指令型',
        desc: '高任务导向-低关系导向',
    },
]

const typeData = computed(() => {
    const data = typeList.find(item => item.name === props.data.likeLeaderStyle)
    return data
})
</script>

<style lang="less" scoped>
.text {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    .highlight {
        color: #08bfbf;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }
}

.illustration-wrap {
    margin-top: 22px;
    display: flex;
    justify-content: center;

    .illustration {
        margin: 26px auto 0;
        // width: 354px;
        // height: 340px;
        position: relative;

        .bg {
            padding: 13px;
            width: 300px;
            height: 300px;
            position: relative;

            .arrow-wrap {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                display: flex;
                justify-content: center;
                align-items: center;

                &.vertical {
                    .arrow-box {
                        position: relative;
                        flex-direction: column-reverse;
                        height: 300px;
                        .text {
                            position: absolute;
                            top: -30px;
                            width: 40px;
                            left: 50%;
                            text-align: center;
                            transform: translateX(-50%);
                        }

                        .arrow {
                            position: absolute;
                            top: -20px;
                            left: 50%;
                            transform: translateX(-50%);
                            width: 16px;
                            height: 16px;
                            border-left: 10px solid transparent;
                            border-right: 10px solid transparent;
                            border-bottom: 16px solid #00bebd;
                        }

                        .line {
                            height: 300px;
                            width: 10px;
                            background: linear-gradient(
                                180deg,
                                #00bebd 2%,
                                rgba(0, 190, 189, 0.36) 40%,
                                rgba(0, 190, 189, 0.25) 100%
                            );
                        }
                    }
                }

                .arrow-box {
                    display: flex;
                }

                .line {
                    width: 300px;
                    height: 10px;
                    background: linear-gradient(
                        270deg,
                        #00bebd 2%,
                        rgba(0, 190, 189, 0.36) 40%,
                        rgba(0, 190, 189, 0.25) 100%
                    );
                }

                .arrow {
                    position: absolute;
                    top: 50%;
                    transform: translateY(-50%);
                    right: -10px;
                    width: 16px;
                    height: 16px;
                    border-left: 16px solid #00bebd;
                    border-top: 10px solid transparent;
                    border-bottom: 10px solid transparent;
                }

                .text {
                    position: absolute;
                    right: -46px;
                    top: 50%;
                    transform: translateY(-50%);
                    color: #000000;
                    font-family: var(--FZLanTingHeiS-M-GB);
                    font-size: 15px;
                    font-style: normal;
                    font-weight: 400;
                    line-height: normal;
                    text-align: center;
                    white-space: nowrap;
                }
            }

            .content {
                width: 100%;
                height: 100%;
                display: flex;
                flex-wrap: wrap;
                justify-content: space-between;
                align-content: space-between;

                .class-box {
                    position: relative;
                    width: 129px;
                    height: 129px;
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;
                    align-content: space-between;

                    &.act {
                        .tag {
                            color: #fff;
                            background: #08bfbf;
                            border: 1px solid #ffffff;
                        }

                        .class-box-item {
                            background: #d6ebff;
                        }
                    }

                    .tag {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        width: 60px;
                        padding: 4px 0;
                        color: #08bfbf;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 400;
                        line-height: 20px;
                        background: #ffffff;
                        border: 1px solid #08bfbf;
                        border-radius: 51px;
                        text-align: center;
                    }

                    .class-box-item {
                        width: 64px;
                        height: 64px;
                        background: #f2f2f2;
                    }
                }
            }

            // .arrow {
            //     position: absolute;
            //     top: 50%;
            //     left: 50%;
            //     transform: translate(-50%, -50%);
            //     width: 316px;
            //     height: 19px;
            //     background: linear-gradient(
            //         270deg,
            //         #00bebd 2%,
            //         rgba(0, 190, 189, 0.36) 40%,
            //         rgba(0, 190, 189, 0.25) 100%
            //     );

            //     &.line {
            //         height: 316px;
            //         width: 19px;
            //         background: linear-gradient(
            //             180deg,
            //             #00bebd 2%,
            //             rgba(0, 190, 189, 0.36) 40%,
            //             rgba(0, 190, 189, 0.25) 100%
            //         );
            //     }
            // }
        }
    }
}

.title {
    margin: 10px 0;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
.text-wrap {
    span {
        &:first-child {
            color: #1f1f1f;
            font-family: var(--FZLanTingHeiS-DB-GB);
        }
    }
}
</style>
