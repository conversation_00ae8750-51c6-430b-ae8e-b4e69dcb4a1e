<template>
    <div
        class="circle-container"
        :style="{
            width: `${size}px`,
            height: `${size}px`,
            left: `${position.left}px`,
            top: `${position.top}px`,
            backgroundColor,
        }"
    >
        <div class="text-wrap">
            {{ localContent }}
        </div>
    </div>
</template>

<script setup lang="ts">

defineOptions({
    name: 'Circle',
})
const props = defineProps({
    content: {
        type: String,
        default: '',
    },
    size: {
        type: Number,
        default: 60,
    },
    position: {
        type: Object,
        default: () => ({}),
    },
    backgroundColor: {
        type: String,
        default: '',
    },
})
const localContent = computed(() => {
    const length = props.content.length
    let breakPosition = 0
    if (length >= 7) {
        breakPosition = 4
    } else if ([5, 6].includes(length)) {
        breakPosition = 3
    } else if (length <= 4) {
        breakPosition = -1
    }
    return breakPosition > 0 ? `${props.content.slice(0, breakPosition)}\n${props.content.slice(breakPosition)}` : props.content
})
</script>

<style lang="less" scoped>
.circle-container {
    position: absolute;
    border-radius: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    white-space: pre-wrap;
    font-size: 12px;
    line-height: 14px;
}
</style>
