<template>
    <div style="margin-top: 23px">
        <SubTitle>
            偏好场景
        </SubTitle>
        <div class="preferred-scenarios">
            <div class="left">
                <div class="title">
                    <p class="icon">
                        <span />
                    </p>
                    喜欢的工作场景
                </div>
                <div v-for="item in data.likeWorkScene" :key="item" class="item">
                    <em class="i" />
                    <span>{{ item }}</span>
                </div>
            </div>
            <div class="right">
                <div class="title">
                    <p class="icon">
                        <span />
                    </p>
                    不喜欢的工作场景
                </div>
                <div v-for="item in data.disLikeWorkScene" :key="item" class="item">
                    <em class="i" />
                    <span>{{ item }}</span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import SubTitle from './sub-title.vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
</script>

<style lang="less" scoped>
.icon {
    margin-right: 15px;
    position: relative;
    width: 12px;
    height: 12px;
    border-top: 6px solid transparent;
    border-bottom: 6px solid transparent;
    border-left: 9px solid #00bebd;
    border-right: 0px solid transparent;

    span {
        content: '';
        position: absolute;
        top: -6px;
        left: -3px;
        width: 12px;
        height: 12px;
        border-top: 6px solid transparent;
        border-bottom: 6px solid transparent;
        border-left: 9px solid rgba(0, 190, 189, 0.3);
        border-right: 0px solid transparent;
        opacity: 0.3;
    }
}

.preferred-scenarios {
    display: flex;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    .left,
    .right {
        flex: 1;
    }

    .left {
        margin-right: 5px;
        .title {
            color: #00bebd;
        }
        .item {
            background: #eef5fb;
        }
    }

    .right {
        .icon {
            border-left: 9px solid #fc9d64;
            span {
                border-left: 9px solid #fc9d64;
            }
        }
        .title {
            color: #fc9d64;
        }
        .item {
            background: #f2f2f2;
        }
    }

    .title {
        margin-bottom: 9px;
        display: flex;
        align-items: center;

        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    .item {
        padding: 2px 17px 2px 7px;
        margin-bottom: 5px;
        background: #eef5fb;
        display: flex;
        align-items: center;

        &:last-child {
            margin-bottom: 0;
        }

        .i {
            margin-right: 13px;
            width: 10px;
            height: 10px;
            background: url('https://img.bosszhipin.com/static/file/2024/e3c0lkbg4k1733219335040.png.webp')
                no-repeat;
            background-size: 100% auto;
        }
    }
}
</style>
<!--  -->
