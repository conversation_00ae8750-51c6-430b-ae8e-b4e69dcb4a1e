<template>
    <PageHeader />
    <SectionTitle title="职业偏好" style="margin-bottom: 22px" />
    <p class="text-desc">
        职业偏好包含<span>职业价值观、偏好领域、偏好场景</span>三部分，职业价值观是用于指导行为的内在价值取向；偏好领域是契合内心情感和倾向的领域，可能会随着时间而转变；偏好场景是基于个人喜好而选择的具体工作场景，是内心倾向的外在选择。
    </p>
    <SubTitle style="margin-top: 23px">
        职业价值观
    </SubTitle>
    <p class="text-desc">
        在职场中，该性格类型较为看重的{{ { 1: '一', 2: '二', 3: '三', 4: '四', 5: '五' }[professionValuesList.filter(x => x.text).length] }}个方面是：
    </p>
    <div class="color-block-wrap">
        <div class="color-block">
            <template v-for="item, index in professionValuesList" :key="item">
                <div
                    class="item"
                    :style="{
                        width: `${item.width}px`,
                        background: item.text ? `rgba(0, 190, 189, ${(5 - index) * 0.2})` : 'transparent',
                    }"
                >
                    <span
                        v-if="item.text"
                        class="icon"
                        :style="{
                            left: `${5 - index}0%`,
                        }"
                    />
                </div>
            </template>
        </div>
        <div class="info-wrap">
            <template v-for="item, index in professionValuesList" :key="item">
                <div class="info-item" :style="{ opacity: item.text ? 1 : 0 }">
                    <!-- <div class="info-item"> -->
                    <span>{{ index + 1 }}</span>
                    <span>{{ item.text }}</span>
                </div>
            </template>
        </div>
    </div>
    <!-- <p class="text-desc">
        这五个方面代表了该性格类型在职业选择和职业发展中的价值取向，该价值取向依据对各类型的大样本调研而来，反映群体的选择趋势。
    </p> -->
    <RichText :domString="data.professionValuesDesc || ''" :richTextIndex="567" />
    <SubTitle style="margin-top: 23px">
        偏好领域
    </SubTitle>
    <p class="text-desc" style="margin-top: 10px;">
        {{ data.preferenceFieldIntr }}
    </p>
    <p class="text-desc" style="margin-bottom: 11px;">
        在感兴趣的领域，更有利于发挥优势，激发职业动力，获得长足发展。以下领域更符合参评者的偏好：
    </p>
    <div class="head table-item text-desc">
        <div>领域类型</div>
        <div>具体领域</div>
    </div>
    <div v-for="item, index in data.preferenceFieldList" :key="index" class="table-for table-item text-desc">
        <div>{{ item.fieldType }}</div>
        <div>
            <p>{{ item.professionContent }}</p>
            <p>{{ item.fieldDesc }}</p>
        </div>
    </div>
    <p style="margin-top: 10px;" />
    <!-- <p class="text-desc" >
        {{ data.aboutPreferenceField }}
    </p> -->
    <RichText :domString="data.aboutPreferenceField" :richTextIndex="198" />
    <PreferredScenarios :data="data" />
    <PageFooter v-if="headerFooterConfig?.footer" :info="data" />
</template>

<script setup lang="ts">

import type { IData } from '../type/type'
import RichText from '@packages/exam-report/components/rich-text/index.vue'

import PageFooter from './page-footer.vue'
import PageHeader from './page-header.vue'
import PreferredScenarios from './preferred-scenarios.vue'
import SectionTitle from './section-title.vue'

import SubTitle from './sub-title.vue'

defineOptions({
    name: 'CareerPrefer',
    inheritAttrs: false,
})

const props = defineProps({
    data: {
        type: Object as PropType<IData>,
        required: true,
    },
    headerFooterConfig: {
        type: Object as PropType<{ header: boolean, footer: boolean }>,
    },
})
function initTable() {
    const dom = document.querySelectorAll('div[data-is-page-wrapper]')
    dom.forEach((item) => {
        const domList = []
        item.childNodes.forEach((item) => {
            const classNmae = item.getAttribute('class')
            if (classNmae && classNmae.split(' ').includes('table-for')) {
                domList.push(item)
            }
        })
        if (domList.length) {
            domList[domList.length - 1].setAttribute('data-table-last', 'true')
        }
    })
}
onMounted(() => {
    setTimeout(() => {
        initTable()
    }, 0)
})

const professionValuesList = computed(() => {
    const { professionValues } = props.data
    const colorBlockList = [196, 145, 115, 93, 68]
    const str = professionValues
    const list = str.split('；')
    const newList = []
    colorBlockList.forEach((item, index) => {
        const text = list[index] || ''
        newList.push({
            width: item,
            text,
        })
    })
    return newList
})
</script>

<style lang="less" scoped>
@import url(../assets/mixins.less);

.text-desc {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    span {
        font-family: var(--FZLanTingHeiS-DB-GB);
    }

    .highlight {
        color: #3bbcf5;
        font-size: 18px;
    }
}

.color-block-wrap {
    margin: 10px 0;
    padding-right: 17px;
    align-items: center;

    .color-block {
        display: flex;
        justify-content: space-between;

        .item {
            position: relative;
            height: 35px;

            .icon {
                position: absolute;
                bottom: -12px;
                left: 50%;
                transform: translateX(-50%) rotate(40deg);
                width: 8px;
                height: 8px;
                background: #ececec;
                border-radius: 10px;

                &::after {
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    content: '';
                    height: 20px;
                    width: 1px;
                    background: #ececec;
                }
            }
        }
    }

    .info-wrap {
        margin-top: 23px;
        display: flex;
        justify-content: space-between;
        .info-item {
            border-top: 1px solid #c4c4c4;
            border-bottom: 1px solid #c4c4c4;
            flex: 1;
            text-align: center;
            color: #000000;
            font-family: var(--FZLanTingHeiS-R-GB);
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 31px;
            border-right: 1px solid #c4c4c4;

            &:first-child {
                border-left: 1px solid #c4c4c4;
            }

            & + .info-item {
                border-right: 1px solid #c4c4c4;
            }

            // &:last-child {
            //     // border-right: 1px solid #c4c4c4;
            // }
        }
    }
}

.page-header-wrap ~ .table-item {
    border-radius: 10px 10px 0 0;
    border-top: 1px solid #c4c4c4;
    border-bottom: 1px solid #c4c4c4;
}

.table-item {
    // border-top: 1px solid #c4c4c4;
    border-left: 1px solid #c4c4c4;
    border-right: 1px solid #c4c4c4;
    display: flex;

    &.head {
        color: #000000;
        font-family: var(--FZLanTingHeiS-DB-GB);
        text-align: center !important;
        background: #e6f9ff;
    }

    &[data-table-last='true'] {
        border-radius: 0 0 10px 10px !important;
    }

    & + .table-item {
        // border-bottom: 1px solid #c4c4c4;
        // border-top: 1px solid #c4c4c4;
        // border-radius: 0;
        border-radius: 0;
        border-top: none;
        border-bottom: 1px solid #c4c4c4;
    }

    & > div {
        &:nth-child(1) {
            width: 115px;
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            border-right: 1px solid #c4c4c4;
        }
        &:nth-child(2) {
            padding: 9px;
            flex: 1;

            p:first-child {
                color: #00bebd;
            }
        }
    }
}
</style>
