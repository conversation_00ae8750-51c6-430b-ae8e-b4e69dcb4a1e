<template>
    <SubTitle style="margin-top: 22px;">
        任务管理建议
    </SubTitle>
    <RichText :domString="data.taskManagerAdviceIntroduction " :richTextIndex="197" />
    <p class="text">
        {{ userName }}相对喜欢的任务特征如下：
    </p>
    <div class="list-wrap">
        <div
            v-for="item, index in data.likeTaskFeature"
            :key="index"
            class="item"
            :style="{
                background: item.background,
                color: item.font,
                borderColor: item.border,
            }"
        >
            <p>{{ item }}</p>
        </div>
    </div>
</template>

<script setup lang="ts">
import RichText from '@packages/exam-report/components/rich-text/index.vue'

import SubTitle from './sub-title.vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    userName: {
        type: String,
        default: '',
    },
})
const tagList = ref(['常规性的任务', '与过往经验相关性较大', '任务有明确的流程和规则', '事务性的工作'])
</script>

<style lang="less" scoped>
.text {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
.info-wrap {
    & + .info-wrap {
        margin-top: 22px;
    }
}

.list-wrap {
    margin-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .item {
        width: 160px;
        height: 70px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border: 1px solid #00bebd;
        border-radius: 10px;
        text-align: center;
        color: #00bebd;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;

        p {
            margin: 0 auto;
            width: 129px;
        }
    }
}
</style>
