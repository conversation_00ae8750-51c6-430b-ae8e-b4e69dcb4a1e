<template>
    <div
        class="level"
        :style="{
            color: levelMap[`${localLevel}`].fontColor,
            backgroundColor: levelMap[`${localLevel}`].bgColor,
            borderColor: levelMap[`${localLevel}`].borderColor,
        }"
    >
        {{ levelMap[`${localLevel}`].font }}
    </div>
</template>
<script setup lang="ts">

const props = defineProps({
    level: {
        type: [Number, String],
        default: 1,
    },
});
const levelMap: Record<string, { font: string; fontColor: string; borderColor: string; bgColor: string }> = {
    '1': {
        font: '低',
        fontColor: '#E7A13E',
        borderColor: 'rgba(231, 161, 62, 0.3)',
        bgColor: '#FAECD8',
    },
    '2': {
        font: '中',
        fontColor: '#00A6A7',
        borderColor: 'rgba(0, 166, 167, 0.37)',
        bgColor: '#E5F9F9',
    },
    '4': {
        font: '高',
        fontColor: '#0092FA',
        borderColor: 'rgba(0, 146, 250, 0.37)',
        bgColor: '#CCE9FE',
    },
};
const localLevel = computed(() => {
    if (typeof props.level === 'number') {
        return props.level;
    } else {
        for (const key in levelMap) {
            if (Object.prototype.hasOwnProperty.call(levelMap, key)) {
                const element = levelMap[key];
                if (element.font === props.level) {
                    return key;
                }
            }
        }
    }
});
</script>
<style lang="less" scoped>
.level {
    width: 32px;
    height: 32px;
    font-family: var(--title-main-font-family);
    font-size: 21px;
    line-height: 32px;
    padding: 0 5px;
    border-radius: 6px;
    border: 1px solid transparent;
}
</style>
