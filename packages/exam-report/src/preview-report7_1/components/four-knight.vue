<template>
    <SubTitle title="四个字母的具体表现" style="margin-bottom: 18px;" />
    <div class="four-knight-wrapper">
        <div v-for="item, index of info" :key="index" class="item-wrapper">
            <div class="a">
                <div class="a-1">
                    {{ item.personalityCode === item.leftCode ? item.leftCode : item.rightCode }}
                </div>
                <div class="a-2">
                    {{ item.personalityCode === item.leftCode ? item.leftName : item.rightName }}
                </div>
            </div>
            <div class="b">
                <div v-for="item2, index2 of item.dimensionPerformance" :key="index2" class="b-item normal-content">
                    {{ item2 }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import SubTitle from './sub-title.vue'

defineOptions({
    name: '',
})
const props = defineProps({
    info: {
        type: Array,
        default: () => ([]),
    },
})
const data = [
    { n1: 'I', n2: '内向', desc: ['1111', '22222'] },
    { n1: 'S', n2: '内向', desc: ['1111', '222222222222222222222\n2222222222222222222'] },
    { n1: 'T', n2: '内向', desc: ['1111', '22222'] },
    { n1: 'J', n2: '内向', desc: ['111111111111111111111111', '22222'] },
]
</script>

<style lang="less" scoped>
@import url(../assets/mixins.less);
.four-knight-wrapper {
    display: flex;
    column-gap: 17px;
    .item-wrapper {
        width: 157px;
        flex-grow: 1;
        border-radius: 10px;
        padding: 7px 17px;
        background-image: linear-gradient(
            to bottom,
            #2ce5e4,
            rgba(198.38867, 255, 254.70204, 0) 126px
        );
        background-repeat: no-repeat;

        .a {
            color: #ffffff;
            font-family: 'Barlow Condensed ExtraBold';
            font-size: 42px;
            font-weight: 800;
            line-height: 32px;
            display: flex;
            justify-content: center;
            column-gap: 10px;
            .a-1 {
                font-style: italic;
            }
            .a-2 {
                font-family: var(--FZLanTingHeiS-B-GB);
                font-size: 18px;
                line-height: 32px;
                padding-top: 6px;
            }
        }
        .b {
            margin-top: 14px;
            .b-item {
                .paragraph-dot-father-base(15px, 26px);
                &::before {
                    .paragraph-dot-child-base();
                }
            }
        }
    }
}
</style>
