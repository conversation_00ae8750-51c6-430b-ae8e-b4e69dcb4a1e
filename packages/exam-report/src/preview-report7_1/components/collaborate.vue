<template>
    <div>
        <SubTitle>
            协作建议
        </SubTitle>
        <p class="text">
            根据任务需要，选择同频或有差异的搭档，同频的搭档能够使得沟通更加顺畅，差异大的搭档能够相互取长补短。
        </p>
    </div>
    <div class="info-wrap">
        <p class="text">
            其他15种性格类型中与
            <span :style="{ color: personalityTypeConfig.color.font }">{{ personalityTypeConfig.text }}</span>
            <span>最同频的搭档</span>是：
        </p>
        <div class="list-wrap">
            <div
                v-for="item, index in personalityList"
                :key="index"
                class="item"
                :style="{
                    background: item.background,
                    color: item.font,
                    borderColor: item.border,
                }"
            >
                <p class="tag">
                    {{ item.name }}
                </p>
                <p class="des">
                    {{ item.showName }}
                </p>
            </div>
        </div>
    </div>
    <div class="info-wrap">
        <p class="text">
            其他15种性格类型中与<span :style="{ color: personalityTypeConfig.color.font }">{{ personalityTypeConfig.text }}</span>
            <span>差异最大的搭档</span>是：
        </p>
        <div class="list-wrap">
            <div
                v-for="item, index in differenceList"
                :key="index"
                class="item"
                :style="{
                    background: item.background,
                    color: item.font,
                    borderColor: item.border,
                }"
            >
                <p class="tag">
                    {{ item.name }}
                </p>
                <p class="des">
                    {{ item.showName }}
                </p>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">

import { colorConfig } from '../util/color-config'
import SubTitle from './sub-title.vue'

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})

const differencePartnerConfig = computed(() => {
    console.log('differencePartnerConfig', props.data.differencePartner)
    // const { personalityType } = props.data.differencePartner
    // const color = colorConfig.getCharacterColor(personalityType)
    return {
    //     text: personalityType,
    //     color,
    }
})

const personalityTypeConfig = computed(() => {
    const { personalityType } = props.data.personalityDescResult
    const color = colorConfig.getCharacterColor(personalityType)
    return {
        text: personalityType,
        color,
    }
})

const personalityList = computed(() => {
    const list = props.data.sameFrequencyPartner

    return list.map(item => ({
        ...item,
        ...colorConfig.getCharacterColor(item.name),
    }))
})

const differenceList = computed(() => {
    const list = props.data.differencePartner

    return list.map(item => ({
        ...item,
        ...colorConfig.getCharacterColor(item.name),
    }))
})
</script>

<style lang="less" scoped>
.text {
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}
.info-wrap {
    & + .info-wrap {
        margin-top: 22px;
    }

    & > .text {
        span {
            font-family: var(--FZLanTingHeiS-DB-GB);
        }
    }
}

.list-wrap {
    margin-top: 10px;
    overflow: hidden;

    .item {
        float: left;
        margin-right: 47px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        border: 1px solid #44b7f2;
        width: 160px;
        height: 70px;
        border-radius: 10px;
        text-align: center;

        .tag {
            font-family: 'Barlow Condensed';
            font-size: 29.63px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            text-align: center;
        }

        .des {
            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            text-align: center;
        }
    }
}
</style>
