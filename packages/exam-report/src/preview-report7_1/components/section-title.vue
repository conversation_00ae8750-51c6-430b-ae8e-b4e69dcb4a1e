<template>
    <div class="section-title">
        <div class="section-title-inner">{{ title }}</div>
    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'SectionTitle',
});
const props = defineProps({
    title: String,
});
</script>
<style lang="less" scoped>
.section-title {
    font-family: var(--title-main-font-family);
    font-size: 28px;
    margin-bottom: 24px;
    line-height: 40px;
    .section-title-inner {
        display: inline-block;
        position: relative;
        &::before {
            content: '';
            width: calc(100% + 21px);
            min-width: 133px;
            height: 13px;
            position: absolute;
            bottom: 0;
            background-image: linear-gradient(90deg, #00bebd 0%, rgba(0, 190, 189, 0) 100%);
            border-radius: 6px;
            opacity: 0.2;
            overflow: hidden;
        }
    }
}
</style>
