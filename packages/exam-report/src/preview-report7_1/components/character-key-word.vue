<template>
    <div class="character-keyword-container">
        <div class="character-img-wrap">
            <div class="circle-bg-small" />
            <div class="circle-bg-big" />
            <Circle
                v-for="(item, index) of renderData"
                :key="index"
                :size="item.size"
                :position="item.position"
                :backgroundColor="item.backgroundColor"
                :content="item.content"
            />
        </div>
    </div>
    <div class="normal-content" style="margin-top: 22px;">{{ data.personalityDescResult.typeFeatureDesc }}</div>
</template>

<script setup lang="ts">

import Circle from './circle.vue'

defineOptions({
    name: 'CharacterKeyword',
})
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
})
const positions = [
    { left: 345, top: 177 },
    { left: 249, top: 71 },
    { left: 203, top: 187 },
    //
    { left: 318, top: 287 },
    { left: 307, top: 0 },
    { left: 429, top: 89 },
    { left: 107, top: 197 },
    { left: 160, top: 31 },
    { left: 169, top: 287 },
    { left: 453, top: 287 },
    { left: 70, top: 51 },
    { left: 477, top: 11 },
    { left: 30, top: 267 },
    { left: 496, top: 173 },
    { left: 0, top: 121 },
    { left: 536, top: 91 },
    { left: 536, top: 277 },
]
const renderData = computed(() => {
    return props.data.personalityDescResult.personalityKeyWord.split('；').slice(0, 17).map((x, i) => ({
        content: x.slice(0, 6),
        size: i >= 3 ? 60 : 80,
        backgroundColor: i >= 3 ? '#DEFCFD' : '#9FF5F5',
        position: positions[i],
    }))
})
</script>

<style lang="less" scoped>
.character-keyword-container {
    .character-img-wrap {
        position: relative;
        width: 596px;
        height: 359px;
        margin-left: 38px;
    }
    .circle-bg-small,
    .circle-bg-big {
        position: absolute;
        border-radius: 100%;
        border: 1px dashed #dfdfdf;
    }
    .circle-bg-small {
        top: 111px;
        left: 229px;
        width: 156px;
        height: 156px;
    }
    .circle-bg-big {
        top: 19px;
        left: 137px;
        width: 340px;
        height: 340px;
    }
}
</style>
