<template>
    <div style="margin-top: 23px">
        <slot></slot>
        <div class="attach-table-header" :data-table-header="headerId">
            <div class="td">适应力类型</div>
            <div class="td">适应力得分</div>
            <div class="td">适应力水平</div>
            <div class="td">与常模比较</div>
            <div class="td">超越百分比</div>
        </div>
        <div
            v-for="(item, index) of info.dimensionList.slice(0, 1)"
            :key="index"
            class="attach-table-row attach-table-row-odd"
            :class="{ 'attach-table-row-last': info.dimensionList.length === 1 }"
        >
            <div class="td normal-content">{{ item.dimensionShowName }}</div>
            <div class="td normal-content">{{ item.standardScore }}</div>
            <div class="td normal-content">{{ levelMap[item.dimensionLevel] }}</div>
            <div
                class="td normal-content"
                :style="{
                    '--color-text': compareMap[item.standardCompareResultLevel].textColor,
                    '--color-border': compareMap[item.standardCompareResultLevel].borderColor,
                    '--color-bg': compareMap[item.standardCompareResultLevel].bgColor,
                }"
            >
                <div class="compare">
                    {{ compareMap[item.standardCompareResultLevel].name }}
                </div>
            </div>
            <div class="td normal-content">{{ item.beyondPercent }}%</div>
        </div>
    </div>
    <div
        v-for="(item, index) of info.dimensionList.slice(1)"
        :key="index"
        class="attach-table-row"
        :class="[
            `${index % 2 !== 0 ? 'attach-table-row-odd' : 'attach-table-row-even'}`,
            { 'attach-table-row-first': index === 0 },
            { 'attach-table-row-last': index === info.dimensionList.length - 2 },
        ]"
        :data-table-row="headerId"
    >
        <div class="td normal-content">{{ item.dimensionShowName }}</div>
        <div class="td normal-content">{{ item.standardScore }}</div>
        <div class="td normal-content">{{ levelMap[item.dimensionLevel] }}</div>
        <div
            class="td normal-content"
            :style="{
                '--color-text': compareMap[item.standardCompareResultLevel].textColor,
                '--color-border': compareMap[item.standardCompareResultLevel].borderColor,
                '--color-bg': compareMap[item.standardCompareResultLevel].bgColor,
            }"
        >
            <div class="compare">
                {{ compareMap[item.standardCompareResultLevel].name }}
            </div>
        </div>
        <div class="td normal-content">{{ item.beyondPercent }}%</div>
    </div>
    <div style="margin-top: 10px; line-height: 31px" class="extra-desc" v-if="info.comment">{{ info.comment }}</div>
</template>
<script setup lang="ts">

import SubTitle from './sub-title.vue';

import type { IData, IDimensionItem } from '../type/type';

defineOptions({
    name: 'AttachTable',
});
const props = defineProps({
    info: {
        type: Object as PropType<IData>,
        default: () => ({}),
    },
    headerId: {
        type: String,
        default: '',
    },
});
const levelMap = {
    1: '低',
    2: '中',
    4: '高',
};
const compareMap = {
    0: {
        name: '等于常模',
        textColor: '#00A6A7',
        borderColor: '#00A6A7',
        bgColor: '#E5F9F9',
    },
    1: {
        name: '低于常模',
        textColor: '#ED7D31',
        borderColor: '#ED7D31',
        bgColor: '#FFF3E2',
    },
    4: {
        name: '高于常模',
        textColor: '#0092FA',
        borderColor: 'rgba(0, 146, 250, 0.37)',
        bgColor: '#CCE9FE',
    },
};
</script>
<style lang="less" scoped>
// 使用条件式mixin的两种方式
// .td-width(@index) {
//     width: if(@index = 1, 120px, if(@index = 2, 129px, if(@index = 3, 124px, if(@index = 4, 154px, if(@index = 5, 154px)))));
// }
.td-width(@index) when (@index = 1) {
    width: 120px;
}
.td-width(@index) when (@index = 2) {
    width: 129px;
}
.td-width(@index) when (@index = 3) {
    width: 124px;
}
.td-width(@index) when (@index = 4) {
    width: 154px;
}
.td-width(@index) when (@index = 5) {
    width: 154px;
}
.cell-base-layout() {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    word-break: break-all;
    white-space: pre-wrap;
    position: relative;
}
.attach-table-header {
    --b-table-border-radius: 6px;

    background-color: #e8f7ff;
    display: flex;
    border-radius: var(--b-table-border-radius) var(--b-table-border-radius) 0 0;
    text-align: center;

    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 16px;
    .td {
        .cell-base-layout();
        line-height: 26px;
        padding: 8px;
    }
}
.attach-table-row {
    --b-table-border-radius: 6px;
    overflow: hidden;
    display: flex;
    &.attach-table-row-odd {
        background-color: #fff;
    }
    &.attach-table-row-even {
        background-color: #fff;
    }
    &.attach-table-row-last {
        border-radius: 0 0 var(--b-table-border-radius) var(--b-table-border-radius);
    }
    .td {
        .cell-base-layout();
        padding: 11px;
        // background-color: #fff;
        .compare {
            color: var(--color-text);
            border: 1px solid var(--color-border);
            background: var(--color-bg);
            padding: 5px 15px;
            border-radius: 6px;
            font-family: var(--FZLanTingHeiS-B-GB);
            font-size: 15px;
            line-height: 15px;
        }
    }
}
// 宽度处理
.attach-table-header,
.attach-table-row {
    .td {
        // 宽度处理
        width: 20%;
    }
}
// 边框处理
.attach-table-header {
    --border-color: #ececec;
    .td {
        border-width: 1.33px;
    }
    & + .attach-table-row.attach-table-row-first {
        border-top-width: 1.33px;
    }
}
.attach-table-row {
    --border-color: #ececec;
    border-top-color: #ececec;
    border-bottom-color: #ececec;
    &.attach-table-row-first {
        border-top-width: 0;
    }
    .td {
        border-width: 1.33px;
        &:first-child {
            border-left-color: #ececec;
        }
        &:last-child {
            border-right-color: #ececec;
        }
    }
}
</style>
