<template>
    <div class="career-performance-info-wrap">
        <div class="title" :style="{ color: `${color}` }">
            <p class="icon" :style="{ borderLeft: `9px solid ${color}` }">
                <span :style="{ borderLeft: `9px solid ${color}` }" />
            </p>
            <span>{{ userName }}在职场中，{{ preTypeStr }} </span>
            <span class="tag" :style="{ backgroundColor: `${color}` }">{{ typeStr }}</span>
            <span>： </span>
        </div>
        <template v-for="item in list" :key="item">
            <div class="text-info" :class="{ flex: showType === 1 }">
                <p class="text-tit">
                    {{ item.name }}
                </p>
                <p>{{ item.showName }}</p>
            </div>
        </template>
    </div>
</template>

<script setup lang="ts">

defineOptions({
    name: 'CareerPerformance',
})
const props = defineProps({
    userName: {
        type: String,
        default: () => (''),
    },
    list: {
        type: Array,
        default: () => ([]),
    },
    color: {
        type: String,
        default: () => ('#00BEBD'),
    },
    showType: {
        type: Number,
        default: () => (0),
    },
    typeStr: {
        type: String,
        default: () => ('特点'),
    },
    preTypeStr: {
        type: String,
        default: '具有以下',
    },
})
</script>

<style lang="less" scoped>
.career-performance-info-wrap {
    color: #1f1f1f;
    font-family: 'FZLanTingHeiS-R-GB';
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    & + .career-performance-info-wrap {
        margin-top: 22px;
    }

    .title {
        margin-bottom: 15px;
        color: #00bebd;
        font-family: var(--FZLanTingHeiS-DB-GB);
        display: flex;
        flex-wrap: wrap;
        align-items: center;

        .icon {
            margin-right: 15px;
            position: relative;
            width: 12px;
            height: 12px;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-left: 9px solid #00bebd;
            border-right: 0px solid transparent;

            span {
                content: '';
                position: absolute;
                top: -6px;
                left: -3px;
                width: 12px;
                height: 12px;
                border-top: 6px solid transparent;
                border-bottom: 6px solid transparent;
                border-left: 9px solid rgba(0, 190, 189, 0.3);
                border-right: 0px solid transparent;
                opacity: 0.3;
            }
        }

        .tag {
            margin-left: 2px;
            color: #fff;
            background: #00bebd;
            border-radius: 2px;
            height: 16px;
            line-height: 16px;
            padding: 0px 2px 0;
        }
    }

    .text-info {
        padding-left: 14px;
        position: relative;

        &::after {
            content: '';
            position: absolute;
            top: 10px;
            left: 0;
            width: 4px;
            height: 4px;
            border-radius: 4px;
            overflow: hidden;
            background: #1f1f1f;
        }

        &.flex {
            display: flex;

            .text-tit {
                margin-right: 10px;
                white-space: nowrap;
            }
        }

        .text-tit {
            color: #1f1f1f;
            font-family: var(--FZLanTingHeiS-DB-GB);
        }

        p {
            white-space: pre-wrap;
        }
    }
}
</style>
