import { ModuleEnum } from '../type/type'

export function headerFooterConfig(currentCode: ModuleEnum, moduleCodes: ModuleEnum[]): { header: boolean, footer: boolean } {
    const currentIndex = moduleCodes.findIndex(x => x == currentCode)
    const result = {
        header: true,
        footer: true,
    }
    if (currentCode == ModuleEnum.综合结果) {
        if (moduleCodes[currentIndex + 1] == ModuleEnum.组织管理建议) {
            result.footer = false
        }
    }
    if (currentCode == ModuleEnum.组织管理建议) {
        if (moduleCodes[currentIndex - 1] == ModuleEnum.综合结果) {
            result.header = false
        }
    }
    // if (currentCode === ModuleEnum.甄选建议 || currentCode === ModuleEnum.发展建议) {
    //     if (moduleCodes[currentIndex - 1] == ModuleEnum.封面 || currentIndex === 0) {
    //         result.header = true;
    //     } else {
    //         result.header = false;
    //     }
    //     if (currentIndex !== moduleCodes.length - 1 && (moduleCodes[currentIndex + 1] == ModuleEnum.甄选建议 || moduleCodes[currentIndex + 1] == ModuleEnum.发展建议)) {
    //         result.footer = false;
    //     }
    // } else {
    //     if (moduleCodes[currentIndex + 1] == ModuleEnum.甄选建议 || moduleCodes[currentIndex + 1] == ModuleEnum.发展建议) {
    //         result.footer = false;
    //     }
    // }
    return result
}
