

const localName = ref('--');
export function useName() {
    function getLocalName(data: any) {
        const itemName = data.examineeInfoList.find((x: any) => x.fieldName === '姓名');
        const itemCode = data.examineeInfoList.find((x: any) => x.fieldName === '第三方唯一码');

        localName.value = itemName?.fieldValue || itemCode?.fieldValue || '--';
    }

    return { localName, getLocalName };
}