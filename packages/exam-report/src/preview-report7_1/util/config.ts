export const levelColor = {
    1: {
        name: '低',
        bar: '#DE952F',
        bg: '#FAECD8',
        border: 'rgba(231, 161, 62, 0.30)',
        font: '#DE952F'
    },
    2: {
        name: '中',
        bar: '#49C7C7',
        bg: '#DBF4F4',
        border: 'rgba(0, 166, 167, 0.37)',
        font: '#00A6A7'
    },
    4: {
        name: '高',
        bar: '#0092FA',
        bg: '#CCE9FE',
        border: 'rgba(0, 146, 250, 0.37)',
        font: '#0092FA'
    }
} as const;

export const approveColor = {
    low: {
        bg: '#CECECE',
        bgDanger: '#F84447'
    },
    middle: {
        bg: '#79DADA'
    },
    high: {
        bg: '#36B5AE'
    },
} as const;

export type levelType = keyof typeof levelColor;
export type approveLevelType = keyof typeof approveColor;
