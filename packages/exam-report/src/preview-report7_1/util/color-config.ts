interface ICharacterColorSet {
    /**
     * 方块边框颜色
     */
    border?: string
    /**
     * 方块背景颜色
     */
    background?: string
    /**
     * 字体颜色
     */
    font?: string
    /**
     * 第一部分内 横幅颜色
     */
    banner?: string
    /**
     * 图片url
     */
    url?: string
}

const typeImgUrlMap = {
    ISTJ: 'https://img.bosszhipin.com/static/file/2024/m25pke58t91733216349189.png.webp',
    ISFJ: 'https://img.bosszhipin.com/static/file/2024/clhfctgkca1733218069275.png.webp',
    INFJ: 'https://img.bosszhipin.com/static/file/2024/d96qxcjv671733218101804.png.webp',
    INTJ: 'https://img.bosszhipin.com/static/file/2024/ujhnb6gq3j1733218143006.png.webp',
    ISTP: 'https://img.bosszhipin.com/static/file/2024/nu6wlac1a11733218180121.png.webp',
    ISFP: 'https://img.bosszhipin.com/static/file/2024/7us8nbhbug1733218306010.png.webp',
    INFP: 'https://img.bosszhipin.com/static/file/2024/2ylamtcqg21733218244622.png.webp',
    INTP: 'https://img.bosszhipin.com/static/file/2024/rfyk2k2tus1733218344624.png.webp',
    ESTP: 'https://img.bosszhipin.com/static/file/2024/bq3opziapa1733218390678.png.webp',
    ESFP: 'https://img.bosszhipin.com/static/file/2024/rkotcozjae1733216251831.png.webp',
    ENFP: 'https://img.bosszhipin.com/static/file/2024/bf3oy93t231733218429295.png.webp',
    ENTP: 'https://img.bosszhipin.com/static/file/2024/zrba6rkzev1733218461219.png.webp',
    ESTJ: 'https://img.bosszhipin.com/static/file/2024/9rddhiddg11733216254678.png.webp',
    ESFJ: 'https://img.bosszhipin.com/static/file/2024/mxwjgn8st41733216253971.png.webp',
    ENFJ: 'https://img.bosszhipin.com/static/file/2024/8k2l1jq43z1733216252651.png.webp',
    ENTJ: 'https://img.bosszhipin.com/static/file/2024/liai8zxkh91733216253238.png.webp',
}
export const colorConfig = {
    //
    imgUrlMap: typeImgUrlMap,
    /**
     * 匹配度方块颜色，非报告参考性颜色
     */
    matchColor: [
        {
            label: '低',
            code: 1,
            cubeGradient: 'radial-gradient(#F6D19C 0%, #E7A13E 100%)',
            progressColor: '#E7A13E',
            progressBarLeaderShadow: '-1px 0px 4px 0px rgba(231, 161, 62, 0.38)',
        },
        {
            label: '中',
            code: 2,
            cubeGradient: 'radial-gradient(#A3EBEB 0%, #49C7C7 100%)',
            progressColor: '#49C7C7',
            progressBarLeaderShadow: '-1px 0px 4px 0px rgba(73, 199, 199, 0.38)',
        },
        {
            label: '高',
            code: 4,
            cubeGradient: 'radial-gradient(#46BEFC 0%, #0092FA 100%)',
            progressColor: '#0092FA',
            progressBarLeaderShadow: '-1px 0px 4px 0px rgba(27, 179, 250, 0.38)',
        },
    ],
    /**
     * @param character 性格，例如ISTJ
     * @returns 报告中使用到的颜色集合
     */
    getCharacterColor(character: string): ICharacterColorSet {
        const localCharacter: string = character.toLowerCase()
        let __return__: ICharacterColorSet = {}
        if (localCharacter.includes('s') && localCharacter.includes('j')) {
            __return__ = {
                border: 'rgba(68, 183, 242, 1)',
                background:
                    'linear-gradient(180deg, rgba(227, 252, 255, 0.3) 0%, rgba(144, 204, 251, 0.3) 100%)',
                font: 'rgba(68, 183, 242, 1)',
                banner: 'linear-gradient(90deg, #90CCFB 0%, rgba(227,252,255,0) 50.87%)',
                linearGradientColorStart: '#E3FCFF',
                linearGradientColorEnd: 'rgba(227, 252, 255, 0.00)',
            }
        } else if (localCharacter.includes('n') && localCharacter.includes('f')) {
            __return__ = {
                border: 'rgba(105, 197, 156, 1)',
                background:
                    'linear-gradient(180deg, rgba(240, 254, 247, 0.3) 0%, rgba(202, 251, 228, 0.3) 100%)',
                font: 'rgba(105, 197, 156, 1)',
                banner: 'linear-gradient(90deg, #CAFBE4 0%, rgba(240,254,247,0) 50.87%)',
                linearGradientColorStart: '#CFFFE9',
                linearGradientColorEnd: 'rgba(207, 255, 233, 0.00)',
            }
        } else if (localCharacter.includes('s') && localCharacter.includes('p')) {
            __return__ = {
                border: 'rgba(252, 157, 100, 1)',
                background:
                    'linear-gradient(180deg, rgba(252, 238, 220, 0.3) 0%, rgba(253, 216, 137, 0.3) 100%)',
                font: 'rgba(252, 157, 100, 1)',
                banner: 'linear-gradient(90deg, #FDD889 0%, rgba(252,238,220,0) 50.87%)',
                linearGradientColorStart: '#FEF1E0',
                linearGradientColorEnd: 'rgba(254, 241, 224, 0.00)',
            }
        } else if (localCharacter.includes('n') && localCharacter.includes('t')) {
            __return__ = {
                border: 'rgba(187, 155, 245, 1)',
                background:
                    'linear-gradient(180deg, rgba(245, 238, 254, 0.3) 0%, rgba(227, 166, 248, 0.3) 100%)',
                font: 'rgba(192, 152, 253, 1)',
                banner: 'linear-gradient(90deg, #E3A6F8 0%, rgba(245,238,254,0) 50.87%)',
                linearGradientColorStart: '#F5EEFE',
                linearGradientColorEnd: 'rgba(245, 238, 254, 0.00)',
            }
        }
        return __return__
    },
}
