<template>
    <div v-if="data" class="product-7_1 report-preview-page">
        <WaterMark
            :content="data.watermarkStr || '001-20240101-163'"
            :waterMarkConfig="props.requestParams ? 2 : props.params?.watermark ? props.params.watermark : data.watermark"
        >
            <Cover v-if="moduleCodes.includes('1')" :data="data" :logo="effectiveParams.reportLogo || data.reportLogo" />
            <div class="content-page single-page-wrap">
                <component
                    :is="item.component"
                    v-for="item of componentsRender"
                    :key="item.moduleCode"
                    :data="data"
                    :moduleCodes="moduleCodes.filter((x) => x !== '1')"
                    :headerFooterConfig="headerFooterConfig(item.moduleCode, JSON.parse(reportModuleCodes))"
                />
            </div>
        </WaterMark>
    </div>
</template>

<script setup lang="ts">
import WaterMark from '@packages/exam-report/components/watermark/index.vue'
import { debounce } from '@packages/exam-report/utils/index'

import Attach from './components-atom/attach.vue'
import ComprehensiveResult from './components-atom/comprehensive-result.vue'
import Cover from './components-atom/cover.vue'
import OrgManageAdvice from './components-atom/org-manage-advise.vue'
import PersonalGrowAdvice from './components-atom/personal-grow-advise.vue'
import ReportInstruction from './components-atom/report-instruction.vue'
import mockData from './mock'
import { ModuleEnum } from './type/type'
import { headerFooterConfig } from './util/show-footer'
import { useName } from './util/use-name'

defineOptions({
    name: 'ReportPreview',
    inheritAttrs: false,
})
const props = defineProps({
    productId: {
        type: Number,
        default: 1,
    },
    params: {
        type: Object,
        default: () => ({}),
    },
    getDataFunction: {
        type: Function,
        default: () => {},
    },
    requestParams: {
        type: Object,
    },
    reportModuleCodes: {
        type: String,
        default: JSON.stringify([
            ModuleEnum.封面,
            ModuleEnum.报告说明,
            ModuleEnum.综合结果,
            ModuleEnum.组织管理建议,
            ModuleEnum.个人发展建议,
            ModuleEnum.附录,
        ]),
    },
})
const { getLocalName } = useName()
const effectiveParams = computed(() => {
    return {
        reportTitle: props.params.reportTitle,
        reportSubTitle: props.params.extraParam?.templateSubTitle,
        reportLogo: props.params.reportLogo,
        reportType: props.params.extraParam?.reportType,
        reportExamineeField: JSON.stringify(props.params.reportExamineeField?.map(x => x.code)),
        reportDimensionField: JSON.stringify(props.params.reportDimensionField?.map(x => ({ code: x.code, showName: x.showName || x.dimName }))),
    }
})
const moduleCodes = ref([])
const ComponentsAll = [
    { moduleCode: ModuleEnum.封面, component: Cover },
    { moduleCode: ModuleEnum.报告说明, component: ReportInstruction },
    { moduleCode: ModuleEnum.综合结果, component: ComprehensiveResult },
    { moduleCode: ModuleEnum.组织管理建议, component: OrgManageAdvice },
    { moduleCode: ModuleEnum.个人发展建议, component: PersonalGrowAdvice },
    { moduleCode: ModuleEnum.附录, component: Attach },
]
const componentsRender = ref<any[]>([])
const data = ref()
let originReportName = ''
let originReportSubTitle = ''
async function getDetail() {
    const params = props.requestParams
        ? props.requestParams
        : {
                reportExaminees: effectiveParams.value.reportExamineeField,
                reportModules: effectiveParams.value.reportDimensionField,
                reportType: effectiveParams.value.reportType,
            }
    const res = await props.getDataFunction(params, props.productId)
    // const res = mockData
    if (res.code === 0) {
        data.value = undefined
        await nextTick()
        data.value = res.data || {}
        originReportName = data.value.templateName
        originReportSubTitle = data.value.reportSubTitle
        data.value.templateName = effectiveParams.value.reportTitle || originReportName
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle
        getLocalName(data.value)
    }
}

async function init() {
    await Promise.all([getDetail()])
}
function reLayout() {
    setTimeout(() => {
        doLayout()
        doLayout2()
        reassignAttendant()
        doNotEssential()
        collapseTable()
        // try {
        //     initReportStructure();
        // } catch (error) {}
    }, 0)
}

// const initReportStructure = () => {
//     const doms = document.querySelectorAll('.section-title .section-title-inner');
//     let list = [];
//     doms.forEach((dom) => {
//         list.push(dom.innerText);
//     });
//     data.value._reportStructure = [...list];
// };

onMounted(async () => {
    await init()
    reLayout()
    // startWatchModuleCodes();
})
/**
 *
 * @param element 元素
 * @param {object} config - 其他配置
 * @param {boolean} config.ignoreAttendantAttribute - 是否忽略随从属性，要得到真实高度的话，需要设为 true
 */
function calcHeight(element: HTMLElement, config = { ignoreAttendantAttribute: false }) {
    if (element === undefined || (!config.ignoreAttendantAttribute && element?.dataset.attendantsBelongTo)) {
        return 0
    }
    const height = element.offsetHeight + Number.parseFloat(getComputedStyle(element).marginTop) + Number.parseFloat(getComputedStyle(element).marginBottom)
    return height
}
function doLayout() {
    const pageTotalHeight = 1124
    const pageHeaderHeight = 67
    const pageFooterHeight = 67
    const avaliableHeight = pageTotalHeight - pageHeaderHeight - pageFooterHeight

    const pageSplitDom = document.createElement('div')
    pageSplitDom.classList.add('page-split')
    const pageHeaderDom = document.querySelector('.page-header-wrap')
    const pageFooterDom = document.querySelector('.page-footer-wrap')

    const ddom = document.querySelectorAll('[data-header-name="调研结果详情-header"]')
    const dddom = document.querySelectorAll('[data-header-name="题目得分详情-header"]')

    let tempHeightSum = 0
    const atoms = [...document.querySelectorAll('.single-page-wrap.content-page > *:not(.page-header-wrap):not(.page-footer-wrap)')]
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i]
        if (atom.dataset.hasAttendants) {
            const attendantsDoms = document.querySelectorAll(`[data-attendants-belong-to="${atom.dataset.hasAttendants}"]`)
            for (let j = 0; j < attendantsDoms.length; j++) {
                const attendantDom = attendantsDoms[j]
                tempHeightSum += calcHeight(attendantDom, { ignoreAttendantAttribute: true })
            }
        }
        const atomHeight = calcHeight(atom)
        const nextAtomHeight = calcHeight(atoms[i + 1])
        tempHeightSum += atomHeight
        const condition1 = atom.nextElementSibling?.classList.contains('page-footer-wrap') // 下个元素是页脚
        const condition2 = tempHeightSum + nextAtomHeight > avaliableHeight // 剩余可用高度不足以放下下一个元素

        if (atoms[i + 1]?.dataset.tableRow && condition2) {
            const tableHeaderDom = document.querySelector(`[data-table-header="${atoms[i + 1].dataset.tableRow}"]`)
            let insertPosition = 'beforebegin'
            if (condition2) {
                insertPosition = 'afterend'
            }
            atom.insertAdjacentHTML(insertPosition, tableHeaderDom?.outerHTML)
            atoms.splice(i + 1, 0, tableHeaderDom)
        }

        if (condition1 || condition2) {
            tempHeightSum = 0
            if (!condition1) {
                atom.insertAdjacentHTML('afterend', pageHeaderDom.outerHTML)
                atom.insertAdjacentHTML('afterend', pageFooterDom.outerHTML)
            }
        }
    }
    const allPageHeaders = [...document.querySelectorAll('.page-header-wrap')]
    const allPageFooters = [...document.querySelectorAll('.page-footer-wrap')]
    for (let i = 0; i < allPageFooters.length; i++) {
        // 更改页脚的页码
        const element = allPageFooters[i]
        element.querySelector('.page-count').innerText = i + 1
    }
}
function doLayout2() {
    const pageSplitDom = document.createElement('div')
    pageSplitDom.classList.add('page-split')
    const divs = []
    const wrapper = document.querySelector('.single-page-wrap.content-page')
    const atoms = [...document.querySelectorAll('.single-page-wrap.content-page > *')]
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i]
        if (atom.classList.contains('page-header-wrap')) {
            divs[divs.length] = document.createElement('div')
        }
        divs[divs.length - 1].appendChild(atom)
    }
    for (let i = 0; i < divs.length; i++) {
        const div = divs[i]
        div.style.height = '1124px'
        div.dataset.isPageWrapper = 'true'
        if (i === divs.length - 1) {
            div.style.height = '1117px'
        }
        div.style.position = 'relative'
        const footer = div.querySelector('.page-footer-wrap')
        footer.style.position = 'absolute'
        footer.style.bottom = 0
        footer.style.marginTop = 0
        wrapper.appendChild(div)
        div.insertAdjacentHTML('beforebegin', pageSplitDom.outerHTML)
    }
}
function reassignAttendant() {
    const allMarkDoms = document.querySelectorAll('[data-has-attendants]')
    for (let i = 0; i < allMarkDoms.length; i++) {
        const markDom = allMarkDoms[i]
        const allAttendantsDoms = document.querySelectorAll(`[data-attendants-belong-to="${markDom.dataset.hasAttendants}"]`)
        let bottomSum = 0
        // 两种写法，正序反序都行。
        // for (let i = 0; i < allAttendantsDoms.length; i++) {
        //     const anchorDom = allAttendantsDoms[i - 1] || markDom.parentElement?.querySelector('.page-footer-wrap');
        //     const attendantDom = allAttendantsDoms[i];
        //     bottomSum += calcHeight(anchorDom, { ignoreAttendantAttribute: true });
        //     allAttendantsDoms[allAttendantsDoms.length - i - 1].style.bottom = `${bottomSum}px`;
        //     anchorDom?.insertAdjacentElement(i === 0 ? 'beforebegin' : 'afterend', attendantDom);
        //     console.log('%c [ bottomSum ]-229', 'font-size:13px; background:pink; color:#bf2c9f;', bottomSum);
        // }
        for (let i = allAttendantsDoms.length - 1; i >= 0; i--) {
            const attendantDom = allAttendantsDoms[i]
            const anchorDom = i === allAttendantsDoms.length - 1 ? markDom.parentElement?.querySelector('.page-footer-wrap') : allAttendantsDoms[i + 1]
            bottomSum += calcHeight(anchorDom, { ignoreAttendantAttribute: true })
            attendantDom.style.bottom = `${bottomSum}px`
            anchorDom?.insertAdjacentElement('beforebegin', attendantDom)
        }
    }
}
function doNotEssential() {
    const notEssentialDom = document.querySelector('[data-not-essential]') as HTMLDivElement
    if (!notEssentialDom) {
        return
    }
    const parentDom = notEssentialDom?.parentElement
    const allDoms = parentDom!.children
    let preHeight = 0
    for (let i = 0; i < allDoms.length; i++) {
        const dom = allDoms[i]
        if (dom.hasAttribute('data-not-essential')) {
            break
        } else {
            preHeight += calcHeight(dom)
        }
    }
    if (preHeight > 1124 / 2) {
        notEssentialDom.style.display = 'none'
    }
}
function collapseTable() {
    const allRowsDom = document.querySelectorAll('[data-attach-l1-index]')
    let cache = []
    for (let i = 0; i < allRowsDom.length; i++) {
        const row = allRowsDom[i]
        cache.push(row)
        if (row.dataset.attachL1Index !== row.nextElementSibling?.dataset.attachL1Index) {
            const parentElement = row.parentElement
            const wrapDom = document.createElement('div')
            for (let j = 0; j < cache.length; j++) {
                wrapDom.appendChild(cache[j])
            }
            const alternative1 = [...(parentElement?.children ?? [])].filter((x: HTMLElement) => x.hasAttribute('data-forged-table-row')).reverse()[0]
            const alternative2 = parentElement?.querySelector('.page-header-wrap')
            const anchorDom = alternative1 || alternative2
            anchorDom?.insertAdjacentElement('afterend', wrapDom)
            cache = []
            setWrapDom(wrapDom, row.children[0] as HTMLDivElement)
        }
    }
}
function setWrapDom(targetDom: HTMLDivElement, sourceDom: HTMLDivElement) {
    targetDom.classList.add('table-row-combined')

    const child = document.createElement('div')
    child.innerText = sourceDom.innerText

    targetDom.style.position = 'relative'
    child.classList.add('table-td-combined')
    child.style.width = `${sourceDom.clientWidth}px` // 不含边框宽度
    child.style.padding = window.getComputedStyle(sourceDom).padding

    targetDom.appendChild(child)
}
watch(
    () => effectiveParams.value.reportTitle,
    () => {
        data.value.templateName = effectiveParams.value.reportTitle || originReportName
        const doms = [...document.querySelectorAll('.page-footer-wrap .report-name-filed')]
        for (let i = 0; i < doms.length; i++) {
            const el = doms[i]
            el.innerText = data.value.templateName
        }
    },
    {},
)
watch(
    () => effectiveParams.value.reportSubTitle,
    () => {
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle
    },
    {},
)
watch(
    [() => effectiveParams.value.reportExamineeField, () => effectiveParams.value.reportDimensionField],
    debounce(async () => {
        // data.value = undefined;
        await getDetail()
        reLayout()
    }, 500),
    {},
)
watch(
    () => props.reportModuleCodes,
    debounce(async () => {
        moduleCodes.value = JSON.parse(props.reportModuleCodes).map(x => String(x))
        const localModuleCodes = moduleCodes.value.filter(x => x !== '1')
        componentsRender.value = localModuleCodes.map(x => ComponentsAll.find((y: any) => y.moduleCode === x))
        const tempData = data.value
        data.value = undefined
        await nextTick()
        data.value = tempData
        reLayout()
    }, 500),
    {
        immediate: true,
    },
)
</script>

<style lang="less">
.report-preview-page.product-7_1 {
    .page-header-wrap {
        margin-bottom: 0;
    }
    .normal-content {
        line-height: 26px;
    }
    .lext-theme-h2 {
        font-family: var(--FZLanTingHeiS-DB-GB);
        margin-top: 10px;
        margin-bottom: 12px;
        color: #1f1f1f;
    }
    .lext-theme-listItem {
        margin-left: 0;
        padding-left: 16px;
        position: relative;
        &:not([data-is-belong-to-previous-li]):before {
            content: '';
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #1f1f1f;
            margin-left: 0;
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
        }
    }
    .inserted-table-header[data-header-name='调研结果详情-header'] {
        font-size: 0;
        line-height: 14px;
        margin-bottom: 0;
        margin-top: 24px !important;
        &::before {
            display: none;
        }
        & + [data-row-name='调研结果详情-row'] {
            margin-top: 22px !important;
        }
    }
}
</style>
