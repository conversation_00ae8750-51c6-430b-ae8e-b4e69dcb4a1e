import { levelType } from '../util/config'

export enum ModuleEnum {
    封面 = '1',
    报告说明 = '2',
    综合结果 = '3',
    组织管理建议 = '13',
    个人发展建议 = '14',
    附录 = '6',
}

export interface IDimensionItem {
    dimensionName: string
    dimensionShowName: string
    /**
     * 维度等级 1-低，2-中，4-高
     */
    dimensionLevel: 1 | 2 | 4
    /**
     * 标准分与常模比较结果 0-等于，1-低，4-高
     */
    standardCompareResultLevel: 0 | 1 | 4
    /**
     * 维度标准分
     */
    standardScore: string
    /**
     * 超越百分比
     */
    beyondPercent: string
    /**
     * 标准分常模平均分
     */
    standardAvg: string
    /**
     * 标准分标准差
     */
    standardDeviation: string
    /**
     * 维度注释
     */
    dimensionComment: string
    /**
     * 维度定义
     */
    dimensionDefine: string
    /**
     * 维度等级表现
     */
    dimensionLevelPerformance: string
    /**
     * 维度提升建议
     */
    dimensionAdvice: string
    /**
     * 甄选建议
     */
    selectionAdvice: string
}

export interface IData extends Record<string, any> {
    jobAdaptStyle: string
    jobStylePerformance: string
    dimensionList: IDimensionItem[]
}
