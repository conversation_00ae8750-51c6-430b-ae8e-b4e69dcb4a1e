.report-preview {
    --page-padding-horizontal: 52px;
    --page-split-margin-horizontal: -52px;

    font-family: var(--base-font-family);
    background-color: #f9f8fb;
    font-size: var(--font-size-body-2);
    color: var(--main-body-text-color);
    flex: 1;
    .page-split {
        height: 24px;
        background-color: #f9f8fb;
        margin: 0 var(--page-split-margin-horizontal);
    }
    .report-preview__bg-container {
        position: absolute;
        left: calc(-1 * var(--page-padding-horizontal));
        right: calc(-1 * var(--page-padding-horizontal));
        top: 0;
        bottom: 0;
        overflow: hidden;
        z-index: 0;
        svg {
            filter: blur(32px);
        }
        & ~ * {
            position: relative;
            z-index: 1;
        }
    }
}
// 兼容旧版本报告样式
.report-preview-page {
    --b-table-border-radius: 5px;
    --b-table-zebra-bg-color: #f0f3f5;
    --b-table-header-background: #e8f7ff;
    --border-color: #ececec;
    --base-body-color: #1f1f1f;
    --copy-right-color: var(--gray-color-5);
    --page-header-color: #808080;
    --page-footer-color: #808080;
    --extra-desc-color: #808080;
    --primary-color: #00a6a7;
    --page-padding-horizontal: 52px;
    --page-split-margin-horizontal: -52px;

    font-family: var(--base-font-family);
    background-color: #f9f8fb;
    flex: 1;
    font-size: 13px;
    color: var(--base-body-color);

    .b-split-pre-line {
        word-break: break-all;
        // display: inline-block;
        line-height: 27px;

        --split-margin-x: 4px;

        &::before {
            background: #ccc;
            height: 11px;
        }
    }

    .page-split {
        height: 24px;
        background-color: #f9f8fb;
        margin: 0 var(--page-split-margin-horizontal);

        &:first-child {
            display: none;
        }
    }

    .page-header-wrap {
        height: 67px;
        display: inline-flex; // 阻止margin合并
        width: 100%;
        align-items: center;
        justify-content: flex-end;
        margin-bottom: 32px;

        .copyright-wrap {
            color: var(--page-header-color);
        }

        & + * {
            margin-top: 0 !important;
        }
    }

    .page-footer-wrap {
        color: var(--page-footer-color);
        font-size: 12px;
        height: 67px;
        display: inline-flex; // 阻止margin合并
        width: 100%;
        align-items: center;
        justify-content: space-between;
        margin-top: 32px;
        left: 0;

        .footer-left {
            margin-right: 24px;
        }

        .page-count {
            flex-shrink: 0;
        }
    }

    .single-page-wrap {
        background-color: #fff;
        width: 794px;
        margin: 0 auto;
        padding-left: var(--page-padding-horizontal);
        padding-right: var(--page-padding-horizontal);

        &.cover-page {
            height: 1124px;
            padding-top: 226px;
            position: relative;
            background-image: url('https://img.bosszhipin.com/static/file/2024/zmgqwy6oal1711447948781.png');
            background-size: contain;
            background-position: center 567px;
            background-repeat: no-repeat;

            .logo-img,
            .logo-text {
                position: absolute;
                right: 35px;
                top: 35px;
                width: 160px;
                height: 80px;
            }

            .logo-img {
                display: flex;
                align-items: center;
                justify-content: center;

                img {
                    display: block;
                    max-height: 100%;
                    max-width: 100%;
                }
            }

            .logo-text {
                line-height: 80px;
                background: #d8d8d8;
                color: #808080;
                opacity: 0.2;
                font-size: 34px;
                text-align: center;
                font-weight: bold;
                visibility: hidden;
            }

            .report-title {
                font-family: var(--title-main-font-family);
                text-align: center;
                font-size: 53px;
                line-height: 65px;
                color: #282828;
                margin: 0 var(--page-split-margin-horizontal);
                padding: 0 100px;
            }

            .examinee-info {
                color: #101010;
                font-size: 17px;
                line-height: 20px;
                margin-top: 83px;
                display: flex;
                justify-content: center;

                .info-wrap {
                    max-width: 400px;

                    .info-item + .info-item {
                        margin-top: 13px;
                    }
                }
            }

            .copyright-wrap {
                position: absolute;
                right: 0;
                left: 0;
                margin: auto;
                text-align: center;
                bottom: 20px;
                font-size: 15px;
            }
        }

        &.cover-page + .content-page {
            .page-split:first-child {
                display: block;
            }
        }
    }

    .normal-content {
        word-break: break-all;
        white-space: pre-wrap;
        line-height: 26px;
    }

    .extra-desc {
        word-break: break-all;
        white-space: pre-wrap;
        font-size: 12px;
        color: var(--extra-desc-color);
        line-height: 24px;
    }

    .section-intro {
        color: var(--gray-color-7);
        line-height: 27px;
        margin-top: -21px;
        margin-bottom: 14px;
        white-space: pre-wrap;
    }

    .no-shrink {
        flex-shrink: 0;
    }

    // 假 table 单元格合并
    .table-td-combined {
        position: absolute;
        left: 1px;
        top: 1px;
        bottom: 1px;
        display: flex;
        justify-content: center;
        align-items: center;
        background-color: #fff;
    }

    .table-row-combined {
        & + .table-row-combined {
            .attach-table-row {
                &:first-child {
                    border-top: none;
                }
            }
        }
    }
}

@page {
    margin: 0;
}

@media print {
    .page-split {
        display: none !important;
    }
}
