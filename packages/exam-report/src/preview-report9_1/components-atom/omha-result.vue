<template>
    <PageHeader></PageHeader>
    <SectionTitle title="模型详细结果"></SectionTitle>
    <p class="desc-font">
        <span>OMHA模型从</span>
        <span>{{allDimensionData.classStr.join('、')}}这</span>
        <span>{{ allDimensionData.classStr.length }}个方面刻画心理健康，</span>
        <span>共计{{allDimensionData.length}}个维度。</span>
    </p>
    <template v-for="item in data.dimensionDetailInfo" :key="item.name">
        <p class="target-title-wrap">
            <span class="text-subtitle">{{item.name}}：</span>
            <span>{{item.desc}}</span>
            <span v-if="item.subDetailInfo">，包含{{ item.subDetailInfo.map((sub: any) => `${sub.name}`).join('、') }}，共{{ item.subDetailInfo.length }}个维度。</span>
        </p>
        <template v-for="(obj, index) in item.subDetailInfo" :key="obj.name">
            <TargetExplain :title="index < 1 ? {name: item.name, desc: item.desc }: {}" :info="item" :data="obj"></TargetExplain>
        </template>
    </template>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">

import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import TargetExplain from '../components/target-explain.vue';

defineOptions({
    name: 'InterviewAdvise',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});

interface IallDimensionData {
    length: number;
    classStr: string[];
}

const allDimensionData = ref<IallDimensionData>({
    classStr: [],
    length: 0,
});

const allDimensionScoreInit = (data: { subDimensionScore: any; }[]) => {
    const newData = <IallDimensionData>{
        classStr: [],
        length: 0,
    };

    let list = [...data];

    list.forEach((item:{ name: string, subDetailInfo: Array<any> }) => {
        if (!newData.classStr.includes(item.name)) {
            newData.classStr.push(item.name);
        }
        newData.length += item.subDetailInfo.length;
    });
    allDimensionData.value = newData;
    return newData;
};

onMounted(() => {
    allDimensionScoreInit(props.data.dimensionDetailInfo);
});

</script>
<style lang="less">
.job-matching-explain {
    margin-top: 24px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    // text-indent: 1em;
    white-space: pre-wrap;
}
.job-matching-title {
    margin-top: 34px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-DB-GB);
    font-size: 22px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
    text-align: center;
}

.result-tit {
    margin-top: 22px;
    display: flex;
    align-items: end;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 15px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;

    .level-tag {
        margin: 0 14px;
        width: 32px;
        height: 32px;
        border-radius: 6px;
        background: #cce9fe;
        border: 1px solid rgba(0, 146, 250, 0.37);
        text-align: center;
        line-height: 32px;
        color: #0092fa;
        font-family: var(--FZLanTingHeiS-B-GB);
        font-size: 22px;
        font-style: normal;
        font-weight: 400;
    }

    .number {
        color: #0092fa;
    }
}

.result-img {
    margin: 22px auto 10px;
    width: 690px;
    height: 180px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

.result-desc {
    color: #808080;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 20px;
}

.character-feature-describe {
    margin-top: 34px;
    color: #1f1f1f;
    font-family: var(--FZLanTingHeiS-R-GB);
    font-size: 13px;
    font-style: normal;
    font-weight: 400;
    line-height: 26px;
}

.character-feature-item {
    margin-top: 10px;
}

.target-title-wrap {
    margin-top: 22px;
    padding: 18px 12px;
    position: relative;
    border-radius: 6px;
    color: #1F1F1F;
    font-size: 13px;
    font-style: normal;
    font-weight: 400;

    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        width: 130px;
        background: linear-gradient(89.99999deg, #00BEBD 8%, rgba(0, 190, 189, 0.00) 89%);
        opacity: 0.20;
        border-radius: 6px;
    }

    .text-subtitle {
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-weight: 600;
    }

    .desc-font {
        font-family: "FZLanTingHeiS-R-GB";
    }
}
</style>
