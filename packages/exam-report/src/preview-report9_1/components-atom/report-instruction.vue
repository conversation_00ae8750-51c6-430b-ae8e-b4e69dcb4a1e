<template>
    <PageHeader></PageHeader>
    <SectionTitle title="测评介绍" style="margin-bottom: 22px" data-has-attendants="1"></SectionTitle>
    <RichText :domString="data.testDescription" :rich-text-index="1"></RichText>
    <!-- <p class="sub-title" style="margin-top: 35px">报告使用指南</p>
    <div class="desc-font">
        <p>本报告有效期为 1-3 个月，在此期间请做好保密处理，超过有效期建议重新完成该测评。</p>
        <p>本报告依据作答者的自我陈述结果分析得出，与其作答过程、作答状态、作答环境有关，在使用本报告前，请先确认作答者的报告参考性情况。</p>
        <p>本报告只反映作答者与职业健康相关的某些特质及能力，以及在近期 1-2 周所呈现的状态，结果没有正确与错误之分，只反映主观体验。</p>
        <p>本报告中的指数范围均为 0-100，50 为常模的平均值。</p>
        <p>“常模”是指具有代表性的人群在本测评所测的维度上的平均水平，可以作为了解个体测评结果在群体中的相对位置的数值参考。</p>
        <p>本报告基于作答者在职业心理健康多方面的指标进行评估，可用于人才招聘、选拔与晋升、人才培养、人才盘点等多种场景。</p>
    </div> -->
    <!-- <p class="sub-title" style="margin: 35px 0 22px">报告阅读结构概览</p> -->
    <!-- <div class="read-img"><img :src="data.readImage" alt="" /></div> -->
    <div class="normal-content attendant-footer" data-attendants-belong-to="1">{{ data.testDescriptionFooter }}</div>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">
import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';

defineOptions({
    name: 'ReportInstruction',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});
</script>
<style lang="less" scoped>
.read-img {
    display: flex;
    justify-content: center;
    img {
        max-width: 690px;
        height: 114px;
    }
}

.attendant-footer {
    &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 321px;
        border-top: 1px solid #979797;
    }
}

[data-attendants-belong-to] {
    position: absolute;
    padding-top: 6px;
    padding-left: 6px;
    font-size: 12px;
    color: #808080;
    line-height: 26px;
}
</style>
