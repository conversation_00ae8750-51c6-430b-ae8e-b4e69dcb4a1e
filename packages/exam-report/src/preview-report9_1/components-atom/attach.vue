<template>
    <PageHeader></PageHeader>
    <SectionTitle title="附录" style="margin-bottom: 32px"></SectionTitle>
    <RefrenceLevel :info="data" style="margin-bottom: 22px"></RefrenceLevel>
    <RichText v-if="data.indicativeIntroduction" :domString="data.indicativeIntroduction" :richTextIndex="2"></RichText>
    <div style="margin-top: 27px"></div>
    <RichText :domString="data.aboutThisReport" :richTextIndex="3"></RichText>
    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">
import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';
import RefrenceLevel from '../components/refrence-level.vue';

defineOptions({
    name: 'Attach',
    inheritAttrs: false,
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});
</script>
