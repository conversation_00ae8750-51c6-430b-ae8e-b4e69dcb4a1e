<template>
    <div class="single-page-wrap end-page">
        <div class="logo-img" v-if="logo"><img :src="logo" alt="" /></div>
        <div class="report-title">{{ data.templateName }}</div>
    </div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'End'
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({})
    },
    logo: {
        type: String,
        default: ''
    }
});
</script>
<style lang="less" scoped>
.report-preview-page {
    .single-page-wrap.end-page {
        height: 1124px;
        padding-top: 422px;
        word-break: break-all;
        margin-top: 24px;
        .logo-img {
            margin: 0 auto 53px;
            width: 160px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            img {
                max-width: 100%;
                max-height: 100%;
            }
        }
        .report-title {
            text-align: center;
            font-size: 30px;
            font-family: var(--title-sub-font-family);
            line-height: 36px;
            color: rgba(21, 21, 21, 0.5);
        }
    }
}
</style>
<style lang="less">
@media print {
    .report-preview-page {
        .single-page-wrap.end-page {
            margin-top: 0 !important;
        }
    }
}
</style>
