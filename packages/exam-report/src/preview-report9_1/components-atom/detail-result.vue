<template>
    <!-- <ComprehensiveResult :data="data"></ComprehensiveResult> -->
    <PageHeader></PageHeader>
    <SectionTitle title="测评结果概要"></SectionTitle>
    <!-- <RichText :domString="data.importantResultIntr" /> -->
    <div class="sub-title-flex-box">
        <p class="sub-title">职业心理健康指数</p>
        <span class="highlight-number">{{ data.totalScoreInfo.score }}</span>
        <LevelIcon :level="data.totalScoreInfo.level" :percent="data.totalScoreInfo.percent" percentStr="超越"></LevelIcon>
    </div>
    <GroupBar
        :legends="['个人', '常模']"
        :dataSource="[
            {
                label: '个体',
                children: [
                    { type: '个体', value: Number(data.totalScoreInfo.score).toFixed(2), color: '#9B6DBF' },
                ]
            },
            {
                label: '常模',
                children: [
                    { type: '常模', value: Number(data.totalScoreInfo.normalScore).toFixed(2), color: '#F6BD2E' },
                ]
            }
        ]"
        :maxNum="Math.max(Number(data.totalScoreInfo.score), Number(data.totalScoreInfo.normalScore), 100)"
    ></GroupBar>
    <div class="desc-font" style="margin: 22px 0 13px">
        <p>
            个体的职业心理健康指数为
            <span class="highlight-number" style="color: #9b6dbf">{{ data.totalScoreInfo.score }}</span>
            ，职业心理健康指数常模为
            <span class="highlight-number" style="color: #f6bd2e">{{ data.totalScoreInfo.normalScore }}</span>
            <span>。个体指数</span>
            <span v-if="data.totalScoreInfo.score > data.totalScoreInfo.normalScore">高</span>
            <span v-if="data.totalScoreInfo.score === data.totalScoreInfo.normalScore">中</span>
            <span v-if="data.totalScoreInfo.score < data.totalScoreInfo.normalScore">低</span>
            <span>于常模，属于</span>
            <span>{{ levelStrMaps[`${data.totalScoreInfo.level}`] }}</span>
            <span>水平。超越</span>
            <span class="highlight-number" style="color: #9b6dbf">{{data.totalScoreInfo.percent}}%</span>
            <span>的人。</span>
        </p>
    </div>
    <div class="desc-font info-bar-wrap">
        <p>{{ data.totalScoreInfo.levelLanguage }}</p>
    </div>
    <div class="read-img"><img :src="data.readImage" alt="" /></div>
    <div v-if="data.developSuggest" class="recommendation-wrap">
        <p class="tit">{{reportType === ReportTypeEnum.PERSONAL ? '发展建议':'甄选建议'}}</p>
        <p v-for="(item, index) in data.developSuggest">
            <span>{{ index + 1 }}、</span>
            <span>{{ item }}</span>
        </p>
    </div>

    <PageFooter :info="data"></PageFooter>
    <PageHeader></PageHeader>

    <SectionTitle title="核心测验结果"></SectionTitle>
    <!-- <RichText :domString="data.coreTestInfo" /> -->
    <p class="desc-font">
        <span>核心测验结果包括{{data.coreTestInfo.length}}部分，分别为</span>
        <span v-for="(item, index) in data.coreTestInfo">
            {{ item.name }}
            {{ index + 1 === data.coreTestInfo.length? '，': '、' }}
        </span>
        <span>它们从不同侧面反映了职业心理健康水平。</span>
    </p>
    <div class="core-test-info-color-img">
        <div class="color-item" v-for="(item, index) in data.coreTestInfo" :key="item.name" :style="{background: `${coreTestInfoColorConfig[item.name]}`}">
            <span>{{ item.name }}指数</span>
        </div>
    </div>
    <div style="padding-top: 22px; margin-bottom: 20px;">
        <p style="margin-bottom: 22px" class="desc-font">
            {{initDataSource(data.coreTestInfo).length}}个核心测验结果情况与相应的常模的比较，呈现与如下图：
        </p>
        <GroupBar
            :legends="['个人指数', '常模指数']"
            :color="['#9B6DBF', '#F6BD2E']"
            :dataSource="initDataSource(data.coreTestInfo)"
            :maxNum="maxNum"
        ></GroupBar>
    </div>

    <template v-for="(item, index) in data.coreTestInfo" :key="item.name">
        <QuizDetailChart :chartWidth="620" :index="`${index + 1}. `" :data="item" titleLevel="3">
            <template v-if="index < 1" #title>
                <p class="desc-font" style="margin-bottom: 34px;">每个核心测验的详细结果，展示如下：</p>
            </template>
        </QuizDetailChart>
        <template v-if="item.subDetailInfo && item.subDetailInfo.length" v-for="(obj, i) in item.subDetailInfo" :key="obj.name">
            <div class="sub-quiz-detail-chart">
                <QuizDetailChart :chartWidth="600" :index="`${index + 1}. ${i + 1} `" :data="obj" titleLevel="4" percentStr="低于"></QuizDetailChart>
            </div>
        </template>
    </template>

    <PageFooter :info="data"></PageFooter>
</template>
<script setup lang="ts">

import PageHeader from '../components/page-header.vue';
import PageFooter from '../components/page-footer.vue';
import SectionTitle from '../components/section-title.vue';
import QuizDetailChart from '../components/quiz-detail-chart.vue';
import GroupBar from '../components/group-bar.vue';
import Bar from '../components/bar.vue';
import RichText from '@packages/exam-report/components/rich-text/index.vue';
import LevelIcon from '../components/level-icon.vue';
import { ReportTypeEnum } from './const';

const maxNum = ref(100);
const teamImportantInfoTextColorConfig: any = {
    低: '#DE952F',
    中: '#49C7C7',
    高: '#0092FA',
};

const levelStrMap: any = {
    '1': '低',
    '2': '等',
    '4': '高',
};

const levelStrMaps: any = {
    '1': '低',
    '2': '中',
    '4': '高',
};

const coreTestInfoColorConfig = {
    '心理弹性': 'linear-gradient(179.99998deg, #D7B4FC 6%, #BF92EE 100%)',
    '幸福感': 'linear-gradient(179.99998deg, #93D1FF 0%, #7295FC 100%)',
    '职业活力': 'linear-gradient(179.99998deg, #FFE394 0%, #F8B67B 100%)',
    '工作满意度': 'linear-gradient(179.99998deg, #91F0E7 0%, #56DDDE 100%)',
}

defineOptions({
    name: 'DetailResult',
    inheritAttrs: false,
});

type Iitem = {
    name: string;
    score: string;
    normalScore: string;
};

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    staticConfig: {
        type: Array,
        default: () => [],
    },
    reportType: {
        type: Number,
        default: ReportTypeEnum.PERSONAL
    }
});

const initDataSource = (list: []) => {
    console.log('initDataSource', list);
    return [
        ...list.map((item: Iitem ) => {
            let score = Number(item.score);
            let normalScore = Number(item.normalScore)
            maxNum.value = Math.ceil(Math.max(score, normalScore, maxNum.value, 100) / 10) * 10

            return {
                label: item.name,
                children: [
                    { type: '团队整体指数', value: score.toFixed(2) },
                    { type: '常模指数', value: normalScore.toFixed(2) },
                ],
            }
        })
    ]
}
</script>
<style lang="less" scoped>

.core-test-info-color-img {
    margin: 22px auto 0;
    display: flex;
    flex-wrap: wrap;
    width: 272px;
    padding-right: -10px;

    .color-item {
        margin-bottom: 10px;
        width: 130px;
        height: 130px;
        border-radius: 10px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        background: linear-gradient(179.99998deg, #D7B4FC 6%, #BF92EE 100%);

        &:nth-child(odd) {
            margin-right: 10px;
        }

        span {
            margin: 0 auto;
            width: 52px;
            color: #FFFFFF;
            font-family: var(--FZLanTingHeiS-R-GB);
            font-size: 13px;
            font-style: normal;
            font-weight: 400;
            line-height: 16px;
        }
    }
}

.read-img {
    display: flex;
    justify-content: center;
    img {
        max-width: 690px;
        height: 260px;
    }
}

.sub-title-flex-box {
    margin-bottom: 22px;
    display: flex;
    align-items: center;

    .sub-title {
        margin-bottom: 0 !important;
    }

    .highlight-number {
        margin: 0 10px;
        padding-bottom: 4px;
    }
}

.info-bar-wrap {
    margin: 13px 0 23px;
    padding: 3px 15px 1px;
    background: #F1F9FF;
    border-radius: 10px;
}

.recommendation-wrap {
    margin: 32px 0;
    padding: 19px 38px;
    border: 1px solid #CCE9FE;
    border-radius: 10px;

    p {
        color: #000000;
        font-family: "FZLanTingHeiS-R-GB";
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;

        &.tit {
            margin-bottom: 5px;
            font-size: 18px;
            font-weight: 500;
        }
    }
}

.desc-font-flex {
    margin: 22px 0 35px;
    display: flex;
    align-items: center;

    .highlight-number {
        margin-right: 10px;
    }
}
.sub-quiz-detail-chart {
    margin-top: 16px;
    padding: 29px 16px 18px;
    background: #f6f6f6;

    & + .sub-quiz-detail-chart {
        margin-top: 0;
    }

    .quiz-detail-chart-wrap:first-child {
        margin-top: 0 !important;
    }
}

.level-two-dimension-introduction {
    margin-top: 24px;
    font-size: 12px;
    color: #808080;
    line-height: 20px;
}
.level-two-dimension {
    padding: 29px 0 12px;
    border-bottom: 1px solid #ececec;
    & + .level-one-dimension {
        margin-top: 39px;
    }
    .level-two-dimension-top {
        margin-bottom: 24px;
        display: flex;
        align-items: center;
        .level-two-dimension-name {
            font-family: var(--title-sub-font-family);
            flex-shrink: 0;
            width: 94px;
            margin-right: 36px;
            font-size: 15px;
            line-height: 26px;
            word-break: break-all;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }
        .level-two-dimension-desc-wrap {
            width: 60px;
            flex-shrink: 0;
            display: flex;
            .level-two-dimension-desc {
                padding: 4px 5px;
                font-size: 12px;
                line-height: 14px;
                border-radius: 4px;
            }
        }
        .progress-wrap {
            margin: 0 8px;
        }
    }
    .level-two-dimension-level-language {
        font-size: 13px;
        line-height: 26px;
    }
}
</style>
