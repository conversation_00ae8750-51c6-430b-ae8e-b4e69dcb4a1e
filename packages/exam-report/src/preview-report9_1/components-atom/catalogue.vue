<template>
    <div class="single-page-wrap catalogue-page">
        <div class="assign-anchor" ref="assignAnchorRef"></div>
        <div v-for="(item, index) of renderData" :key="index" :class="['catalogue-item', `catalogue-level-${item.level}`]">
            <div class="text-wrap" :style="{ 'padding-left': `${(item.level - 1) * 22}px` }">{{ item.content }}</div>
            <div class="line"></div>
            <div class="page-index">{{ item.pageIndex }}</div>
        </div>
    </div>
</template>
<script setup lang="ts">

defineOptions({
    name: 'Catalogue',
});
const assignAnchorRef = ref<HTMLElement>();
const renderData = ref<{ content: string; level: number; pageIndex: number }[]>([]);
function renderPage() {
    const sectionTitles = document.querySelectorAll('.section-title');
    for (let i = 0; i < sectionTitles.length; i++) {
        const element = sectionTitles[i];
        element.dataset.titleLevel = 1;
    }
    const subTitles = [...document.querySelectorAll('.lext-theme-h2'), ...document.querySelectorAll('.sub-title')];
    for (let i = 0; i < subTitles.length; i++) {
        const element = subTitles[i];
        element.dataset.titleLevel = 2;
    }
    const allTitles = document.querySelectorAll('[data-title-level]');
    renderData.value = [...allTitles].map((x: HTMLElement) => {
        const result = {
            level: Number(x.dataset.titleLevel),
            content: '',
            pageIndex: findPageIndex(x),
        };
        if (x.classList.contains('lext-theme-h2')) {
            const children = x.querySelectorAll('*');
            result.content = children.length > 0 ? [...children].map((y: HTMLElement) => y.innerText).join('') : '';
        } else {
            result.content = x.dataset.titleText || x.innerText;
        }
        return result;
    });
    window.setTimeout(assign, 0);
}
function findPageIndex(x: HTMLElement): number {
    const pageWrapDom = findParent(x);
    return pageWrapDom ? Number(pageWrapDom.querySelector('.page-count')?.innerHTML) : 0;
}
function findParent(x: HTMLElement): HTMLElement | null {
    let parent = x.parentElement;
    if (!parent) {
        return null;
    }
    if (parent.dataset.isPageWrapper) {
        return parent;
    } else {
        return findParent(parent);
    }
}

function assign(): void {
    const pageTotalHeight = 1124;
    const pageHeaderHeight = 67;
    const pageFooterHeight = 67;
    const avaliableHeight = pageTotalHeight - pageHeaderHeight - pageFooterHeight;

    const pageSplitDom = document.createElement('div');
    pageSplitDom.classList.add('page-split');
    const pageHeaderDom = document.querySelector('.page-header-wrap');
    const pageFooterDom = document.querySelector('.page-footer-wrap');

    const allTitles = document.querySelectorAll('.catalogue-item');
    const wrappers: HTMLDivElement[] = [document.createElement('div')];
    let tempHeightSum = 0;
    for (let i = 0; i < allTitles.length; i++) {
        const element = allTitles[i] as HTMLElement;
        const elementHeight = element.offsetHeight + parseFloat(getComputedStyle(element).marginTop) + parseFloat(getComputedStyle(element).marginBottom);
        tempHeightSum += elementHeight;
        if (tempHeightSum > avaliableHeight) {
            wrappers[wrappers.length] = document.createElement('div');
            tempHeightSum = 0;
        }
        wrappers[wrappers.length - 1].appendChild(element);
    }
    for (let i = wrappers.length - 1; i >= 0; i--) {
        const div = wrappers[i];
        div.style.height = '1124px';
        div.dataset.isPageWrapper = 'true';
        div.style.position = 'relative';
        div.insertAdjacentHTML('afterbegin', pageHeaderDom?.outerHTML as string);
        div.insertAdjacentHTML('beforeend', pageFooterDom?.outerHTML as string);
        assignAnchorRef.value?.insertAdjacentElement('afterend', div);
        div.insertAdjacentHTML('beforebegin', pageSplitDom.outerHTML);
        i === wrappers.length - 1 && div.insertAdjacentHTML('afterend', pageSplitDom.outerHTML);
    }
}
defineExpose({ render: renderPage });
</script>
<style lang="less" scoped>
.catalogue-page {
    .catalogue-item {
        display: flex;
        align-items: center;
        color: #1f1f1f;
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: 13px;
        line-height: 28px;
        &.catalogue-level-1 {
            font-family: var(--FZLanTingHeiS-DB-GB);
            font-size: 16px;
            line-height: 28px;
        }
        .text-wrap {
            max-width: 385px;
        }
        .line {
            flex-grow: 1;
            border-top: 1px dashed #dcdcdc;
            margin: 0 40px;
        }
        .page-index {
            width: 20px;
            text-align: right;
            font-family: var(--FZLanTingHeiS-R-GB);
            font-size: 13px;
            line-height: 28px;
        }
    }
    :deep(.page-count) {
        display: none;
    }
}
</style>
