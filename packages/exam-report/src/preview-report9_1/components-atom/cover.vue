<template>
    <div
        class="single-page-wrap cover-page"
        :style="{
            backgroundImage: `url(${
                reportType === ReportTypeEnum.PERSONAL
                    ? 'https://img.bosszhipin.com/static/file/2024/p93adnv57f1722240555818.svg'
                    : 'https://img.bosszhipin.com/static/file/2024/9l8e4cy9o71722240556016.svg'
            })`,
            backgroundPosition: 'center bottom',
        }"
    >
        <div class="logo-img" v-if="logo"><img :src="logo" /></div>
        <div class="logo-text" v-else>LOGO</div>
        <div class="report-title">{{ data.templateName }}</div>
        <div class="report-sub-title">{{ data.reportSubTitle }}</div>
        <div class="spliter"></div>
        <div class="examinee-info">
            <div class="info-wrap">
                <div class="info-item" v-for="(item, index) of data.examineeInfoList" :key="index">{{ item.fieldShowName }}：{{ item.fieldValue }}</div>
            </div>
        </div>
        <Copyright></Copyright>
    </div>
</template>
<script setup lang="ts">
import Copyright from '../components/copyright.vue';
import { ReportTypeEnum } from './const';
defineOptions({
    name: 'Cover',
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    logo: {
        type: String,
        default: '',
    },
    reportType: {
        type: Number,
        default: ReportTypeEnum.PERSONAL,
    },
});
</script>
<style lang="less" scoped>
:deep(.copyright-wrap) {
    --copy-right-color: rgba(16, 16, 16, 0.3);
}
.report-preview-page {
    .single-page-wrap.cover-page {
        padding-top: 161px;
        word-break: break-all;
        white-space: pre-wrap;
        .report-title {
            width: 525px;
            font-family: var(--title-sub-font-family);
            font-size: 60px;
            line-height: 71px;
            padding: 0;
            color: #151515;
            text-align: left;
            margin-left: 31px;
        }
        .report-sub-title {
            width: 576px;
            font-family: var(--FZLanTingHeiS-L-GB);
            padding: 0;
            margin-left: 31px;
            margin-top: 5px;
            margin-right: -17px;
            font-size: 24px;
            color: #151515;
            line-height: 32px;
            text-align: left;
            opacity: 0.5;
            word-break: normal;
            letter-spacing: 0.5px;
        }
        .spliter {
            width: 183px;
            height: 2px;
            background: #d8d8d8;
            opacity: 0.5;
            margin: 10px 0 10px 31px;
        }
        .examinee-info {
            font-family: var(--FZLanTingHeiS-R-GB);
            color: #333;
            text-align: left;
            justify-content: flex-start;
            margin: 0 0 0 31px;
            font-size: 15px;
            line-height: 24px;
            .info-wrap {
                .info-item {
                    & + .info-item {
                        margin-top: 0;
                    }
                }
            }
        }
    }
}
</style>
