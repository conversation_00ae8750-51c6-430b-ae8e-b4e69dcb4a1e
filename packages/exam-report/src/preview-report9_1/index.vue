<template>
    <div v-if="data" class="product-9 report-preview-page">
        <WaterMark :content="data.watermarkStr || '001-20240101-163'" :waterMarkConfig="props.requestParams ? 2 : props.params?.watermark ? props.params.watermark : data.watermark">
            <Cover
                v-if="moduleCodes.includes('1')"
                :data="data"
                :logo="effectiveParams.reportLogo || data.reportLogo"
                :reportType="props.requestParams?.reportType || props.params.extraParam?.reportType"
            />
            <!-- 目录 -->
            <div class="content-page single-page-wrap">
                <component
                    :is="item.component"
                    v-for="item of componentsRender"
                    :key="item.moduleCode"
                    :data="data"
                    :staticConfig="staticConfig"
                    :reportType="props.requestParams?.reportType || props.params.extraParam?.reportType"
                />
            </div>
            <End :data="data" :logo="effectiveParams.reportLogo || data.reportLogo" />
        </WaterMark>
    </div>
</template>

<script setup lang="ts">
import WaterMark from '@packages/exam-report/components/watermark/index.vue'
import { debounce } from '@packages/exam-report/utils/index'

import Attach from './components-atom/attach.vue'
import { staticConfig } from './components-atom/const'
import Cover from './components-atom/cover.vue'
import DetailResult from './components-atom/detail-result.vue'
import End from './components-atom/end.vue'
import OmhaResult from './components-atom/omha-result.vue'
import ReportInstruction from './components-atom/report-instruction.vue'
import { res } from './mock'

defineOptions({
    name: 'ReportPreview',
    inheritAttrs: false,
})
const props = defineProps({
    productId: {
        type: Number,
        default: 1,
    },
    params: {
        type: Object,
        default: () => ({}),
    },
    getDataFunction: {
        type: Function,
        default: () => {},
    },
    requestParams: {
        type: Object,
    },
    reportModuleCodes: {
        type: String,
        default: '[1, 2, 3, 4, 6]',
    },
})

const effectiveParams = computed(() => {
    return {
        reportTitle: props.params.reportTitle,
        reportSubTitle: props.params.extraParam?.templateSubTitle,
        matchJobList: props.params.extraParam?.matchJobList,
        reportLogo: props.params.reportLogo,
        reportType: props.params.extraParam?.reportType,
        reportExamineeField: JSON.stringify(props.params.reportExamineeField?.map(x => x.code)),
        reportDimensionField: JSON.stringify(props.params.reportDimensionField?.map(x => ({ code: x.code, showName: x.showName || x.dimName }))),
    }
})
const moduleCodes = ref([])
const ComponentsAll = [
    { moduleCode: '1', component: Cover },
    { moduleCode: '2', component: ReportInstruction },
    // { moduleCode: '3', component: ComprehensiveResult },
    { moduleCode: '3', component: DetailResult },
    { moduleCode: '4', component: OmhaResult },
    { moduleCode: '6', component: Attach },
]
const componentsRender = ref([])
const data = ref()
const CatalogueRef = ref()
let originReportName = ''
let originReportSubTitle = ''
async function getDetail() {
    const params = props.requestParams
        ? props.requestParams
        : {
                reportExaminees: effectiveParams.value.reportExamineeField,
                reportModules: effectiveParams.value.reportDimensionField,
                reportType: effectiveParams.value.reportType,
            }
    const res = await props.getDataFunction(params, props.productId)
    if (res.code === 0) {
        data.value = undefined
        await nextTick()
        data.value = res.data || {}
        originReportName = data.value.templateName
        originReportSubTitle = data.value.reportSubTitle
        data.value.templateName = effectiveParams.value.reportTitle || originReportName
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle
    }
}

async function init() {
    await Promise.all([getDetail()])
}
function reLayout() {
    setTimeout(() => {
        doLayout()
        doLayout2()
        reassignAttendant()
        doNotEssential()
        collapseTable()
        assignChartTitle()
        assignSectionTitle()
        CatalogueRef.value?.render()
    }, 0)
}
onMounted(async () => {
    await init()
    reLayout()
    // startWatchModuleCodes();
})
/**
 *
 * @param element 元素
 * @param {object} config - 其他配置
 * @param {boolean} config.ignoreAttendantAttribute - 是否忽略随从属性，要得到真实高度的话，需要设为 true
 */
function calcHeight(element: HTMLElement, config = { ignoreAttendantAttribute: false }) {
    if (element === undefined || (!config.ignoreAttendantAttribute && element?.dataset.attendantsBelongTo)) {
        return 0
    }
    const height = element.offsetHeight + Number.parseFloat(getComputedStyle(element).marginTop) + Number.parseFloat(getComputedStyle(element).marginBottom)
    return height
}
function doLayout() {
    const pageTotalHeight = 1124
    const pageHeaderHeight = 67
    const pageFooterHeight = 67
    const avaliableHeight = pageTotalHeight - pageHeaderHeight - pageFooterHeight

    const pageSplitDom = document.createElement('div')
    pageSplitDom.classList.add('page-split')
    const pageHeaderDom = document.querySelector('.page-header-wrap')
    const pageFooterDom = document.querySelector('.page-footer-wrap')

    let tempHeightSum = 0
    const atoms = [...document.querySelectorAll('.single-page-wrap.content-page > *:not(.page-header-wrap):not(.page-footer-wrap)')]
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i]
        if (atom.dataset.hasAttendants) {
            const attendantsDoms = document.querySelectorAll(`[data-attendants-belong-to="${atom.dataset.hasAttendants}"]`)
            for (let j = 0; j < attendantsDoms.length; j++) {
                const attendantDom = attendantsDoms[j]
                tempHeightSum += calcHeight(attendantDom, { ignoreAttendantAttribute: true })
            }
        }
        const atomHeight = calcHeight(atom)
        const nextAtomHeight = calcHeight(atoms[i + 1])
        tempHeightSum += atomHeight
        const condition1 = atom.nextElementSibling?.classList.contains('page-footer-wrap') // 下个元素是页脚
        const condition2 = tempHeightSum + nextAtomHeight > avaliableHeight // 剩余可用高度不足以放下下一个元素
        if (condition1 || condition2) {
            tempHeightSum = 0
            if (!condition1) {
                atom.insertAdjacentHTML('afterend', pageHeaderDom.outerHTML)
                atom.insertAdjacentHTML('afterend', pageFooterDom.outerHTML)
            }
        }
    }
    const allPageHeaders = [...document.querySelectorAll('.page-header-wrap')]
    const allPageFooters = [...document.querySelectorAll('.page-footer-wrap')]
    for (let i = 0; i < allPageFooters.length; i++) {
        // 更改页脚的页码
        const element = allPageFooters[i]
        element.querySelector('.page-count').innerText = i + 1
    }
}
function doLayout2() {
    const pageSplitDom = document.createElement('div')
    pageSplitDom.classList.add('page-split')
    const divs = []
    const wrapper = document.querySelector('.single-page-wrap.content-page')
    const atoms = [...document.querySelectorAll('.single-page-wrap.content-page > *')]
    for (let i = 0; i < atoms.length; i++) {
        const atom = atoms[i]
        if (atom.classList.contains('page-header-wrap')) {
            divs[divs.length] = document.createElement('div')
        }
        divs[divs.length - 1].appendChild(atom)
    }
    for (let i = 0; i < divs.length; i++) {
        const div = divs[i]
        div.style.height = '1124px'
        div.dataset.isPageWrapper = 'true'
        if (i === divs.length - 1) {
            div.style.height = '1117px'
        }
        div.style.position = 'relative'
        const footer = div.querySelector('.page-footer-wrap')
        footer.style.position = 'absolute'
        footer.style.bottom = 0
        footer.style.marginTop = 0
        wrapper.appendChild(div)
        div.insertAdjacentHTML('beforebegin', pageSplitDom.outerHTML)
    }
}
function reassignAttendant() {
    const allMarkDoms = document.querySelectorAll('[data-has-attendants]')
    for (let i = 0; i < allMarkDoms.length; i++) {
        const markDom = allMarkDoms[i]
        const allAttendantsDoms = document.querySelectorAll(`[data-attendants-belong-to="${markDom.dataset.hasAttendants}"]`)
        let bottomSum = 0
        // 两种写法，正序反序都行。
        // for (let i = 0; i < allAttendantsDoms.length; i++) {
        //     const anchorDom = allAttendantsDoms[i - 1] || markDom.parentElement?.querySelector('.page-footer-wrap');
        //     const attendantDom = allAttendantsDoms[i];
        //     bottomSum += calcHeight(anchorDom, { ignoreAttendantAttribute: true });
        //     allAttendantsDoms[allAttendantsDoms.length - i - 1].style.bottom = `${bottomSum}px`;
        //     anchorDom?.insertAdjacentElement(i === 0 ? 'beforebegin' : 'afterend', attendantDom);
        //     console.log('%c [ bottomSum ]-229', 'font-size:13px; background:pink; color:#bf2c9f;', bottomSum);
        // }
        for (let i = allAttendantsDoms.length - 1; i >= 0; i--) {
            const attendantDom = allAttendantsDoms[i]
            const anchorDom = i === allAttendantsDoms.length - 1 ? markDom.parentElement?.querySelector('.page-footer-wrap') : allAttendantsDoms[i + 1]
            bottomSum += calcHeight(anchorDom, { ignoreAttendantAttribute: true })
            attendantDom.style.bottom = `${bottomSum}px`
            anchorDom?.insertAdjacentElement('beforebegin', attendantDom)
        }
    }
}
function doNotEssential() {
    const notEssentialDom = document.querySelector('[data-not-essential]') as HTMLDivElement
    if (!notEssentialDom) {
        return
    }
    const parentDom = notEssentialDom?.parentElement
    const allDoms = parentDom!.children
    let preHeight = 0
    for (let i = 0; i < allDoms.length; i++) {
        const dom = allDoms[i]
        if (dom.hasAttribute('data-not-essential')) {
            break
        } else {
            preHeight += calcHeight(dom)
        }
    }
    if (preHeight > 1124 / 2) {
        notEssentialDom.style.display = 'none'
    }
}
function collapseTable() {
    const allRowsDom = document.querySelectorAll('[data-attach-l1-index]')
    let cache = []
    for (let i = 0; i < allRowsDom.length; i++) {
        const row = allRowsDom[i]
        cache.push(row)
        if (row.dataset.attachL1Index !== row.nextElementSibling?.dataset.attachL1Index) {
            const currentPageFooterDom = row.parentElement?.querySelector('.page-footer-wrap')
            const wrapDom = document.createElement('div')
            for (let j = 0; j < cache.length; j++) {
                wrapDom.appendChild(cache[j])
                currentPageFooterDom?.insertAdjacentElement('beforebegin', wrapDom)
            }
            cache = []
            setWrapDom(wrapDom, row.children[0] as HTMLDivElement)
        }
    }
}
function setWrapDom(targetDom: HTMLDivElement, sourceDom: HTMLDivElement) {
    targetDom.classList.add('table-row-combined')

    const child = document.createElement('div')
    child.innerText = sourceDom.innerText

    targetDom.style.position = 'relative'
    child.classList.add('table-td-combined')
    child.style.width = `${sourceDom.clientWidth}px` // 不含边框宽度
    child.style.padding = window.getComputedStyle(sourceDom).padding

    targetDom.appendChild(child)
}
function assignChartTitle() {
    const chartTitles = document.querySelectorAll('[data-chart-index]')
    chartTitles.forEach((x, i) => {
        x.innerHTML = x.innerHTML.replace(/图\d+/, `图${i + 1}`)
    })
}
function assignSectionTitle() {
    const sectionTitles = document.querySelectorAll('.section-title')
    let cursor = 1
    const reg = /Part\d+/
    sectionTitles.forEach((x) => {
        if (reg.test(x.innerHTML)) {
            x.innerHTML = x.innerHTML.replace(reg, `Part${cursor}`)
            cursor++
        }
    })
}
watch(
    () => effectiveParams.value.reportTitle,
    () => {
        data.value.templateName = effectiveParams.value.reportTitle || originReportName
        const doms = [...document.querySelectorAll('.page-footer-wrap .report-name-filed')]
        for (let i = 0; i < doms.length; i++) {
            const el = doms[i]
            el.innerText = data.value.templateName
        }
    },
    {},
)
watch(
    () => effectiveParams.value.reportSubTitle,
    () => {
        data.value.reportSubTitle = effectiveParams.value.reportSubTitle || originReportSubTitle
    },
    {},
)
watch(
    [() => effectiveParams.value.reportExamineeField, () => effectiveParams.value.reportDimensionField],
    debounce(async () => {
        // data.value = undefined;
        await getDetail()
        reLayout()
    }, 500),
    {},
)
// function startWatchModuleCodes() {
watch(
    () => props.reportModuleCodes,
    debounce(async () => {
        moduleCodes.value = JSON.parse(props.reportModuleCodes).map(x => String(x))
        const localModuleCodes = moduleCodes.value.filter(x => x !== '1')
        componentsRender.value = localModuleCodes.map(x => ComponentsAll.find((y: any) => y.moduleCode === x))
        const tempData = data.value
        data.value = undefined
        await nextTick()
        data.value = tempData
        reLayout()
    }, 500),
    {
        immediate: true,
    },
)
// }
</script>

<style lang="less">
// @import './index.less';
.report-preview-page.product-9 {
    .page-header-wrap {
        margin-bottom: 0;
    }

    // 标题下说明文本（常用）
    .desc-font {
        color: #1f1f1f;
        font-family: var(--FZLanTingHeiS-R-GB);
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    // 子标题
    .sub-title {
        font-family: var(--title-sub-font-family);
        font-size: 18px;
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        line-height: 31px;
        &::before {
            content: '';
            width: 5px;
            height: 18px;
            background: #00bebd;
            border-radius: 3px;
            margin-right: 8px;
            position: relative;
            top: -1px;
        }
        :deep(.level) {
            font-family: var(--title-main-font-family);
            font-size: 21px;
            height: 32px;
            line-height: 32px;
            padding: 0 5px;
            border-radius: 6px;
            border: 1px solid rgba(0, 146, 250, 0.37);
            margin-left: 8px;
        }
    }

    .text-tit {
        color: #1f1f1f;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 31px;
    }

    .text-tit-sub {
        color: #1f1f1f;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    .text-subtitle {
        color: #1f1f1f;
        font-family: var(--FZLanTingHeiS-DB-GB);
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    .highlight-number {
        color: #0092fa;
        font-family: var(--Kanzhun);
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
    }

    // 占位符
    .g2-seize {
        outline: 2px solid #9b6dbf;
        height: 200px;
    }
}
</style>
