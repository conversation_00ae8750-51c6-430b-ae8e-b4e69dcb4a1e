<template>
    <div class="pie-chart-container">
        <div class="pie-chart-self" ref="pieChartRef"></div>
        <div class="pie-chart-legend">
            <div class="legend-item" v-for="(item, index) of data.valueList">
                <div class="legend-indicator" :style="{ background: COLORSEQUENCE[index % COLORSEQUENCE.length] }"></div>
                <div class="legend-label">{{ item.name }}</div>
            </div>
        </div>
        <div class="chart-name">{{ data.key }}分布情况</div>
    </div>
</template>
<script setup lang="ts">

import { Pie } from '@antv/g2plot';

defineOptions({
    name: 'PieChart',
});
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
});

const pieChartRef = ref();
const COLORSEQUENCE = ['#667798', '#6DADDB', '#9EDBFF', '#4C7D7D', '#61ABA3', '#65D9AC', '#6394FA', '#C5ADDD', '#F6B18C', '#9B6DBF', '#F6BD2E'];
function init() {
    const piePlot = new Pie(pieChartRef.value, {
        renderer: 'svg',
        appendPadding: 0,
        data: props.data.valueList,
        angleField: 'count',
        colorField: 'name',
        color: COLORSEQUENCE,
        radius: 0.7,
        label: {
            type: 'spider',
            labelHeight: 40,
            content(simpleData, metaData, index) {
                return `${simpleData.count}人\n${metaData._origin.percent}%`;
                // return `${simpleData.name.match(/.{1,3}/g).join('\n')}\n${metaData._origin.percent}%`;
            },
            style: {
                fontSize: 12,
                // lineHeight: 36,
                fontFamily: 'FZLT-ZHJ',
            },
        },
        pieStyle: {
            lineWidth: 3,
        },
        legend: false,
        tooltip: false,
    });
    piePlot.render();
}
onMounted(() => {
    init();
});
</script>
<style lang="less" scoped>
.pie-chart-container {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 21px 0 10px;
    .pie-chart-self {
        width: 280px;
        height: 216px;
    }
    .pie-chart-legend {
        color: #000;
        font-size: 12px;
        line-height: 32px;
        padding: 0 17px;
        display: flex;
        flex-wrap: wrap;
        column-gap: 7px;
        .legend-item {
            display: flex;
            align-items: center;
            .legend-indicator {
                width: 10px;
                height: 10px;
                border-radius: 100%;
                margin-right: 7px;
            }
        }
    }
    .chart-name {
        padding: 0 17px;
        text-align: center;
        margin-top: 10px;
        flex-grow: 1;
        display: flex;
        align-items: end;
    }
}
</style>
