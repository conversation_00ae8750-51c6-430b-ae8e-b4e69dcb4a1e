<template>
    <div class="sub-title">{{ title }}<slot></slot></div>
</template>
<script setup lang="ts">
defineOptions({
    name: 'SubTitle'
});
const props = defineProps({
    title: String
});
</script>
<style lang="less" scoped>
.sub-title {
    font-family: var(--title-sub-font-family);
    font-size: 18px;
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    line-height: 32px;
    &::before {
        content: '';
        width: 5px;
        height: 19px;
        background: #00bebd;
        border-radius: 3px;
        margin-right: 12px;
        position: relative;
        top: -1px;
    }
    :deep(.level) {
        font-family: var(--title-main-font-family);
        font-size: 21px;
        height: 32px;
        line-height: 32px;
        padding: 0 5px;
        color: var(--primary-color);
        background: #e5f9f9;
        border-radius: 5px;
        border: 1px solid rgba(0, 166, 167, 0.37);
        margin-left: 8px;
    }
}
</style>
