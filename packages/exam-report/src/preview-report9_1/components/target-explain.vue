<template>
    <div class="target-explain-wrap">
        <div class="tit-wrap">
            <p class="tit">{{ data.name }}</p>
            <p class="number">{{ data.score }}</p>
            <LevelIcon :level="data.level"></LevelIcon>
        </div>
        <div class="chart-info-wrap">
            <div class="item-wrap">
                <div class="chart">
                    <GroupBar
                        :blankMargins="5"
                        :lineNumber="4"
                        :legends="['个人', '常模']"
                        :dataSource="[
                            {
                                label: '个体',
                                children: [
                                    { type: '个体', value: Number(data.score).toFixed(2), color: '#9B6DBF' },
                                ]
                            },
                            {
                                label: '常模',
                                children: [
                                    { type: '常模', value: Number(data.normalScore).toFixed(2), color: '#F6BD2E' },
                                ]
                            }
                        ]"
                        :maxNum="Math.max(Number(data.score), Number(data.normalScore), 100)"
                    ></GroupBar>
                </div>
                <div class="text-wrap desc-font">{{ data.desc }}</div>
            </div>
        </div>
        <div class="desc-info-bar desc-font">
           {{ data.levelLanguage }}
        </div>
    </div>
</template>
<script setup lang="ts">

import GroupBar from './group-bar.vue';
import LevelIcon from './level-icon.vue';

const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    title: {
        type: Object,
        default: () => ({}),
    },
    info: {
        type: Object,
        default: () => ({}),
    }
});

const levelStrMap = {
    '1': '低',
    '2': '等',
    '4': '高',
};

const levelStrMaps = {
    '1': '低',
    '2': '中',
    '4': '高',
};
</script>
<style lang="less" scoped>
.target-explain-wrap {
    margin-top: 22px;

    .tit-wrap {
        margin: 0 0 13px;
        display: flex;
        align-items: center;

        .tit {
            color: #1F1F1F;
            font-family: "FZLanTingHeiS-DB-GB";
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
        }

        .number {
            margin: 0 15px;
            color: #0092FA;
            font-family: "Kanzhun";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
        }
    }

    .chart-info-wrap {
        .item-wrap {
            display: flex;

            .chart {
                width: 295px;
            }
            .text-wrap {
                margin-left: 20px;
                color: #5C5C5C;
                flex: 1;
            }
        }
    }

    .desc-info-bar {
        margin-top: 10px;
        color: #1F1F1F;
        padding: 8px 17px 9px;
        background: #F1F9FF;
        border-radius: 10px;
    }
}
</style>
