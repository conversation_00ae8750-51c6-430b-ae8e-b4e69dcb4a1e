<template>
    <div class="quiz-detail-chart-wrap" :style="{ marginTop: $slots.title ? '0px' : '34px' }">
        <slot name="title"></slot>
        <div class="tit-wrap" :data-title-level="titleLevel" :data-title-text="`${index}、${data.name}指数`">
            <p class="text-tit" style="margin-right: 10px">
                <span>{{ index }}{{ data.name }}：</span>
                <span class="number" :class="`number${data.level}`"> {{ data.score }}</span>
            </p>
            <LevelIcon :level="data.level" :percent="data.percent" :percentStr="percentStr"></LevelIcon>
        </div>
        <p v-if="data.desc" class="desc-font" style="margin-top: 10px">{{ data.desc }}</p>
        <p v-if="data.levelLanguage" class="desc-tag-wrap desc-font">
            {{ data.levelLanguage }}
        </p>
        <GroupBar
            :legends="['个人', '常模']"
            :dataSource="[
                {
                    label: '个体',
                    children: [{ type: '个体', value: Number(data.score).toFixed(2), color: '#9B6DBF' }],
                },
                {
                    label: '常模',
                    children: [{ type: '常模', value: Number(data.normalScore).toFixed(2), color: '#F6BD2E' }],
                },
            ]"
            :maxNum="Math.ceil(Math.max(Number(data.score), Number(data.normalScore), 100) / 10) * 10"
        ></GroupBar>
        <div class="info-desc" style="margin-top: 10px">
            <span>个体的{{ data.name }}指数为{{ data.score }}，{{ data.name }}指数常模为{{ data.normalScore }}。个体指数</span>
            <span v-if="data.score > data.normalScore">高</span>
            <span v-if="data.score === data.normalScore">中</span>
            <span v-if="data.score < data.normalScore">低</span>
            <span>于常模，{{ levelStrMap[data.level] }}水平，{{ percentStr }}{{ data.percent }}%的人。</span>
        </div>
    </div>
</template>
<script setup lang="ts">
import LevelIcon from './level-icon.vue';
import Bar from './bar.vue';
import GroupBar from './group-bar.vue';

const levelStrMap = {
    1: '低',
    2: '中',
    4: '高',
};
const props = defineProps({
    index: {
        type: String,
        default: () => '1',
    },
    data: {
        type: Object,
        default: () => ({}),
    },
    titleLevel: {
        type: String,
    },
    chartWidth: {
        type: Number,
        default: 700,
    },
    percentStr: {
        type: String,
        default: '超越',
    },
});
</script>
<style lang="less" scoped>
.quiz-detail-chart-wrap {
    .tit-wrap {
        display: flex;
        align-items: center;

        .number {
            font-family: 'Kanzhun';
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: 31px;

            &.number1 {
                color: #e7a13e;
            }
            &.number2 {
                color: #49c7c7;
            }
            &.number4 {
                color: #0092fa;
            }
        }
    }

    .desc-tag-wrap {
        margin: 6px 0 10px 0;
        padding: 10px;
        background: #f1f9ff;
        border-radius: 10px;
    }

    .info-desc {
        margin-top: 10px;
        color: #5c5c5c;
        font-family: 'FZLanTingHeiS-R-GB';
        font-size: 13px;
        font-style: normal;
        font-weight: 400;
        line-height: 22px;
    }
    .flex-box {
        display: flex;
        gap: 11px;
        justify-content: center;
    }
    .container {
        width: 337px;
        height: 296px;
        flex-shrink: 0;
        background: #ffffff;
        border: 1px solid #11a1fc;
        box-sizing: content-box;
    }
}
</style>
