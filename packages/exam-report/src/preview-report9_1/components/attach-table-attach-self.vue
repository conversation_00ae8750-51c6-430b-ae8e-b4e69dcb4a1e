<template>
    <div class="attach-table-header">
        <div class="td" v-for="item of thead" :key="item">{{ item }}</div>
    </div>
    <template v-for="(itemL1, indexL1) of info" :key="indexL1">
        <div
            class="attach-table-row"
            :class="`${indexL2 % 2 === 0 ? 'attach-table-row-odd' : 'attach-table-row-even'}`"
            v-for="(itemL2, indexL2) of itemL1.subDimensionInfoNewList"
            :key="indexL2"
            :data-attach-l1-index="indexL1"
        >
            <div class="td">{{ itemL1.dimensionName }}</div>
            <div class="td">{{ itemL2.dimensionName }}</div>
            <div class="td">{{ itemL2.description }}</div>
        </div>
    </template>
</template>
<script setup lang="ts">

defineOptions({
    name: 'AttachTableAttachSelf'
});
const props = defineProps({
    info: {
        type: Array<any>,
        default: () => []
    }
});
const thead = computed(() => {
    return ['一级维度', '二级维度', '维度说明'];
});
</script>
<style lang="less" scoped>
.attach-table-header {
    background-color: var(--b-table-header-background);
    display: flex;
    border-radius: var(--b-table-border-radius) var(--b-table-border-radius) 0 0;
    text-align: center;
    font-family: var(--title-sub-font-family);
    .td {
        font-size: 13px;
        line-height: 26px;
        flex-shrink: 0;
        position: relative;
        padding: 10px 0 6px;
        &:nth-child(1) {
            width: 120px;
        }
        &:nth-child(2) {
            width: 120px;
        }
        &:nth-child(3) {
            flex-grow: 1;
        }
    }
}
.attach-table-row {
    display: flex;
    &.attach-table-row-even {
        // background-color: var(--b-table-zebra-bg-color);
    }
    &[style] {
        border-radius: 0 0 var(--b-table-border-radius) var(--b-table-border-radius);
    }
    .td {
        font-size: 13px;
        line-height: 26px;
        flex-shrink: 0;
        word-break: break-all;
        white-space: pre-wrap;
        display: flex;
        align-items: center;
        padding: 11px 20px;
        &:nth-child(1) {
            width: 120px;
            justify-content: center;
        }
        &:nth-child(2) {
            width: 120px;
            justify-content: center;
        }
        &:nth-child(3) {
            flex-grow: 1;
            flex-basis: 0;
        }
    }
}
</style>
