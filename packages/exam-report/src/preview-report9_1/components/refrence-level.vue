<template>
    <div class="refrence-level-wrap">
        <SubTitle class="refrence-level-title" title="报告参考性">
            <div
                class="level"
                v-if="info.indicativeResultDesc"
                :style="{
                    color: levelMap[info.indicativeResultDesc].fontColor,
                    backgroundColor: levelMap[info.indicativeResultDesc].bgColor,
                    borderColor: levelMap[info.indicativeResultDesc].borderColor,
                }"
            >
                {{ info.indicativeResultDesc }}
            </div>
        </SubTitle>
        <div style="height: 20px;"></div>
        <!-- <div class="refrence-time">作答起止时间: {{ info.answerStartTime }} 至 {{ info.answerEndTime }}</div> -->
        <Table
            border
            :columns="[
                {
                    label: 1,
                    field: 'showName',
                    width: 155,
                    thProps: {
                        colSpan: 2,
                    },
                },
                {
                    label: 2,
                    field: 'result',
                    thProps: {
                        colSpan: 0,
                    },
                },
            ]"
            :tableData="[{
                showName: '作答起止时间',
                result: `${info.answerStartTime} 至 ${info.answerEndTime}`,
            },...info.indicativeList]"
        >
            <template #th-showName>
                <span style="font-size: 13px; font-family: var(--title-sub-font-family); line-height: 26px">报告参考性：</span>
                <span
                    :style="{
                        color: levelMap[info.indicativeResultDesc].fontColor,
                        'line-height': '26px',
                    }"
                >
                    <span style="font-family: var(--title-sub-font-family)">{{ info.indicativeResultDesc }}</span>
                    <span>{{ levelMap[info.indicativeResultDesc].desc }}</span>
                </span>
            </template>
            <template #td-indicatorMeaning="{ raw }">
                <div v-html="raw.indicatorMeaning"></div>
            </template>
        </Table>
    </div>
</template>
<script setup lang="ts">
import SubTitle from './sub-title.vue';
import { Table } from '@boss/design';
import '@boss/design/es/table/style.js';

defineOptions({
    name: 'RefrenceLevel',
});
const props = defineProps({
    info: {
        type: Object,
        default: () => ({}),
    },
});
const levelMap: any = {
    高: {
        fontColor: '#0092FA',
        borderColor: 'rgba(0, 146, 250, 0.37)',
        bgColor: '#CCE9FE',
        desc: '（报告结果，具有较高参考性）',
    },
    中: {
        fontColor: '#49C7C7',
        borderColor: 'rgba(0, 166, 167, 0.37)',
        bgColor: '#E5F9F9',
        desc: '（报告结果，可选择性参考使用）',
    },
    低: {
        fontColor: '#E7A13E',
        borderColor: 'rgba(231, 161, 62, 0.3)',
        bgColor: '#FAECD8',
        desc: '（报告结果，需谨慎参考）',
    },
};
</script>
<style lang="less" scoped>

.sub-title-flex-box {
    margin-bottom: 22px;
    display: flex;
    align-items: center;

    .sub-title {
        margin-bottom: 0 !important;
    }

    .highlight-number {
        margin: 0 10px;
        padding-bottom: 4px;
    }
}

.refrence-level-wrap {
    --b-table-body-font-color: #1f1f1f;
    --b-table-header-font-color: #1f1f1f;
    --b-table-header-font-size: 13px;
    --b-table-body-font-size: 13px;
    --b-table-header-line-height: 24px;
    --b-table-body-line-height: 24px;

    .refrence-time {
        font-size: 13px;
        line-height: 26px;
        margin-bottom: 12px;
    }

    :deep(.b-table) {
        &::before {
            display: none;
        }
        .b-table-container {
            .b-table-element {
                border-bottom-left-radius: var(--b-table-border-radius);
                border-bottom-right-radius: var(--b-table-border-radius);
                overflow: hidden;
            }
        }
        .b-table-th {
            height: 42px;
            padding: 8px 30px;
            .b-table-th-wraper {
                justify-content: center;
            }
        }
        .b-table-td {
            height: 48px;
            &:nth-child(1) {
                padding: 11px 30px;
            }
            &:nth-child(2) {
                padding: 11px 41px;
            }
        }
        .b-table-tbody {
            .b-table-cell {
                &::before {
                    border-left: 1px solid var(--border-color);
                    border-bottom: 1px solid var(--border-color);
                }
                &:last-child {
                    &::before {
                        border-right: 1px solid var(--border-color);
                    }
                }
            }
            .b-table-tr:last-child {
                .b-table-cell:first-child {
                    &::before {
                        border-bottom-left-radius: var(--b-table-border-radius);
                    }
                }
                .b-table-cell:last-child {
                    &::before {
                        border-bottom-right-radius: var(--b-table-border-radius);
                    }
                }
            }
            // .b-table-tr:nth-child(even),
            // .b-table-tr:nth-child(even):hover {
            //     --b-table-background-color: var(--b-table-zebra-bg-color);
            //     --b-table-row-hover-background: var(--b-table-zebra-bg-color) !important;
            // }
            .b-table-tr:hover {
                --b-table-row-hover-background: #fff !important;
            }
        }
    }
    .refrence-level-title {
        margin-bottom: 8px;
    }
    .extra-desc {
        margin-top: 12px;
        line-height: 26px;
    }
}
</style>
