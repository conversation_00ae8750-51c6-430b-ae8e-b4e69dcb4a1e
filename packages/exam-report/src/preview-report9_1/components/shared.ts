export function getMaxScaleValue(data: any[], limit?: number) {
    let value = 0;
    data.forEach((item) => {
        if (item.value > value) {
            value = item.value;
        }
    });

    if (value < 5) {
        return 5;
    }

    if (value < 10) {
        return 10;
    }

    // Step 1: Get the first two digits of the integer part of the value
    const strValue = value.toString();
    const decimalIndex = strValue.indexOf('.');
    const integerPart = decimalIndex === -1 ? strValue : strValue.slice(0, decimalIndex);
    let firstTwoDigits = parseInt(integerPart.slice(0, 2), 10);

    // Round the first two digits to the nearest multiple of 5
    if (firstTwoDigits % 5 !== 0) {
        firstTwoDigits = Math.ceil(firstTwoDigits / 5) * 5;
    }

    // Form the new value with the rounded first two digits
    const scaleFactor = Math.pow(10, integerPart.length - 2);
    let newValue = firstTwoDigits * scaleFactor;

    // Step 2: Check if the new value is greater than or equal to the original value
    if (newValue < value) {
        // If not, round the first two digits again to the next multiple of 5
        firstTwoDigits += 5;
        newValue = firstTwoDigits * scaleFactor;
    }

    return limit ? Math.min(newValue, limit) : newValue;
}
