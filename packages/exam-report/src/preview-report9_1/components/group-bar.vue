<template>
    <div class="bar-wrap">
        <div class="group-bar-wrap">
            <div class="line-group">
                <!-- <div class="line-item" >
                    <p class="num">0</p>
                </div> -->
                <div v-for="(item, index) in lineNumber + 1" :key="index" class="line-item" >
                    <!-- :style="{ width: `${ scaleNum >= 1 ? unitWidth * Math.ceil(scaleNum) : unitWidth * scaleNum }px`}" -->
                    <!-- <p class="num">{{ scaleNum >= 1 ? Math.ceil(index * scaleNum) : (index * scaleNum).toFixed(2) }}</p> -->
                    
                    <p class="num">{{ Math.ceil((maxNum / lineNumber) * (index)) }}</p>
                </div>
            </div>
            <ul :style="{padding: `${blankMargins}px 0`}">
                <li v-for="(item, index) in dataSource" :key="index">
                    <p class="label">{{ insertNewlineEveryThreeChars(item.label) }}</p>
                    <div class="grid-group" style="width: calc(100% - 103px)">
                        <template v-for="(subItem, i) in (item.children)" :key="i">
                            <div
                                class="grid-group-item"
                                :style="{ 
                                    background: subItem.color || color[i], 
                                    width: `${(subItem.value / maxNum) * 100 }%` 
                                }"
                            >
                                <p v-if="isShowContent" class="num">{{ toFixedNum ? subItem.value?.toFixed(toFixedNum) : subItem.value }}</p>
                            </div>
                        </template>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</template>
<script setup lang="ts">

import { Bar } from '@antv/g2plot';

const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    color: {
        type: Array,
        default: () => ['#667798', '#6CADDB', '#9EDBFF', '#4B7D7D', '#61ABA3', '#65D9AC', '#6394F3', '#C4ADDD', '#F6B18C']
    },
    dataSource: {
        type: Array,
        default: () => []
    },
    splitNum: {
        type: Number,
        default: 3
    },
    itemHeight: {
        type: Number,
        default: 15
    },
    legends: {
        type: Array,
        default: () => []
    },
    maxNum: {
        type: Number,
        default: 100
    },
    isShowContent: {
        type: Boolean,
        default: true
    },
    toFixedNum: {
        type: Number,
        default: 0
    },
    lineNumber: {
        type: Number,
        default: 10
    },
    step: {
        type: Number,
        default: 10
    },
    blankMargins: {
        type: Number,
        default: 10
    }
});
const chartWidth = 630;
const scaleNum = computed(() => props.maxNum / 10);
const unitWidth = computed(() => chartWidth /  props.maxNum);
const insertNewlineEveryThreeChars = (str: string) => {
    let result = '';
    for (let i = 0; i < str?.length; i += props.splitNum) {
        result += str.slice(i, i + props.splitNum);
        if (i + props.splitNum < str?.length) result += '\n';
    }
    return result;
};
</script>
<style scoped lang="less">
.title {
    color: #1f1f1f;
    font-family: FZLT-HJ;
    font-size: 12px;
    font-weight: 400;
    line-height: 26px;
    text-align: center;
    transform: translate(-20px, 8px);
    white-space: pre-wrap;
}
.legend-box {
    text-align: center;
    margin-top: 36px;
    .legend-item {
        color: #000000;
        font-family: FZLT-HJ;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 26px;
        padding-right: 28px;
        span {
            width: 12px;
            height: 10px;
            display: inline-block;
            margin-right: 9px;
            transform: translateY(1px);
        }
    }
}
.bar-wrap {
    padding-bottom: 30px;
}

.group-bar-wrap {
    position: relative;
    display: flex;
    justify-content: space-between;
    flex-direction: column;

    ul {
        position: relative;
    }
    // ul > :first-child {
    //     padding-top: 16px;
    // }
    // ul > :last-child {
    //     padding-bottom: 16px;
    // }
    li {
        padding-bottom: 16px;
        display: flex;
        align-items: center;

        &:last-child {
            padding-bottom: 0;
        }

        .label {
            min-width: 60px;
            white-space: pre-line;
            text-align: center;
            color: #000000;
            font-family: Kanzhun;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            display: flex;
            align-items: center;
            justify-content: center;
            transform: translateX(-7px);
        }
        .grid-group {
            display: flex;
            flex-direction: column;
        }
        .grid-group-item {
            position: relative;
            height: 15px;
            text-align: end;

            .num {
                position: absolute;
                left: 100%;
                color: #000000;
                font-family: FZLT-HJ;
                font-size: 12px;
                font-style: normal;
                font-weight: 400;
                line-height: 15px;
                margin-left: 10px;
                text-wrap: nowrap;
            }
        }
    }
}
.line-group {
    height: 100%;
    position: absolute;
    left: 60px;
    right: 40px;
    display: flex;
    justify-content: space-between;

    .line-item {
        height: 100%;
        border-left: 1px solid #dcdcdc;
        transform: translateX(-1px);
        position: relative;

        .num {
            width: 27px;
            text-align: center;
            color: #808080;
            font-family: FZLT-HJ;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 26px;
            position: absolute;
            bottom: -33px;
            left: -13px;
            text-wrap: nowrap;
        }
    }
}
</style>
