<template>
    <div class="level-wrap">
        <div
            class="level"
            v-if="level"
            :style="{
                color: levelMap[`${level}`].fontColor,
                backgroundColor: levelMap[`${level}`].bgColor,
                borderColor: levelMap[`${level}`].borderColor,
            }"
        >
            {{ levelMap[`${level}`].font }}
        </div>
        <p v-if="percent" class="desc-font tag" :class="`tag${level}`">
            {{ percentStr || levelMap[`${level}`].tagDescText }}
            <span class="number">{{ percent }}%</span> 的人
            <em class="icon"></em>
        </p>
    </div>
</template>
<script setup lang="ts">
const props = defineProps({
    level: {
        type: Number,
        default: () => (1),
    },
    percent: {
        type: String,
        default: () => (''),
    },
    percentStr: {
        type: String,
        default: () => (''),
    }
});
const levelMap: any = {
    '1': {
        font: '低',
        fontColor: '#E7A13E',
        borderColor: 'rgba(231, 161, 62, 0.3)',
        bgColor: '#FAECD8',
        desc: '（报告结果，需谨慎参考）',
        tagDescText: '低于',
    },
    '2': {
        font: '中',
        fontColor: '#49C7C7',
        borderColor: 'rgba(0, 166, 167, 0.37)',
        bgColor: '#E5F9F9',
        desc: '（报告结果，可选择性参考使用）',
        tagDescText: '超越',
    },
    '4': {
        font: '高',
        fontColor: '#0092FA',
        borderColor: 'rgba(0, 146, 250, 0.37)',
        bgColor: '#CCE9FE',
        desc: '（报告结果，具有较高参考性）',
        tagDescText: '超越',
    },
};
</script>
<style lang="less" scoped>
.level-wrap {
    display: flex;
    align-items: center;

    .level {
        width: 32px;
        height: 32px;
        font-family: var(--title-main-font-family);
        font-size: 21px;
        line-height: 32px;
        padding: 0 5px;
        border-radius: 5px;
        border: 1px solid rgba(0, 166, 167, 0.37);
    }

    .tag {
        position: relative;
        padding: 5px 10px;
        border-radius: 4px;
        margin-left: 17px;
        color: #1F1F1F;

        .number {
            font-family: "Kanzhun";
            font-size: 18px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }

        .icon {
            position: absolute;
            left: -10px;
            top: 50%;
            transform: translateY(-50%);
            border-top: 4px solid transparent;
            border-bottom: 4px solid transparent;
            border-left: 2px solid  transparent;
            border-right: 8px solid #37C2BC;
        }

        &.tag1 {
            background: #FAECD8;

            .number {
                color: #DE952F;
            }

            .icon {
                border-right: 8px solid #FAECD8 !important;
            }
        }

        &.tag2 {
            background: #DBF4F4;

            .number {
                color: #00A6A7;;
            }

            .icon {
                border-right: 8px solid #DBF4F4 !important;
            }
        }

        &.tag4 {
            background: #CCE9FE;

            .number {
                color: #0092FA;
            }

            .icon {
                border-right: 8px solid #CCE9FE !important;
            }
        }
    }
}

</style>
