<template>
    <div class="interview-advice-item">
        <!-- <div class="title">{{ info.dimensionName }}</div> -->
        <div class="content-wrap">
            <div class="question">
                <!-- <div class="part-title">面试问题</div> -->
                <div class="content">
                    {{ props.info?.interviewQuestion }}
                </div>
            </div>
            <div class="keypoint">
                <div class="part-title">考察关键点</div>
                <div class="content">
                    {{ props.info?.examKeyPoint }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">

defineOptions({
    name: 'InterviewAdviceItem'
});
const props = defineProps({
    info: { type: Object, default: () => ({}) }
});
const questionList = computed(() => (props.info?.interviewQuestion ?? '').split('\n').map((x) => x.replace(/•\s{0,1}/, '')));
const examKeyPointList = computed(() => (props.info?.examKeyPoint ?? '').split('\n').map((x) => x.replace(/•\s{0,1}/, '')));
</script>
<style lang="less" scoped>
.interview-advice-item {
    // border-radius: 5px;
    // overflow: hidden;
    // border: 1px solid var(--border-color);
    // & + .interview-advice-item {
    //     margin-top: 20px;
    // }
    // .title {
    //     font-family: var(--title-sub-font-family);
    //     padding: 15px 12px 12px;
    //     line-height: 27px;
    //     font-size: 17px;
    //     text-align: center;
    //     background: #e8f7ff;
    //     border-radius: 5px;
    // }
    .content-wrap {
        // padding: 16px 28px;
        .question {
            margin-bottom: 7px;
        }
        .part-title {
            font-family: var(--title-sub-font-family);
            font-size: 13px;
            line-height: 26px;
            margin-bottom: 5px;
        }
        .content {
            word-break: break-all;
            white-space: pre-wrap;
            line-height: 24px;
            font-size: 12px;
            p {
                display: flex;
                align-items: baseline;

                & + p {
                    // margin-top: 8px;
                }
                // &::before {
                //     content: '';
                //     display: inline-block;
                //     width: 8px;
                //     height: 8px;
                //     background: #ccc;
                //     margin-right: 13px;
                //     border-radius: 50%;
                //     flex-shrink: 0;
                // }
            }
        }
    }
}
</style>
