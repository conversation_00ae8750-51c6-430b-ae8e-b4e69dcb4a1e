import type { DepOptimizationOptions } from 'vite'
import { resolve } from 'node:path'
import vue from '@vitejs/plugin-vue'
import { defineConfig } from 'vite'
import { jsonX } from 'vite-plugin-jsonx'
import { aliasConfig } from '../../vite.config'

export default defineConfig(({ mode }) => {
    const isProd = mode === 'prod'
    const isDev = mode === 'dev'
    const isTest = mode === 'test'

    let build = {}
    if (isProd) {
        build = {
            lib: {
                entry: resolve(__dirname, 'src/index.ts'),
                name: 'vivu-npm',
                fileName: 'index',
                formats: ['es', 'cjs', 'umd'],
            },
            assetsInlineLimit: 0,
            rollupOptions: {
                /**
                 * DESC:
                 * make sure to externalize deps that shouldn't be bundled
                 * into your library
                 */
                external: [
                    'vue',
                    '@antv/g2plot',
                    'dayjs',
                    'vue-router',
                    '@boss/design',
                ],
                output: {
                    /**
                     * DESC:
                     * Provide global variables to use in the UMD build
                     * for externalized deps
                     */
                    globals: {
                        vue: 'Vue',
                    },
                },
            },
        }
    }

    const optimizeDeps: DepOptimizationOptions = {}

    let test = {}
    if (isTest) {
        /**
         * DESC:
         * vitest config
         */
        test = {
            include: ['test/**/*.test.ts'],
            environment: 'happy-dom',
            deps: {
                inline: [
                    '@vue',
                ],
            },
            coverage: {
                reporter: [
                    'text',
                    'text-summary',
                    'lcov',
                ],
            },
        }
    }

    return {
        plugins: [vue(), jsonX()],
        optimizeDeps,
        build,
        test,

        /**
         * DESC:
         * defining aliases
         */
        resolve: {
            alias: [
                ...aliasConfig,
            ],
        },
        server: {
            host: true,
            port: 5177,
            proxy: {
                '/wapi/admin': {
                    target: 'https://zhice-admin-rd.weizhipin.com/',
                    changeOrigin: true,
                },
                '/wapi/zpxzats': {
                    target: 'https://xiaoyuan-admin-qa.weizhipin.com/',
                    changeOrigin: true,
                },
            },
        },
    }
})
