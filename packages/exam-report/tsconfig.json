{"compilerOptions": {"baseUrl": ".", "outDir": "dist", "declaration": true, "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "isolatedModules": true, "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "skipLibCheck": true, "lib": ["esnext", "dom"], "paths": {"@packages/exam-report/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "exclude": ["node_modules", "dist", "public"]}