import { execa } from 'execa';
import ora from 'ora';
import chalk from 'chalk';
import inquirer from 'inquirer';
import { glob } from 'glob';
import semver from 'semver';
import { fileURLToPath } from 'node:url';
import { dirname, join, resolve, relative } from 'node:path';
import { promises as fs } from 'node:fs';
import { CLIError, debug } from './error-handler.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * 根据命令类型获取默认超时时间
 */
function getDefaultTimeout(command, args = []) {
  // 移除所有超时限制，让所有命令都能正常运行而不被强制终止
  // 这样可以避免长期运行的进程（如开发服务器）被错误地认为是超时失败
  return 0; // 无超时限制
}

/**
 * 执行命令并返回结果
 */
export async function execCommand(command, args = [], options = {}) {
  const {
    timeout = getDefaultTimeout(command, args),
    cwd = process.cwd(),
    stdio = 'pipe',
    verbose = process.env.VERBOSE,
    dryRun = process.env.DRY_RUN
  } = options;

  const fullCommand = `${command} ${args.join(' ')}`;
  debug(`执行命令: ${fullCommand}`);

  if (dryRun) {
    console.log(chalk.gray(`[DRY RUN] ${fullCommand}`));
    return { stdout: '', stderr: '', exitCode: 0 };
  }

  try {
    // 构建 execa 选项，如果 timeout 为 0 则不设置超时
    const execaOptions = {
      cwd,
      stdio: verbose ? 'inherit' : stdio,
    };

    // 只有当 timeout > 0 时才设置超时
    if (timeout > 0) {
      execaOptions.timeout = timeout;
    }

    const result = await execa(command, args, execaOptions);

    debug(`命令执行成功: ${fullCommand}`);
    return {
      stdout: result.stdout || '',
      stderr: result.stderr || '',
      exitCode: result.exitCode || 0
    };

  } catch (error) {
    debug(`命令执行失败: ${fullCommand}, 错误: ${error.message}`);

    // 移除超时相关的错误处理，因为现在不设置超时限制
    throw new CLIError(
      `命令执行失败: ${fullCommand}\n${error.stderr || error.message}`,
      error.exitCode || 1,
      '请检查命令参数和环境配置'
    );
  }
}

/**
 * 带进度指示器的命令执行
 */
export async function execWithSpinner(message, command, args = [], options = {}) {
  const spinner = ora(message).start();

  try {
    const result = await execCommand(command, args, {
      ...options,
      stdio: 'pipe' // 强制使用 pipe 以避免干扰 spinner
    });

    spinner.succeed(`${message} - 完成`);
    return result;

  } catch (error) {
    spinner.fail(`${message} - 失败`);
    throw error;
  }
}

/**
 * 格式化文件大小
 */
export function formatBytes(bytes) {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间
 */
export function formatTime(ms) {
  if (ms < 1000) return `${ms}ms`;
  if (ms < 60000) return `${(ms / 1000).toFixed(2)}s`;
  if (ms < 3600000) return `${(ms / 60000).toFixed(2)}min`;
  return `${(ms / 3600000).toFixed(2)}h`;
}

/**
 * 查找文件
 */
export async function findFiles(pattern, options = {}) {
  const {
    cwd = process.cwd(),
    ignore = ['**/node_modules/**', '**/dist/**', '**/build/**'],
    absolute = false
  } = options;

  try {
    const files = await glob(pattern, {
      cwd,
      ignore,
      absolute,
      nodir: true
    });

    debug(`找到 ${files.length} 个文件匹配模式: ${pattern}`);
    return files;

  } catch (error) {
    throw new CLIError(
      `文件查找失败: ${error.message}`,
      2,
      '请检查文件路径和权限'
    );
  }
}

/**
 * 读取 JSON 文件
 */
export async function readJsonFile(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf8');
    return JSON.parse(content);
  } catch (error) {
    if (error.code === 'ENOENT') {
      throw new CLIError(`文件不存在: ${filePath}`, 2);
    }
    if (error instanceof SyntaxError) {
      throw new CLIError(`JSON 格式错误: ${filePath}`, 22);
    }
    throw error;
  }
}

/**
 * 写入 JSON 文件
 */
export async function writeJsonFile(filePath, data, options = {}) {
  const { indent = 2, backup = false } = options;

  try {
    // 创建备份
    if (backup && await fileExists(filePath)) {
      await fs.copyFile(filePath, `${filePath}.backup`);
    }

    // 检测原文件的缩进
    let actualIndent = indent;
    if (await fileExists(filePath)) {
      const content = await fs.readFile(filePath, 'utf8');
      const match = content.match(/^(\s+)"/m);
      if (match) {
        actualIndent = match[1];
      }
    }

    const content = JSON.stringify(data, null, actualIndent) + '\n';
    await fs.writeFile(filePath, content, 'utf8');

  } catch (error) {
    throw new CLIError(
      `写入文件失败: ${filePath} - ${error.message}`,
      13,
      '请检查文件权限和磁盘空间'
    );
  }
}

/**
 * 检查文件是否存在
 */
export async function fileExists(filePath) {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 交互式确认
 */
export async function confirm(message, defaultValue = false) {
  const { confirmed } = await inquirer.prompt({
    type: 'confirm',
    name: 'confirmed',
    message,
    default: defaultValue
  });

  return confirmed;
}

/**
 * 交互式选择
 */
export async function select(message, choices, options = {}) {
  const { multiple = false, pageSize = 10 } = options;

  const { selected } = await inquirer.prompt({
    type: multiple ? 'checkbox' : 'list',
    name: 'selected',
    message,
    choices,
    pageSize
  });

  return selected;
}

// 添加缺失的函数
export function showProgress(message, current, total) {
  const percentage = Math.round((current / total) * 100);
  const progress = '█'.repeat(Math.round(percentage / 5));
  const empty = '░'.repeat(20 - Math.round(percentage / 5));

  process.stdout.write(`\r${message} [${progress}${empty}] ${percentage}% (${current}/${total})`);

  if (current >= total) {
    console.log(); // 换行
  }
}

export function promptInput(message, defaultValue = '') {
  return inquirer.prompt({
    type: 'input',
    name: 'value',
    message,
    default: defaultValue
  }).then(answers => answers.value);
}

export function promptSelect(message, choices) {
  return inquirer.prompt({
    type: 'list',
    name: 'value',
    message,
    choices
  }).then(answers => answers.value);
}

export function promptConfirm(message, defaultValue = false) {
  return inquirer.prompt({
    type: 'confirm',
    name: 'value',
    message,
    default: defaultValue
  }).then(answers => answers.value);
}

export function ensureDir(dirPath) {
  const path = require('node:path');
  const fs = require('node:fs');

  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

export function copyFile(src, dest) {
  const fs = require('node:fs');
  const path = require('node:path');

  // 确保目标目录存在
  ensureDir(path.dirname(dest));

  fs.copyFileSync(src, dest);
}

// 别名导出
export { execCommand as executeCommand };

export {
  chalk,
  ora,
  inquirer,
  glob,
  semver
};
