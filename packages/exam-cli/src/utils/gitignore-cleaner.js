import { readFileSync, existsSync, statSync, readdirSync } from 'node:fs';
import { rm } from 'node:fs/promises';
import { join, resolve, relative, dirname, basename } from 'node:path';
import { glob } from 'glob';
import chalk from 'chalk';
import ora from 'ora';
import { CLIError } from './error-handler.js';

/**
 * .gitignore 文件清理工具
 */
export class GitignoreCleaner {
  constructor(options = {}) {
    this.options = {
      dryRun: false,
      verbose: false,
      cwd: process.cwd(),
      ...options
    };

    this.deletedFiles = [];
    this.deletedDirs = [];
    this.skippedFiles = [];
    this.errors = [];
  }

  /**
   * 解析 .gitignore 文件
   */
  parseGitignore(gitignorePath) {
    if (!existsSync(gitignorePath)) {
      return [];
    }

    const content = readFileSync(gitignorePath, 'utf-8');
    const lines = content.split('\n');
    const patterns = [];

    for (let line of lines) {
      line = line.trim();

      // 跳过空行和注释
      if (!line || line.startsWith('#')) {
        continue;
      }

      // 处理否定模式（以 ! 开头）
      const isNegation = line.startsWith('!');
      if (isNegation) {
        line = line.substring(1);
      }

      patterns.push({
        pattern: line,
        isNegation,
        source: gitignorePath
      });
    }

    return patterns;
  }

  /**
   * 收集所有 .gitignore 文件
   */
  async collectGitignoreFiles(rootDir = this.options.cwd) {
    const gitignoreFiles = [];

    // 主 .gitignore 文件
    const mainGitignore = join(rootDir, '.gitignore');
    if (existsSync(mainGitignore)) {
      gitignoreFiles.push(mainGitignore);
    }

    // 查找子目录中的 .gitignore 文件
    try {
      const subGitignores = await glob('**/.gitignore', {
        cwd: rootDir,
        ignore: ['node_modules/**', '.git/**'],
        absolute: true
      });

      gitignoreFiles.push(...subGitignores.filter(file => file !== mainGitignore));
    } catch (error) {
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️  查找 .gitignore 文件时出错: ${error.message}`));
      }
    }

    return gitignoreFiles;
  }

  /**
   * 解析所有 .gitignore 规则
   */
  async parseAllGitignores() {
    const gitignoreFiles = await this.collectGitignoreFiles();
    const allPatterns = [];

    for (const gitignoreFile of gitignoreFiles) {
      const patterns = this.parseGitignore(gitignoreFile);
      const gitignoreDir = dirname(gitignoreFile);

      // 为每个模式添加相对路径信息
      for (const pattern of patterns) {
        allPatterns.push({
          ...pattern,
          baseDir: gitignoreDir,
          relativePath: relative(this.options.cwd, gitignoreDir)
        });
      }
    }

    return allPatterns;
  }

  /**
   * 检查文件是否应该被忽略
   */
  shouldIgnore(filePath, patterns) {
    const relativePath = relative(this.options.cwd, filePath);
    let shouldIgnore = false;

    for (const { pattern, isNegation, baseDir } of patterns) {
      const patternRelativeToRoot = relative(this.options.cwd, join(baseDir, pattern));

      // 使用 glob 模式匹配
      try {
        const isMatch = this.matchPattern(relativePath, patternRelativeToRoot);

        if (isMatch) {
          if (isNegation) {
            shouldIgnore = false; // 否定模式，不忽略
          } else {
            shouldIgnore = true; // 正常模式，忽略
          }
        }
      } catch (error) {
        if (this.options.verbose) {
          console.warn(chalk.yellow(`⚠️  模式匹配错误: ${pattern} - ${error.message}`));
        }
      }
    }

    return shouldIgnore;
  }

  /**
   * 模式匹配
   */
  matchPattern(filePath, pattern) {
    // 处理目录模式（以 / 结尾）
    if (pattern.endsWith('/')) {
      pattern = pattern.slice(0, -1);
      return filePath === pattern || filePath.startsWith(pattern + '/');
    }

    // 处理绝对路径模式（以 / 开头）
    if (pattern.startsWith('/')) {
      pattern = pattern.substring(1);
      return filePath === pattern || filePath.startsWith(pattern + '/');
    }

    // 处理通配符模式
    if (pattern.includes('*') || pattern.includes('?')) {
      // 使用简单的通配符匹配
      const regexPattern = pattern
        .replace(/\./g, '\\.')
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');

      const regex = new RegExp(`^${regexPattern}$`);
      return regex.test(filePath) || regex.test(basename(filePath));
    }

    // 精确匹配或部分路径匹配
    return filePath === pattern ||
           filePath.startsWith(pattern + '/') ||
           filePath.endsWith('/' + pattern) ||
           filePath.includes('/' + pattern + '/') ||
           basename(filePath) === pattern;
  }

  /**
   * 安全检查 - 确保不删除重要文件
   */
  isSafeToDelete(filePath) {
    const relativePath = relative(this.options.cwd, filePath);

    // 永远不删除的文件和目录
    const protectedPaths = [
      '.git',
      '.gitignore',
      'package.json',
      'pnpm-workspace.yaml',
      'yarn.lock',
      'pnpm-lock.yaml',
      'README.md',
      'LICENSE',
      'src',
      'lib',
      'packages',
      'apps',
      '.github',
      '.vscode/settings.json',
      '.vscode/extensions.json'
    ];

    // 检查是否是受保护的路径
    for (const protectedPath of protectedPaths) {
      if (relativePath === protectedPath || relativePath.startsWith(protectedPath + '/')) {
        return false;
      }
    }

    // 检查是否是源代码文件
    const sourceExtensions = ['.js', '.ts', '.jsx', '.tsx', '.vue', '.css', '.scss', '.less', '.html', '.md'];
    const ext = filePath.substring(filePath.lastIndexOf('.'));

    if (sourceExtensions.includes(ext)) {
      // 如果是在 src、lib、packages 等目录下的源代码文件，不删除
      if (relativePath.match(/^(src|lib|packages|apps)\//)) {
        return false;
      }
    }

    return true;
  }

  /**
   * 查找匹配的文件和目录
   */
  async findMatchingFiles(patterns) {
    const matchingFiles = [];
    const matchingDirs = [];

    try {
      // 获取所有文件和目录
      const allFiles = await glob('**/*', {
        cwd: this.options.cwd,
        dot: true,
        ignore: ['.git/**'],
        absolute: true
      });

      for (const filePath of allFiles) {
        if (this.shouldIgnore(filePath, patterns) && this.isSafeToDelete(filePath)) {
          try {
            const stats = statSync(filePath);
            if (stats.isDirectory()) {
              matchingDirs.push(filePath);
            } else {
              matchingFiles.push(filePath);
            }
          } catch (error) {
            // 文件可能已经被删除或无法访问
            if (this.options.verbose) {
              console.warn(chalk.yellow(`⚠️  无法访问文件: ${filePath}`));
            }
          }
        }
      }
    } catch (error) {
      throw new CLIError(`查找文件时出错: ${error.message}`);
    }

    return { files: matchingFiles, dirs: matchingDirs };
  }

  /**
   * 删除单个文件或目录
   */
  async deleteItem(itemPath, isDirectory = false) {
    const relativePath = relative(this.options.cwd, itemPath);

    if (this.options.dryRun) {
      console.log(chalk.gray(`[DRY RUN] 将删除 ${isDirectory ? '目录' : '文件'}: ${relativePath}`));
      return true;
    }

    try {
      await rm(itemPath, { recursive: true, force: true });

      if (isDirectory) {
        this.deletedDirs.push(relativePath);
      } else {
        this.deletedFiles.push(relativePath);
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`✓ 已删除 ${isDirectory ? '目录' : '文件'}: ${relativePath}`));
      }

      return true;
    } catch (error) {
      this.errors.push({ path: relativePath, error: error.message });

      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️  删除失败: ${relativePath} - ${error.message}`));
      }

      return false;
    }
  }

  /**
   * 执行清理操作
   */
  async clean() {
    const spinner = ora('解析 .gitignore 文件...').start();

    try {
      // 解析所有 .gitignore 规则
      const patterns = await this.parseAllGitignores();

      if (patterns.length === 0) {
        spinner.warn('未找到 .gitignore 文件');
        return this.getResult();
      }

      spinner.text = '查找匹配的文件和目录...';

      // 查找匹配的文件和目录
      const { files, dirs } = await this.findMatchingFiles(patterns);

      if (files.length === 0 && dirs.length === 0) {
        spinner.succeed('没有找到需要清理的文件');
        return this.getResult();
      }

      spinner.succeed(`找到 ${files.length} 个文件和 ${dirs.length} 个目录需要清理`);

      // 显示将要删除的内容
      if (this.options.verbose || this.options.dryRun) {
        console.log(chalk.cyan('\n📋 将要删除的内容:'));

        if (dirs.length > 0) {
          console.log(chalk.yellow('目录:'));
          dirs.slice(0, 10).forEach(dir => {
            console.log(`  📁 ${relative(this.options.cwd, dir)}`);
          });
          if (dirs.length > 10) {
            console.log(chalk.gray(`  ... 还有 ${dirs.length - 10} 个目录`));
          }
        }

        if (files.length > 0) {
          console.log(chalk.yellow('文件:'));
          files.slice(0, 10).forEach(file => {
            console.log(`  📄 ${relative(this.options.cwd, file)}`);
          });
          if (files.length > 10) {
            console.log(chalk.gray(`  ... 还有 ${files.length - 10} 个文件`));
          }
        }
        console.log();
      }

      if (this.options.dryRun) {
        console.log(chalk.blue('🔍 试运行模式，不会实际删除文件'));
        return this.getResult();
      }

      // 执行删除操作
      const deleteSpinner = ora('清理文件和目录...').start();

      let processed = 0;
      const total = files.length + dirs.length;

      // 先删除文件
      for (const file of files) {
        await this.deleteItem(file, false);
        processed++;
        deleteSpinner.text = `清理进度: ${processed}/${total} (${Math.round(processed / total * 100)}%)`;
      }

      // 再删除目录（按深度排序，先删除深层目录）
      const sortedDirs = dirs.sort((a, b) => b.split('/').length - a.split('/').length);
      for (const dir of sortedDirs) {
        await this.deleteItem(dir, true);
        processed++;
        deleteSpinner.text = `清理进度: ${processed}/${total} (${Math.round(processed / total * 100)}%)`;
      }

      deleteSpinner.succeed('清理完成');

      return this.getResult();

    } catch (error) {
      spinner.fail('清理失败');
      throw new CLIError(`清理操作失败: ${error.message}`);
    }
  }

  /**
   * 获取清理结果
   */
  getResult() {
    return {
      deletedFiles: this.deletedFiles,
      deletedDirs: this.deletedDirs,
      skippedFiles: this.skippedFiles,
      errors: this.errors,
      totalDeleted: this.deletedFiles.length + this.deletedDirs.length,
      hasErrors: this.errors.length > 0
    };
  }

  /**
   * 显示清理结果
   */
  displayResult(result) {
    console.log(chalk.cyan('\n📊 清理结果汇总'));
    console.log('='.repeat(50));

    console.log(chalk.green(`✅ 删除文件: ${result.deletedFiles.length} 个`));
    console.log(chalk.green(`✅ 删除目录: ${result.deletedDirs.length} 个`));

    if (result.errors.length > 0) {
      console.log(chalk.red(`❌ 错误: ${result.errors.length} 个`));
    }

    if (this.options.verbose && result.deletedFiles.length > 0) {
      console.log(chalk.green('\n已删除的文件:'));
      result.deletedFiles.slice(0, 20).forEach(file => {
        console.log(`  📄 ${file}`);
      });
      if (result.deletedFiles.length > 20) {
        console.log(chalk.gray(`  ... 还有 ${result.deletedFiles.length - 20} 个文件`));
      }
    }

    if (this.options.verbose && result.deletedDirs.length > 0) {
      console.log(chalk.green('\n已删除的目录:'));
      result.deletedDirs.forEach(dir => {
        console.log(`  📁 ${dir}`);
      });
    }

    if (result.errors.length > 0) {
      console.log(chalk.red('\n删除失败的项目:'));
      result.errors.forEach(({ path, error }) => {
        console.log(`  ❌ ${path}: ${error}`);
      });
    }
  }
}
