import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { existsSync, readFileSync } from 'node:fs';
import { join } from 'node:path';
import { glob } from 'glob';
import { CLIError } from '../utils/error-handler.js';
import { executeCommand } from '../utils/common.js';

/**
 * 构建管理器类
 */
class BuildManager {
  constructor(options = {}) {
    this.options = options;
    this.cwd = process.cwd();
    this.packageManager = this.detectPackageManager();
  }

  /**
   * 检测包管理器
   */
  detectPackageManager() {
    if (existsSync(join(this.cwd, 'pnpm-lock.yaml'))) return 'pnpm';
    if (existsSync(join(this.cwd, 'yarn.lock'))) return 'yarn';
    if (existsSync(join(this.cwd, 'package-lock.json'))) return 'npm';
    return 'pnpm';
  }

  /**
   * 检测项目类型和构建工具
   */
  detectBuildTools() {
    const tools = [];

    // 检查配置文件
    const configFiles = [
      { file: 'vite.config.js', tool: 'vite' },
      { file: 'vite.config.ts', tool: 'vite' },
      { file: 'webpack.config.js', tool: 'webpack' },
      { file: 'rollup.config.js', tool: 'rollup' },
      { file: 'next.config.js', tool: 'next' },
      { file: 'nuxt.config.js', tool: 'nuxt' },
      { file: 'vue.config.js', tool: 'vue-cli' }
    ];

    configFiles.forEach(({ file, tool }) => {
      if (existsSync(join(this.cwd, file))) {
        tools.push(tool);
      }
    });

    // 检查 package.json 中的依赖
    const packageJsonPath = join(this.cwd, 'package.json');
    if (existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
      const deps = { ...packageJson.dependencies, ...packageJson.devDependencies };

      if (deps.vite) tools.push('vite');
      if (deps.webpack) tools.push('webpack');
      if (deps.rollup) tools.push('rollup');
      if (deps.next) tools.push('next');
      if (deps.nuxt) tools.push('nuxt');
      if (deps['@vue/cli-service']) tools.push('vue-cli');
    }

    return [...new Set(tools)];
  }

  /**
   * 获取工作空间包列表
   */
  async getWorkspacePackages() {
    const packages = [];

    // 检查 pnpm workspace
    if (existsSync(join(this.cwd, 'pnpm-workspace.yaml'))) {
      const workspaceYaml = readFileSync(join(this.cwd, 'pnpm-workspace.yaml'), 'utf-8');
      const patterns = workspaceYaml.match(/- ['"](.+?)['"]|^  - (.+)$/gm) || [];

      for (const pattern of patterns) {
        const cleanPattern = pattern.replace(/- ['"]?(.+?)['"]?/, '$1').trim();
        const matches = await glob(cleanPattern, { cwd: this.cwd });

        for (const match of matches) {
          const packageJsonPath = join(this.cwd, match, 'package.json');
          if (existsSync(packageJsonPath)) {
            const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
            packages.push({
              name: packageJson.name,
              path: match,
              version: packageJson.version,
              scripts: packageJson.scripts || {}
            });
          }
        }
      }
    }

    // 检查 yarn workspace
    else if (existsSync(join(this.cwd, 'package.json'))) {
      const rootPackageJson = JSON.parse(readFileSync(join(this.cwd, 'package.json'), 'utf-8'));
      if (rootPackageJson.workspaces) {
        const patterns = Array.isArray(rootPackageJson.workspaces)
          ? rootPackageJson.workspaces
          : rootPackageJson.workspaces.packages || [];

        for (const pattern of patterns) {
          const matches = await glob(pattern, { cwd: this.cwd });

          for (const match of matches) {
            const packageJsonPath = join(this.cwd, match, 'package.json');
            if (existsSync(packageJsonPath)) {
              const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
              packages.push({
                name: packageJson.name,
                path: match,
                version: packageJson.version,
                scripts: packageJson.scripts || {}
              });
            }
          }
        }
      }
    }

    return packages;
  }

  /**
   * 构建单个包
   */
  async buildPackage(packagePath, options = {}) {
    const packageJsonPath = join(this.cwd, packagePath, 'package.json');
    if (!existsSync(packageJsonPath)) {
      throw new CLIError(`找不到包文件: ${packageJsonPath}`);
    }

    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    const scripts = packageJson.scripts || {};

    // 确定构建命令
    let buildCommand = 'build';
    if (options.script) {
      buildCommand = options.script;
      // 检查指定的脚本是否存在
      if (!scripts[buildCommand]) {
        console.log(chalk.yellow(`⚠️  包 ${packageJson.name} 没有 ${buildCommand} 脚本，跳过构建`));
        return { success: true, package: packageJson.name, skipped: true, reason: `没有 ${buildCommand} 脚本` };
      }
    } else if (!scripts.build) {
      // 尝试其他可能的构建脚本
      if (scripts['build:prod']) buildCommand = 'build:prod';
      else if (scripts['build:production']) buildCommand = 'build:production';
      else if (scripts.compile) buildCommand = 'compile';
      else {
        console.log(chalk.yellow(`⚠️  包 ${packageJson.name} 没有构建脚本，跳过构建`));
        return { success: true, package: packageJson.name, skipped: true, reason: '没有构建脚本' };
      }
    }

    // 显示构建开始信息
    console.log(chalk.cyan.bold(`🏗️  构建包: ${packageJson.name}`));
    console.log(chalk.gray(`   路径: ${packagePath}`));
    console.log(chalk.gray(`   命令: ${this.packageManager} run ${buildCommand}`));
    console.log(''); // 空行分隔

    try {
      const cmd = `${this.packageManager} run ${buildCommand}`;
      // 默认显示构建日志，除非明确设置为静默模式
      const showLogs = !this.options.silent;

      await executeCommand(cmd, {
        cwd: join(this.cwd, packagePath),
        stdio: showLogs ? 'inherit' : 'pipe'
      });

      console.log(''); // 空行分隔
      console.log(chalk.green.bold(`✅ ${packageJson.name} 构建完成`));
      return { success: true, package: packageJson.name };
    } catch (error) {
      console.log(''); // 空行分隔
      console.log(chalk.red.bold(`❌ ${packageJson.name} 构建失败`));
      return { success: false, package: packageJson.name, error: error.message };
    }
  }

  /**
   * 构建工作空间
   */
  async buildWorkspace(options = {}) {
    const packages = await this.getWorkspacePackages();

    if (packages.length === 0) {
      console.log(chalk.yellow('未检测到工作空间包'));
      return;
    }

    console.log(chalk.cyan.bold(`🏗️  构建工作空间 (${packages.length} 个包)`));

    // 选择要构建的包
    let selectedPackages = packages;
    if (options.interactive) {
      const { selected } = await inquirer.prompt([
        {
          type: 'checkbox',
          name: 'selected',
          message: '选择要构建的包:',
          choices: packages.map(pkg => ({
            name: `${pkg.name} (${pkg.path})`,
            value: pkg,
            checked: true
          }))
        }
      ]);
      selectedPackages = selected;
    }

    if (options.package) {
      selectedPackages = packages.filter(pkg =>
        pkg.name.includes(options.package) || pkg.path.includes(options.package)
      );
    }

    const results = [];

    // 并行或串行构建
    if (options.parallel && selectedPackages.length > 1) {
      console.log(chalk.blue('🔄 并行构建模式'));
      const buildPromises = selectedPackages.map(pkg =>
        this.buildPackage(pkg.path, options)
      );
      const buildResults = await Promise.allSettled(buildPromises);

      buildResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          results.push(result.value);
        } else {
          results.push({
            success: false,
            package: selectedPackages[index].name,
            error: result.reason.message
          });
        }
      });
    } else {
      console.log(chalk.blue('🔄 串行构建模式'));
      for (const pkg of selectedPackages) {
        const result = await this.buildPackage(pkg.path, options);
        results.push(result);

        if (!result.success && options.stopOnError) {
          console.log(chalk.red('❌ 因为错误停止构建'));
          break;
        }
      }
    }

    // 显示构建结果
    this.displayBuildResults(results);
  }

  /**
   * 显示构建结果
   */
  displayBuildResults(results) {
    console.log('\n' + chalk.cyan.bold('📊 构建结果汇总'));
    console.log('='.repeat(50));

    const successful = results.filter(r => r.success && !r.skipped);
    const skipped = results.filter(r => r.success && r.skipped);
    const failed = results.filter(r => !r.success);

    console.log(chalk.green(`✅ 成功: ${successful.length} 个`));
    console.log(chalk.yellow(`⚠️  跳过: ${skipped.length} 个`));
    console.log(chalk.red(`❌ 失败: ${failed.length} 个`));

    if (successful.length > 0) {
      console.log('\n' + chalk.green.bold('成功的包:'));
      successful.forEach(result => {
        console.log(`  ✅ ${result.package}`);
      });
    }

    if (skipped.length > 0) {
      console.log('\n' + chalk.yellow.bold('跳过的包:'));
      skipped.forEach(result => {
        console.log(`  ⚠️  ${result.package}`);
        if (result.reason) {
          console.log(`     ${chalk.gray(result.reason)}`);
        }
      });
    }

    if (failed.length > 0) {
      console.log('\n' + chalk.red.bold('失败的包:'));
      failed.forEach(result => {
        console.log(`  ❌ ${result.package}`);
        if (result.error && this.options.verbose) {
          console.log(`     ${chalk.gray(result.error)}`);
        }
      });
    }
  }

  /**
   * 清理构建产物
   */
  async cleanBuildOutputs() {
    const spinner = ora('正在清理构建产物...').start();

    try {
      const outputs = await glob('**/dist', { cwd: this.cwd });
      const buildDirs = await glob('**/build', { cwd: this.cwd });
      const allOutputs = [...outputs, ...buildDirs];

      if (allOutputs.length === 0) {
        spinner.info('没有找到构建产物');
        return;
      }

      spinner.succeed(`找到 ${allOutputs.length} 个构建目录`);
      console.log(chalk.yellow('构建产物目录:'));
      allOutputs.forEach(dir => {
        console.log(`  ${dir}`);
      });

    } catch (error) {
      spinner.fail('清理构建产物失败');
      throw new CLIError(`清理失败: ${error.message}`);
    }
  }

  /**
   * 分析构建依赖
   */
  async analyzeBuildDependencies() {
    const spinner = ora('正在分析构建依赖...').start();

    try {
      const packages = await this.getWorkspacePackages();
      const analysis = {
        packages: packages.length,
        buildTools: this.detectBuildTools(),
        scripts: new Map(),
        dependencies: new Map()
      };

      // 分析构建脚本
      packages.forEach(pkg => {
        Object.keys(pkg.scripts).forEach(script => {
          if (script.includes('build') || script.includes('compile')) {
            analysis.scripts.set(script, (analysis.scripts.get(script) || 0) + 1);
          }
        });
      });

      spinner.succeed('构建依赖分析完成');
      return analysis;
    } catch (error) {
      spinner.fail('构建依赖分析失败');
      throw new CLIError(`分析失败: ${error.message}`);
    }
  }
}

/**
 * 注册构建命令
 */
export function buildCommand(program) {
  const build = program
    .command('build')
    .alias('b')
    .description('🏗️  构建管理工具');

  // 构建工作空间
  build
    .command('workspace')
    .alias('ws')
    .description('构建工作空间所有包')
    .option('-p, --package <name>', '只构建指定包')
    .option('-s, --script <script>', '使用指定的构建脚本')
    .option('--parallel', '并行构建')
    .option('--stop-on-error', '遇到错误时停止')
    .option('-i, --interactive', '交互式选择包')
    .option('--silent', '静默模式，隐藏构建日志')
    .action(async (options) => {
      try {
        const builder = new BuildManager({
          verbose: program.opts().verbose,
          silent: options.silent
        });
        await builder.buildWorkspace(options);
      } catch (error) {
        console.error(chalk.red('❌ 构建失败:'), error.message);
        process.exit(1);
      }
    });

  // 构建当前包
  build
    .command('current')
    .alias('.')
    .description('构建当前包')
    .option('-s, --script <script>', '使用指定的构建脚本')
    .option('--silent', '静默模式，隐藏构建日志')
    .action(async (options) => {
      try {
        const builder = new BuildManager({
          verbose: program.opts().verbose,
          silent: options.silent
        });
        const result = await builder.buildPackage('.', options);

        if (result.success) {
          console.log(chalk.green.bold('✅ 构建成功'));
        } else {
          console.error(chalk.red.bold('❌ 构建失败:'), result.error);
          process.exit(1);
        }
      } catch (error) {
        console.error(chalk.red('❌ 构建失败:'), error.message);
        process.exit(1);
      }
    });

  // 清理构建产物
  build
    .command('clean')
    .description('清理构建产物')
    .action(async () => {
      try {
        const builder = new BuildManager({ verbose: program.opts().verbose });
        await builder.cleanBuildOutputs();
      } catch (error) {
        console.error(chalk.red('❌ 清理失败:'), error.message);
        process.exit(1);
      }
    });

  // 分析构建配置
  build
    .command('analyze')
    .description('分析构建配置和依赖')
    .action(async () => {
      try {
        const builder = new BuildManager({ verbose: program.opts().verbose });
        const analysis = await builder.analyzeBuildDependencies();

        console.log(chalk.cyan.bold('\n🔍 构建分析报告'));
        console.log('='.repeat(50));

        console.log(chalk.green(`工作空间包数量: ${analysis.packages}`));
        console.log(chalk.green(`检测到的构建工具: ${analysis.buildTools.join(', ') || '无'}`));

        if (analysis.scripts.size > 0) {
          console.log('\n' + chalk.yellow.bold('构建脚本统计:'));
          Array.from(analysis.scripts.entries()).forEach(([script, count]) => {
            console.log(`  ${chalk.cyan(script)}: ${count} 个包`);
          });
        }
      } catch (error) {
        console.error(chalk.red('❌ 分析失败:'), error.message);
        process.exit(1);
      }
    });
}
