import chalk from 'chalk';
import { execWithSpinner, confirm, select } from '../utils/common.js';
import { loadConfig } from '../config/index.js';
import { success, info, CLIError } from '../utils/error-handler.js';
import { GitignoreCleaner } from '../utils/gitignore-cleaner.js';

/**
 * 初始化项目
 */
async function handleInit(options) {
  const { type = 'auto', clean = false, install = true, build = true, dryRun = false } = options;

  console.log(chalk.blue('🚀 开始项目初始化...'));

  // 加载配置
  const { config } = await loadConfig();

  try {
    // 确定初始化类型
    let initType = type;
    if (type === 'auto') {
      initType = await select('请选择初始化类型', [
        { name: '部分初始化 (保留 node_modules)', value: 'partial' },
        { name: '完全初始化 (清空所有缓存和构建产物)', value: 'full' }
      ]);
    }

    // 执行初始化步骤
    if (initType === 'full') {
      console.log(chalk.blue('📋 执行完全初始化...'));

      // 使用 .gitignore 清理器进行深度清理
      console.log(chalk.cyan('🧹 根据 .gitignore 规则清理项目...'));

      const cleaner = new GitignoreCleaner({
        cwd: process.cwd(),
        dryRun: dryRun,
        verbose: options.verbose || false
      });

      const cleanResult = await cleaner.clean();

      if (!dryRun) {
        cleaner.displayResult(cleanResult);

        if (cleanResult.hasErrors) {
          console.log(chalk.yellow('⚠️  清理过程中遇到一些错误，但会继续执行初始化'));
        }
      }

      // 传统清理项目（如果指定了 clean 参数）
      if (clean) {
        await execWithSpinner('执行额外清理操作', 'pnpm', ['-w', 'run', 'clear']);
      }
    } else {
      console.log(chalk.blue('📋 执行部分初始化...'));
    }

    // 安装依赖
    if (install) {
      const packageManager = config.project.packageManager || 'pnpm';
      await execWithSpinner('安装依赖', packageManager, ['install']);
    }

    // 构建工作空间
    if (build && config.project.type === 'monorepo') {
      const packageManager = config.project.packageManager || 'pnpm';
      await execWithSpinner('构建工作空间', packageManager, ['-w', 'run', 'build:workspace']);
    }

    success('项目初始化完成！');

    // 显示后续建议
    console.log(chalk.cyan('\n💡 建议的下一步操作:'));
    console.log(chalk.gray('  • 运行 exam-cli dev 启动开发服务'));
    console.log(chalk.gray('  • 运行 exam-cli deps check 检查依赖'));
    console.log(chalk.gray('  • 运行 exam-cli analyze 分析项目'));

  } catch (error) {
    throw new CLIError(
      `项目初始化失败: ${error.message}`,
      1,
      '请检查网络连接和项目配置'
    );
  }
}

/**
 * 注册初始化命令
 */
export function initCommand(program) {
  program
    .command('init')
    .description('🚀 初始化项目')
    .option('-t, --type <type>', '初始化类型 (auto|partial|full)', 'auto')
    .option('-c, --clean', '执行额外的清理操作')
    .option('--no-install', '跳过依赖安装')
    .option('--no-build', '跳过构建步骤')
    .option('-f, --full', '直接执行完全初始化，不显示选择菜单')
    .option('-p, --partial', '直接执行部分初始化，不显示选择菜单')
    .option('--dry-run', '预览模式，显示将要删除的文件但不实际执行删除')
    .action(async (options) => {
      try {
        // 处理快捷参数
        if (options.full) {
          options.type = 'full';
        } else if (options.partial) {
          options.type = 'partial';
        }

        // 传递全局选项
        options.verbose = program.opts().verbose;
        options.dryRun = options.dryRun || program.opts().dryRun;

        await handleInit(options);
      } catch (error) {
        console.error(chalk.red(`❌ ${error.message}`));
        process.exit(error.code || 1);
      }
    });
}
