import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { existsSync, readFileSync } from 'node:fs';
import { join } from 'node:path';
import { spawn } from 'node:child_process';
import { CLIError } from '../utils/error-handler.js';
import { executeCommand } from '../utils/common.js';

/**
 * 开发服务器管理器类
 */
class DevServerManager {
  constructor(options = {}) {
    this.options = options;
    this.cwd = process.cwd();
    this.packageManager = this.detectPackageManager();
    this.runningProcesses = new Map();
  }

  /**
   * 检测包管理器
   */
  detectPackageManager() {
    if (existsSync(join(this.cwd, 'pnpm-lock.yaml'))) return 'pnpm';
    if (existsSync(join(this.cwd, 'yarn.lock'))) return 'yarn';
    if (existsSync(join(this.cwd, 'package-lock.json'))) return 'npm';
    return 'pnpm';
  }

  /**
   * 获取工作空间包列表
   */
  async getWorkspacePackages() {
    const packages = [];

    // 检查 pnpm workspace
    if (existsSync(join(this.cwd, 'pnpm-workspace.yaml'))) {
      const workspaceYaml = readFileSync(join(this.cwd, 'pnpm-workspace.yaml'), 'utf-8');
      // 简化的解析，实际应该使用 yaml 解析器
      const lines = workspaceYaml.split('\n');

      for (const line of lines) {
        const match = line.match(/^\s*-\s*['"]?(.+?)['"]?\s*$/);
        if (match) {
          const pattern = match[1];
          // 这里简化处理，假设都是直接的目录路径
          const dirs = pattern.includes('*')
            ? await this.globPackages(pattern)
            : [pattern];

          for (const dir of dirs) {
            const packageJsonPath = join(this.cwd, dir, 'package.json');
            if (existsSync(packageJsonPath)) {
              const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
              packages.push({
                name: packageJson.name,
                path: dir,
                version: packageJson.version,
                scripts: packageJson.scripts || {},
                devScript: this.findDevScript(packageJson.scripts || {})
              });
            }
          }
        }
      }
    }

    // 检查 yarn/npm workspace
    else if (existsSync(join(this.cwd, 'package.json'))) {
      const rootPackageJson = JSON.parse(readFileSync(join(this.cwd, 'package.json'), 'utf-8'));
      if (rootPackageJson.workspaces) {
        // 简化处理
        console.log(chalk.yellow('检测到 workspace，但当前简化实现仅支持 pnpm workspace'));
      }
    }

    return packages;
  }

  /**
   * 简化的 glob 包查找
   */
  async globPackages(pattern) {
    // 这里应该使用实际的 glob 库，暂时简化处理
    if (pattern === 'apps/*') {
      return ['apps/admin', 'apps/user', 'apps/h5', 'apps/h5-monitor', 'apps/main'].filter(dir =>
        existsSync(join(this.cwd, dir))
      );
    }
    if (pattern === 'packages/*') {
      return [
        'packages/components', 'packages/exam-cli', 'packages/exam-env',
        'packages/exam-question', 'packages/exam-report'
      ].filter(dir => existsSync(join(this.cwd, dir)));
    }
    return [];
  }

  /**
   * 查找开发脚本
   */
  findDevScript(scripts) {
    const devScripts = ['dev', 'serve', 'start', 'dev:local'];
    return devScripts.find(script => scripts[script]) || null;
  }

  /**
   * 启动单个开发服务器
   */
  async startDevServer(packagePath, options = {}) {
    const packageJsonPath = join(this.cwd, packagePath, 'package.json');
    if (!existsSync(packageJsonPath)) {
      throw new CLIError(`找不到包文件: ${packageJsonPath}`);
    }

    const packageJson = JSON.parse(readFileSync(packageJsonPath, 'utf-8'));
    const scripts = packageJson.scripts || {};

    // 确定开发命令
    let devCommand = options.script || this.findDevScript(scripts);
    if (!devCommand) {
      throw new CLIError(`包 ${packageJson.name} 没有开发脚本`);
    }

    console.log(chalk.cyan(`🚀 启动 ${packageJson.name} 开发服务器...`));
    console.log(chalk.gray(`   命令: ${this.packageManager} run ${devCommand}`));
    console.log(chalk.gray(`   路径: ${packagePath}`));

    if (this.options.dryRun) {
      console.log(chalk.yellow('试运行模式 - 不会实际启动服务器'));
      return;
    }

    // 启动子进程
    const child = spawn(this.packageManager, ['run', devCommand], {
      cwd: join(this.cwd, packagePath),
      stdio: this.options.verbose ? 'inherit' : 'pipe',
      shell: true
    });

    // 保存进程引用
    this.runningProcesses.set(packageJson.name, {
      process: child,
      package: packageJson.name,
      path: packagePath
    });

    // 处理进程事件
    child.on('error', (error) => {
      console.error(chalk.red(`❌ ${packageJson.name} 启动失败:`), error.message);
      this.runningProcesses.delete(packageJson.name);
    });

    child.on('exit', (code) => {
      if (code !== 0) {
        console.error(chalk.red(`❌ ${packageJson.name} 进程退出，代码: ${code}`));
      } else {
        console.log(chalk.yellow(`📴 ${packageJson.name} 服务器已停止`));
      }
      this.runningProcesses.delete(packageJson.name);
    });

    // 如果不是静默模式，显示输出
    if (!this.options.verbose && child.stdout) {
      child.stdout.on('data', (data) => {
        const output = data.toString();
        if (output.includes('Local:') || output.includes('localhost') || output.includes('ready')) {
          console.log(chalk.green(`✅ ${packageJson.name}: ${output.trim()}`));
        }
      });
    }

    console.log(chalk.green(`✅ ${packageJson.name} 开发服务器已启动`));
  }

  /**
   * 启动多个开发服务器
   */
  async startMultipleServers(packages, options = {}) {
    console.log(chalk.cyan.bold(`🚀 启动多个开发服务器 (${packages.length} 个)`));

    for (const pkg of packages) {
      try {
        await this.startDevServer(pkg.path, {
          script: options.script || pkg.devScript
        });


      } catch (error) {
        console.error(chalk.red(`❌ 启动 ${pkg.name} 失败:`), error.message);
        if (options.stopOnError) {
          break;
        }
      }
    }

    // 显示运行状态
    this.showRunningStatus();

    // 设置优雅关闭
    this.setupGracefulShutdown();
  }

  /**
   * 显示运行状态
   */
  showRunningStatus() {
    if (this.runningProcesses.size === 0) {
      console.log(chalk.yellow('没有运行的服务器'));
      return;
    }

    console.log('\n' + chalk.cyan.bold('🏃 运行中的服务器:'));
    console.log('='.repeat(50));

    this.runningProcesses.forEach((info, name) => {
      console.log(chalk.green(`  ✅ ${name} (${info.path})`));
    });

    console.log('\n' + chalk.gray('按 Ctrl+C 停止所有服务器'));
  }

  /**
   * 设置优雅关闭
   */
  setupGracefulShutdown() {
    const shutdown = () => {
      console.log('\n' + chalk.yellow('🛑 正在关闭所有开发服务器...'));

      this.runningProcesses.forEach((info, name) => {
        console.log(chalk.gray(`  停止 ${name}...`));
        info.process.kill('SIGTERM');
      });

      // 强制关闭超时
      setTimeout(() => {
        this.runningProcesses.forEach((info, name) => {
          if (!info.process.killed) {
            console.log(chalk.red(`  强制停止 ${name}`));
            info.process.kill('SIGKILL');
          }
        });
        process.exit(0);
      }, 5000);
    };

    process.on('SIGINT', shutdown);
    process.on('SIGTERM', shutdown);
  }

  /**
   * 选择要启动的包
   */
  async selectPackages(packages) {
    if (packages.length === 0) {
      throw new CLIError('没有找到可开发的包');
    }

    // 过滤有开发脚本的包
    const devPackages = packages.filter(pkg => pkg.devScript);

    if (devPackages.length === 0) {
      throw new CLIError('没有找到包含开发脚本的包');
    }

    const { selected } = await inquirer.prompt([
      {
        type: 'checkbox',
        name: 'selected',
        message: '选择要启动的开发服务器:',
        choices: devPackages.map(pkg => ({
          name: `${pkg.name} (${pkg.devScript}) - ${pkg.path}`,
          value: pkg,
          checked: false
        }))
      }
    ]);

    return selected;
  }

  /**
   * 检查端口占用
   */
  async checkPortUsage(ports) {
    const usedPorts = [];

    for (const port of ports) {
      try {
        await executeCommand(`lsof -ti:${port}`, { stdio: 'pipe' });
        usedPorts.push(port);
      } catch {
        // 端口未被占用
      }
    }

    if (usedPorts.length > 0) {
      console.log(chalk.yellow('⚠️  以下端口已被占用:'));
      usedPorts.forEach(port => {
        console.log(`  ${port}`);
      });
    }

    return usedPorts;
  }
}

/**
 * 注册开发命令
 */
export function devCommand(program) {
  const dev = program
    .command('dev')
    .alias('d')
    .description('🚀 开发服务器管理')
    .option('--dry-run', '试运行模式');

  // 启动当前包的开发服务器
  dev
    .command('start')
    .alias('.')
    .description('启动当前包的开发服务器')
    .option('-s, --script <script>', '指定开发脚本名称')
    .action(async (options) => {
      try {
        const manager = new DevServerManager({
          verbose: program.opts().verbose,
          dryRun: dev.opts().dryRun
        });
        await manager.startDevServer('.', options);
      } catch (error) {
        console.error(chalk.red('❌ 启动失败:'), error.message);
        process.exit(1);
      }
    });

  // 启动工作空间多个开发服务器
  dev
    .command('workspace')
    .alias('ws')
    .description('启动工作空间多个开发服务器')
    .option('-p, --package <name>', '只启动指定包')
    .option('-s, --script <script>', '指定开发脚本名称')
    .option('--stop-on-error', '遇到错误时停止')
    .option('-i, --interactive', '交互式选择包')
    .action(async (options) => {
      try {
        const manager = new DevServerManager({
          verbose: program.opts().verbose,
          dryRun: dev.opts().dryRun
        });

        let packages = await manager.getWorkspacePackages();

        // 过滤指定包
        if (options.package) {
          packages = packages.filter(pkg =>
            pkg.name.includes(options.package) || pkg.path.includes(options.package)
          );
        }

        // 交互式选择
        if (options.interactive) {
          packages = await manager.selectPackages(packages);
        }

        if (packages.length === 0) {
          console.log(chalk.yellow('没有要启动的包'));
          return;
        }

        await manager.startMultipleServers(packages, options);
      } catch (error) {
        console.error(chalk.red('❌ 启动失败:'), error.message);
        process.exit(1);
      }
    });

  // 列出可用的开发服务器
  dev
    .command('list')
    .alias('ls')
    .description('列出可用的开发服务器')
    .action(async () => {
      try {
        const manager = new DevServerManager({ verbose: program.opts().verbose });
        const packages = await manager.getWorkspacePackages();

        console.log(chalk.cyan.bold('\n📋 可用的开发服务器'));
        console.log('='.repeat(50));

        if (packages.length === 0) {
          console.log(chalk.yellow('没有找到工作空间包'));
          return;
        }

        packages.forEach(pkg => {
          const status = pkg.devScript ? chalk.green('✅') : chalk.red('❌');
          const script = pkg.devScript || '无开发脚本';
          console.log(`${status} ${chalk.cyan(pkg.name)}`);
          console.log(`   路径: ${chalk.gray(pkg.path)}`);
          console.log(`   脚本: ${chalk.yellow(script)}`);
          console.log('');
        });
      } catch (error) {
        console.error(chalk.red('❌ 列出失败:'), error.message);
        process.exit(1);
      }
    });

  // 检查端口占用
  dev
    .command('ports [ports...]')
    .description('检查端口占用情况')
    .action(async (ports) => {
      try {
        const defaultPorts = ['3000', '3001', '8080', '8081', '5173', '4173'];
        const checkPorts = ports.length > 0 ? ports : defaultPorts;

        const manager = new DevServerManager({ verbose: program.opts().verbose });
        await manager.checkPortUsage(checkPorts);
      } catch (error) {
        console.error(chalk.red('❌ 检查失败:'), error.message);
        process.exit(1);
      }
    });
}
