import chalk from 'chalk';
import ora from 'ora';
import inquirer from 'inquirer';
import { resolve } from 'node:path';
import { CLIError } from '../utils/error-handler.js';
import { execCommand } from '../utils/common.js';

/**
 * 应用管理器类
 */
class AppManager {
    constructor(options = {}) {
        this.options = options;
        this.cwd = process.cwd();

        // 应用配置
        this.apps = {
            admin: {
                name: 'Admin 管理后台',
                filter: 'web-admin',
                description: '管理员后台应用',
            },
            user: {
                name: 'User 用户前台',
                filter: 'web-user',
                description: '用户前台应用',
            },
        };

        // 环境配置
        this.environments = {
            dev: { name: '开发环境', description: '本地开发环境' },
            qa: { name: 'QA环境', description: '测试环境' },
            rd: { name: 'RD环境', description: '研发环境' },
            pre: { name: 'Pre环境', description: '预发布环境' },
            prod: { name: '生产环境', description: '生产环境' },
        };
    }

    /**
     * 构建应用
     */
    async buildApp(appName, env, options = {}) {
        const app = this.apps[appName];
        if (!app) {
            throw new CLIError(`未知的应用: ${appName}`);
        }

        const environment = this.environments[env];
        if (!environment) {
            throw new CLIError(`未知的环境: ${env}`);
        }

        // 显示构建开始信息
        console.log(chalk.cyan.bold(`🏗️  开始构建 ${app.name} (${environment.name})`));

        try {
            // 传统 turbo 构建模式
            console.log(chalk.gray(`   过滤器: ${app.filter}`));
            console.log(chalk.gray(`   命令: pnpm exec turbo run build:${env} --filter=${app.filter}`));
            console.log(''); // 空行分隔

            // 默认显示构建日志，除非明确设置为静默模式
            const showLogs = !this.options.silent;

            await execCommand('pnpm', ['exec', 'turbo', 'run', `build:${env}`, `--filter=${app.filter}`], {
                cwd: this.cwd,
                stdio: showLogs ? 'inherit' : 'pipe',
                dryRun: this.options.dryRun,
            });

            console.log(''); // 空行分隔
            console.log(chalk.green.bold(`✅ ${app.name} (${environment.name}) 构建完成`));
            return { success: true, app: appName, env };
        } catch (error) {
            console.log(''); // 空行分隔
            console.log(chalk.red.bold(`❌ ${app.name} (${environment.name}) 构建失败`));
            throw new CLIError(`构建失败: ${error.message}`);
        }
    }

    /**
     * 启动应用开发服务器
     */
    async startApp(appName, env = 'dev', options = {}) {
        const app = this.apps[appName];
        if (!app) {
            throw new CLIError(`未知的应用: ${appName}`);
        }

        const environment = this.environments[env];
        if (!environment) {
            throw new CLIError(`未知的环境: ${env}`);
        }

        console.log(chalk.cyan(`🚀 启动 ${app.name} 开发服务器 (${environment.name})...`));

        try {
            // 检查是否使用无配置模式
            if (options.noConfig) {
                return await this.startAppWithoutConfig(appName, env, options);
            }

            // 传统 turbo 开发模式
            const devCommand = env === 'dev' ? 'dev' : `dev:${env}`;

            if (this.options.dryRun) {
                console.log(chalk.yellow(`试运行: pnpm exec turbo run ${devCommand} --filter=${app.filter}`));
                return;
            }

            await execCommand('pnpm', ['exec', 'turbo', 'run', devCommand, `--filter=${app.filter}`], {
                cwd: this.cwd,
                stdio: 'inherit',
                dryRun: this.options.dryRun,
            });
        } catch (error) {
            throw new CLIError(`启动开发服务器失败: ${error.message}`);
        }
    }

    /**
     * 交互式选择应用和环境
     */
    async selectAppAndEnv(action = 'build') {
        const { appName } = await inquirer.prompt([
            {
                type: 'list',
                name: 'appName',
                message: `选择要${action}的应用:`,
                choices: Object.entries(this.apps).map(([key, app]) => ({
                    name: `${app.name} - ${app.description}`,
                    value: key,
                })),
            },
        ]);

        const envChoices = action === 'dev' ? ['dev', 'qa', 'rd'] : ['dev', 'qa', 'rd', 'pre', 'prod'];

        const { env } = await inquirer.prompt([
            {
                type: 'list',
                name: 'env',
                message: '选择环境:',
                choices: envChoices.map((key) => ({
                    name: `${this.environments[key].name} - ${this.environments[key].description}`,
                    value: key,
                })),
            },
        ]);

        return { appName, env };
    }

    /**
     * 列出所有应用
     */
    listApps() {
        console.log(chalk.cyan.bold('\n📱 可用的应用'));
        console.log('='.repeat(50));

        Object.entries(this.apps).forEach(([key, app]) => {
            console.log(`${chalk.green('✓')} ${chalk.cyan(key.padEnd(10))} ${app.name}`);
            console.log(`   ${chalk.gray(app.description)}`);
            console.log('');
        });

        console.log(chalk.cyan.bold('🌍 可用的环境'));
        console.log('='.repeat(50));

        Object.entries(this.environments).forEach(([key, env]) => {
            console.log(`${chalk.green('✓')} ${chalk.yellow(key.padEnd(8))} ${env.name}`);
            console.log(`   ${chalk.gray(env.description)}`);
            console.log('');
        });
    }
}

/**
 * 注册应用管理命令
 */
export function appCommand(program) {
    const app = program.command('app').alias('a').description('📱 应用管理工具 (构建和开发)');

    // 构建应用
    app.command('build [app] [env]')
        .alias('b')
        .description('构建应用')
        .option('--all', '构建所有应用')
        .option('-i, --interactive', '交互式选择应用和环境')
        .option('-s, --silent', '静默模式，隐藏构建日志')
        .option('--no-config', '使用无配置模式（基于 exam-env 的统一配置）')
        .option('--force', '强制执行，跳过确认提示')
        .action(async (appName, env, options) => {
            try {
                const manager = new AppManager({
                    verbose: program.opts().verbose,
                    dryRun: program.opts().dryRun,
                    silent: options.silent,
                });

                if (options.interactive || (!appName && !env)) {
                    const selection = await manager.selectAppAndEnv('build');
                    appName = selection.appName;
                    env = selection.env;
                }

                if (!appName || !env) {
                    throw new CLIError('请指定应用名称和环境，或使用 --interactive 参数');
                }

                await manager.buildApp(appName, env, {
                    noConfig: options.noConfig,
                    force: options.force,
                });
            } catch (error) {
                console.error(chalk.red('❌ 构建失败:'), error.message);
                process.exit(1);
            }
        });

    // 启动开发服务器
    app.command('dev [app] [env]')
        .alias('d')
        .description('启动应用开发服务器')
        .option('-i, --interactive', '交互式选择应用和环境')
        .option('--no-config', '使用无配置模式（基于 exam-env 的统一配置）')
        .option('--force', '强制执行，跳过确认提示')
        .action(async (appName, env, options) => {
            try {
                const manager = new AppManager({
                    verbose: program.opts().verbose,
                    dryRun: program.opts().dryRun,
                });

                if (options.interactive || !appName) {
                    const selection = await manager.selectAppAndEnv('dev');
                    appName = selection.appName;
                    env = selection.env;
                }

                if (!appName) {
                    throw new CLIError('请指定应用名称，或使用 --interactive 参数');
                }

                await manager.startApp(appName, env || 'dev', {
                    noConfig: options.noConfig,
                    force: options.force,
                });
            } catch (error) {
                console.error(chalk.red('❌ 启动失败:'), error.message);
                process.exit(1);
            }
        });

    // 列出应用
    app.command('list')
        .alias('ls')
        .description('列出所有可用的应用和环境')
        .action(() => {
            const manager = new AppManager();
            manager.listApps();
        });
}
