import { cosmiconfig } from 'cosmiconfig';
import { join } from 'node:path';
import { ConfigError } from '../utils/error-handler.js';

/**
 * 默认配置
 */
const defaultConfig = {
  // 项目设置
  project: {
    name: 'exam-project',
    type: 'monorepo',
    packageManager: 'pnpm'
  },

  // Git 设置
  git: {
    defaultBranch: 'master',
    protectedBranches: ['master', 'main', 'develop'],
    autoCleanup: false
  },

  // 依赖管理设置
  dependencies: {
    checkConsistency: true,
    autoUpdate: false,
    excludePatterns: [
      '**/node_modules/**',
      '**/dist/**',
      '**/build/**',
      '**/coverage/**'
    ]
  },

  // 构建设置
  build: {
    outputDir: 'dist',
    cleanBeforeBuild: true,
    parallel: true,
    logLevel: 'info',
    timeout: {
      build: 0,        // 无超时限制
      install: 0,      // 无超时限制
      test: 0,         // 无超时限制
      typeCheck: 0,    // 无超时限制
      lint: 0          // 无超时限制
    }
  },

  // 分析设置
  analysis: {
    enableInsights: true,
    reportPath: '.build-logs',
    timeRange: 30, // 天数
    excludePatterns: [
      'node_modules',
      '.git',
      'dist',
      'build',
      '.turbo',
      '.cache'
    ]
  },

  // 清理设置
  clean: {
    targets: [
      'node_modules',
      '.turbo',
      'dist',
      'build',
      'coverage',
      '.cache',
      '.pnpm-store'
    ],
    keepFiles: [
      '.gitkeep',
      'README.md'
    ],
    clearCaches: {
      npm: true,
      yarn: true,
      pnpm: true
    }
  },

  // Vue 优化设置
  vue: {
    autoImport: {
      configPath: '.eslintrc-auto-import.json',
      preserveKeywords: [
        'render',
        'createVNode',
        'Teleport',
        'App',
        'UnwrapRef',
        'WatchStopHandle',
        'ComponentInternalInstance',
        'CSSProperties',
        'AppContext',
        'createWebHistory',
        'createRouter',
        'ObjectDirective',
        'StyleValue',
        'Plugin'
      ],
      maxModifiedFiles: 100
    }
  },

  // 日志设置
  logging: {
    level: 'info',
    timestamp: true,
    colors: true
  }
};

/**
 * 加载配置
 */
export async function loadConfig(searchFrom = process.cwd(), configPath = null) {
  const explorer = cosmiconfig('exam-cli', {
    searchPlaces: [
      'package.json',
      '.exam-cli.config.js',
      '.exam-cli.config.mjs',
      '.exam-cli.config.json',
      'exam-cli.config.js',
      'exam-cli.config.mjs',
      'exam-cli.config.json'
    ]
  });

  try {
    let result;

    if (configPath) {
      // 如果指定了配置文件路径
      result = await explorer.load(configPath);
      if (!result) {
        throw new ConfigError(`无法加载指定的配置文件: ${configPath}`, configPath);
      }
    } else {
      // 自动搜索配置文件
      result = await explorer.search(searchFrom);
    }

    // 合并默认配置和用户配置
    const config = result ? mergeConfig(defaultConfig, result.config) : defaultConfig;

    // 验证配置
    validateConfig(config);

    return {
      config,
      configPath: result?.filepath || null
    };

  } catch (error) {
    if (error instanceof ConfigError) {
      throw error;
    }

    throw new ConfigError(
      `配置加载失败: ${error.message}`,
      configPath || searchFrom
    );
  }
}

/**
 * 深度合并配置对象
 */
function mergeConfig(defaultConfig, userConfig) {
  const merged = { ...defaultConfig };

  for (const [key, value] of Object.entries(userConfig)) {
    if (value && typeof value === 'object' && !Array.isArray(value)) {
      merged[key] = mergeConfig(merged[key] || {}, value);
    } else {
      merged[key] = value;
    }
  }

  return merged;
}

/**
 * 验证配置
 */
function validateConfig(config) {
  // 验证项目类型
  if (config.project?.type && !['monorepo', 'single'].includes(config.project.type)) {
    throw new ConfigError('project.type 必须是 "monorepo" 或 "single"');
  }

  // 验证包管理器
  if (config.project?.packageManager && !['npm', 'yarn', 'pnpm'].includes(config.project.packageManager)) {
    throw new ConfigError('project.packageManager 必须是 "npm", "yarn" 或 "pnpm"');
  }

  // 验证日志级别
  if (config.logging?.level && !['error', 'warn', 'info', 'debug'].includes(config.logging.level)) {
    throw new ConfigError('logging.level 必须是 "error", "warn", "info" 或 "debug"');
  }

  // 验证时间范围
  if (config.analysis?.timeRange && (typeof config.analysis.timeRange !== 'number' || config.analysis.timeRange <= 0)) {
    throw new ConfigError('analysis.timeRange 必须是正整数');
  }
}

/**
 * 创建默认配置文件
 */
export async function createDefaultConfig(outputPath = 'exam-cli.config.js') {
  const fs = await import('node:fs/promises');
  const configContent = `// Exam CLI 配置文件
export default ${JSON.stringify(defaultConfig, null, 2)};
`;

  try {
    await fs.writeFile(outputPath, configContent, 'utf8');
    return outputPath;
  } catch (error) {
    throw new ConfigError(`创建配置文件失败: ${error.message}`, outputPath);
  }
}

/**
 * 获取配置值
 */
export function getConfigValue(config, path, defaultValue = null) {
  return path.split('.').reduce((obj, key) => obj?.[key], config) ?? defaultValue;
}

/**
 * 设置配置值
 */
export function setConfigValue(config, path, value) {
  const keys = path.split('.');
  const lastKey = keys.pop();
  const target = keys.reduce((obj, key) => {
    if (!obj[key]) obj[key] = {};
    return obj[key];
  }, config);

  target[lastKey] = value;
  return config;
}

/**
 * 获取默认配置
 */
export function getDefaultConfig() {
  return JSON.parse(JSON.stringify(defaultConfig));
}

/**
 * 配置模板
 */
export const configTemplates = {
  monorepo: {
    ...defaultConfig,
    project: {
      ...defaultConfig.project,
      type: 'monorepo',
      packageManager: 'pnpm'
    }
  },

  single: {
    ...defaultConfig,
    project: {
      ...defaultConfig.project,
      type: 'single',
      packageManager: 'npm'
    },
    build: {
      ...defaultConfig.build,
      parallel: false
    }
  }
};

export { defaultConfig };
