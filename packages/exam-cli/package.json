{"name": "@bzl/exam-cli", "version": "1.1.4", "description": "A powerful CLI tool for exam monorepo management", "keywords": ["cli", "monorepo", "development", "git", "dependency", "analysis"], "author": "Exam Team", "license": "MIT", "type": "module", "exports": {".": {"import": "./src/index.js"}}, "bin": {"exam-cli": "./bin/exam-cli.js", "exam": "./bin/exam-cli.js"}, "files": ["bin", "src", "README.md"], "engines": {"node": ">=16.0.0"}, "dependencies": {"commander": "^11.1.0", "chalk": "^5.3.0", "ora": "^7.0.1", "inquirer": "^9.2.12", "execa": "^8.0.1", "glob": "^10.3.10", "semver": "^7.5.4", "cosmiconfig": "^8.3.6", "picocolors": "^1.0.0", "figlet": "^1.7.0", "boxen": "^7.1.1", "update-notifier": "^7.0.0", "vite": "^6.3.5"}, "devDependencies": {"eslint": "^8.54.0", "prettier": "^3.1.0"}, "scripts": {"dev": "node ./bin/exam-cli.js", "build": "echo 'No build step needed for pure ESM package'", "lint": "eslint src bin", "format": "prettier --write src bin", "test": "echo 'Tests to be added'"}, "repository": {"type": "git", "url": "git+https://github.com/exam/exam-monorepo.git", "directory": "packages/exam-cli"}, "homepage": "https://github.com/exam/exam-monorepo#readme", "bugs": {"url": "https://github.com/exam/exam-monorepo/issues"}}