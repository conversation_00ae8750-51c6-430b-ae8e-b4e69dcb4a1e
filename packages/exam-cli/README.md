# @exam/cli

🚀 **强大的 Exam 项目管理工具**

一个现代化的 CLI 工具，专为 Exam 项目的 monorepo 管理而设计，提供项目初始化、依赖管理、构建、分析等全方位功能。

## ✨ 特性

- 🏗️ **项目管理**: 项目初始化、依赖管理、构建管理
- 🌿 **Git 管理**: 分支管理、清理、信息查看
- 📊 **项目分析**: 代码统计、Git 活跃度、技术债务分析
- 🗑️ **项目清理**: node_modules、构建产物、缓存清理
- 🚀 **开发工具**: 多项目开发服务器管理
- 🔧 **Vue 优化**: 导入优化、未使用代码清理
- 💾 **配置驱动**: 支持配置文件、环境变量
- 🎨 **用户友好**: 漂亮的 UI、进度指示、详细日志

## 📦 安装

### 全局安装

```bash
npm install -g @exam/cli
# 或
pnpm add -g @exam/cli
```

### 使用 npx (推荐)

```bash
npx @exam/cli --help
```

## 🚀 快速开始

### 查看帮助

```bash
exam-cli --help
# 或
exam --help
```

### 项目初始化

```bash
# 交互式选择初始化类型
exam-cli init

# 直接执行完全初始化（推荐）- 根据 .gitignore 规则深度清理
exam-cli init --full

# 直接执行部分初始化
exam-cli init --partial

# 预览模式 - 查看将要删除的文件但不实际执行
exam-cli init --full --dry-run

# 跳过依赖安装
exam-cli init --no-install

# 跳过构建步骤
exam-cli init --no-build

# 显示详细清理过程
exam-cli init --full --verbose
```

#### 完全初始化特性

完全初始化模式会：
- 🧹 **智能清理**: 根据 .gitignore 文件规则自动清理所有匹配的文件和目录
- 🔍 **多层级支持**: 支持根目录和子目录中的多个 .gitignore 文件
- 🛡️ **安全保护**: 自动保护重要文件（源代码、配置文件等）不被误删
- 📊 **详细报告**: 显示清理进度和结果统计
- 🔬 **预览模式**: 使用 `--dry-run` 预览将要删除的内容

### 依赖管理

```bash
# 安装依赖
exam-cli deps install

# 分析依赖
exam-cli deps analyze

# 检查过期依赖
exam-cli deps outdated

# 更新依赖
exam-cli deps update
```

### Git 分支管理

```bash
# 查看分支信息
exam-cli branch info

# 清理合并的分支
exam-cli branch clean

# 清理所有本地分支
exam-cli branch clean --all
```

### 项目分析

```bash
# 完整项目分析
exam-cli analyze all

# 代码统计
exam-cli analyze code

# Git 活跃度
exam-cli analyze git

# 技术债务
exam-cli analyze debt
```

### 项目清理

```bash
# 清理 node_modules
exam-cli clean deps

# 清理构建产物
exam-cli clean build

# 清理缓存
exam-cli clean cache

# 全面清理
exam-cli clean all
```

### 构建管理

```bash
# 构建工作空间
exam-cli build workspace

# 构建当前包
exam-cli build current

# 并行构建
exam-cli build workspace --parallel

# 交互式选择包
exam-cli build workspace --interactive
```

### 开发服务器

```bash
# 启动当前包的开发服务器
exam-cli dev start

# 启动工作空间多个服务器
exam-cli dev workspace

# 交互式选择服务器
exam-cli dev workspace --interactive

# 列出可用服务器
exam-cli dev list
```

### Vue 优化

```bash
# 优化 Vue 文件导入
exam-cli vue optimize

# 分析导入统计
exam-cli vue analyze

# 清理未使用的导入
exam-cli vue clean
```

### 应用管理 (新增)

```bash
# 列出所有可用应用和环境
exam-cli app list

# 构建应用 (交互式选择)
exam-cli app build --interactive

# 构建指定应用和环境
exam-cli app build admin prod

# 启动开发服务器 (交互式选择)
exam-cli app dev --interactive

# 启动指定应用开发服务器
exam-cli app dev user qa
```

### 发布管理 (新增)

```bash
# 完整发布流程
exam-cli publish release

# 仅创建 changeset
exam-cli publish changeset

# 准备发布环境
exam-cli publish prepare

# 检查发布状态
exam-cli publish status
```

### 代码质量管理 (新增)

```bash
# 运行 ESLint 检查
exam-cli quality lint

# 自动修复 ESLint 问题
exam-cli quality lint --fix

# 运行 TypeScript 类型检查
exam-cli quality type-check

# 运行测试
exam-cli quality test

# 运行测试 (CI 模式包含覆盖率)
exam-cli quality test --ci

# 运行所有质量检查
exam-cli quality check-all

# 包含测试的全面检查
exam-cli quality check-all --include-tests --auto-fix
```

## 📝 命令详情

### 全局选项

- `--verbose`: 显示详细日志
- `--dry-run`: 试运行模式，不执行实际操作
- `--config <path>`: 指定配置文件路径
- `--no-banner`: 不显示欢迎横幅

### 命令别名

大部分命令都有简短的别名：

- `exam-cli init` → `exam-cli i`
- `exam-cli deps` → `exam-cli dep`
- `exam-cli branch` → `exam-cli br`
- `exam-cli analyze` → `exam-cli a`
- `exam-cli clean` → `exam-cli c`
- `exam-cli build` → `exam-cli b`
- `exam-cli dev` → `exam-cli d`
- `exam-cli vue` → `exam-cli v`
- `exam-cli app` → `exam-cli a` (应用管理)
- `exam-cli publish` → `exam-cli pub` (发布管理)
- `exam-cli quality` → `exam-cli q` (代码质量)

## ⚙️ 配置

支持多种配置方式：

### 配置文件

在项目根目录创建以下任意配置文件：

- `.examrc.json`
- `.examrc.js`
- `exam.config.js`
- `package.json` 中的 `exam` 字段

```javascript
// exam.config.js
export default {
  // 默认包管理器
  packageManager: 'pnpm',

  // 忽略的文件模式
  ignorePatterns: [
    '**/node_modules/**',
    '**/dist/**',
    '**/.git/**'
  ],

  // 构建配置
  build: {
    parallel: true,
    stopOnError: false
  },

  // 分析配置
  analyze: {
    includeTests: false,
    maxFileSize: '1MB'
  }
};
```

### 环境变量

- `EXAM_CLI_VERBOSE`: 启用详细日志
- `EXAM_CLI_DRY_RUN`: 启用试运行模式
- `EXAM_CLI_CONFIG`: 指定配置文件路径

## 🛠️ 开发

### 本地开发

```bash
# 克隆仓库
git clone <repo-url>
cd exam-monorepo/packages/exam-cli

# 安装依赖
pnpm install

# 运行开发版本
pnpm dev

# 链接到全局
pnpm link --global
```

### 项目结构

```
exam-cli/
├── bin/                 # 可执行文件
│   └── exam-cli.js
├── src/                 # 源码
│   ├── commands/        # 命令模块
│   ├── config/          # 配置管理
│   ├── utils/           # 工具函数
│   └── index.js         # 主入口
├── package.json
└── README.md
```

## 🤝 贡献

欢迎贡献代码！请遵循以下流程：

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 提交更改 (`git commit -m 'Add amazing feature'`)
4. 推送分支 (`git push origin feature/amazing-feature`)
5. 创建 Pull Request

## 📋 更新日志

### v1.1.0 (最新)

- 🆕 新增应用管理功能 (`exam-cli app`)
  - 支持多应用构建管理（Admin/User）
  - 多环境支持（dev/qa/rd/pre/prod）
  - 交互式应用选择和启动
- 📦 新增发布管理功能 (`exam-cli publish`)
  - 完整的 Changeset 发布流程
  - 自动化版本管理和发布状态检查
- 🔍 新增代码质量管理功能 (`exam-cli quality`)
  - ESLint 代码风格检查
  - TypeScript 类型检查
  - 单元测试和覆盖率报告
- 🎯 整合了根目录 package.json 中的常用脚本命令
- 📱 更好的用户体验和交互式操作

### v1.0.0

- ✨ 初始版本
- 🏗️ 完整的项目管理功能
- 🌿 Git 分支管理
- 📊 项目分析工具
- 🗑️ 项目清理功能
- 🚀 开发服务器管理
- 🔧 Vue 优化工具

## 📄 许可证

MIT License - 详见 [LICENSE](../../LICENSE) 文件

## 🔗 相关链接

- [问题反馈](https://github.com/exam/exam-monorepo/issues)
- [讨论区](https://github.com/exam/exam-monorepo/discussions)
- [项目主页](https://github.com/exam/exam-monorepo)

---

**Made with ❤️ by Exam Team**

## 功能

### Vue 项目优化工具

#### 1. 优化 Vue 文件导入
```bash
exam-cli vue optimize [options]
# 或者使用别名
exam-cli v opt [options]
```

选项：
- `-p, --pattern <pattern>`: 指定文件匹配模式，默认 `**/*.vue`
- `-f, --force`: 强制优化，不询问确认
- `--dry-run`: 试运行模式，不修改文件

#### 2. 分析 Vue 文件导入统计
```bash
exam-cli vue analyze
# 或者使用别名
exam-cli v a
```

#### 3. 清理未使用的导入
```bash
exam-cli vue clean [options]
```

选项：
- `-p, --pattern <pattern>`: 指定文件匹配模式，默认 `**/*.vue`

#### 4. 清理基于自动导入配置的 Vue 导入语句 ⭐ 新功能
```bash
exam-cli vue clean-auto-imports [options]
# 或者使用别名
exam-cli v cai [options]
```

选项：
- `-d, --dir <directory>`: 指定要处理的目录（相对于当前工作目录）

这个功能会：
- 智能检测源码目录（优先级：src > app > source > 当前目录）
- 读取自动导入配置文件（支持多层级查找）
- 自动识别已在自动导入配置中定义的 Vue 导入项
- 移除这些不必要的导入语句，减少代码冗余
- 保留特定的重要导入关键字（如 `render`, `createVNode`, `Teleport` 等）
- 自动移除 `type` 关键字
- 提供详细的处理统计报告

#### 5. 批量清理 Monorepo 中的 Vue 自动导入 🚀 Monorepo 专用
```bash
exam-cli vue clean-auto-imports-monorepo [options]
# 或者使用别名
exam-cli v caim [options]
```

选项：
- `-p, --patterns <patterns...>`: 指定子项目匹配模式，默认 `apps/*` `packages/*`

这个功能专为 monorepo 项目设计，会：
- 自动扫描并发现包含 Vue 文件的子项目
- 为每个子项目独立处理 Vue 导入清理
- 支持每个子项目有独立的自动导入配置
- 提供整体的统计报告和各项目的详细结果
- 适用于大型 monorepo 项目的批量维护

全局选项：
- `-f, --force`: 强制执行，不询问确认
- `--dry-run`: 试运行模式，不修改文件
- `--verbose`: 显示详细信息

#### 使用示例

##### 单项目清理
1. **基本用法**：
```bash
# 清理自动导入的 Vue 导入语句（自动检测源码目录）
exam-cli vue clean-auto-imports
```

2. **指定目录**：
```bash
# 只处理特定目录
exam-cli vue clean-auto-imports --dir src/components
```

3. **试运行模式**：
```bash
# 先查看会有什么改动，不实际修改文件
exam-cli vue clean-auto-imports --dry-run
```

4. **强制执行，跳过确认**：
```bash
exam-cli vue clean-auto-imports --force
```

##### Monorepo 批量清理
1. **基本用法**（处理 apps/* 和 packages/* 下的所有子项目）：
```bash
exam-cli vue clean-auto-imports-monorepo
```

2. **自定义匹配模式**：
```bash
# 只处理 apps 目录下的项目
exam-cli vue clean-auto-imports-monorepo --patterns "apps/*"

# 处理多个自定义目录
exam-cli vue clean-auto-imports-monorepo --patterns "frontend/*" "backend/*" "shared/*"
```

3. **试运行模式**（推荐在批量操作前使用）：
```bash
exam-cli vue clean-auto-imports-monorepo --dry-run --verbose
```

4. **强制批量清理所有子项目**：
```bash
exam-cli vue clean-auto-imports-monorepo --force
```

#### 自动导入配置文件格式

工具会按以下优先级查找配置文件：
1. 当前处理目录下的 `.eslintrc-auto-import.json`
2. 上一级目录的 `.eslintrc-auto-import.json`
3. 上两级目录的 `.eslintrc-auto-import.json`
4. 项目根目录的 `.eslintrc-auto-import.json`

**配置文件格式：**
```json
{
  "globals": {
    "ref": "readonly",
    "reactive": "readonly",
    "computed": "readonly",
    "watch": "readonly",
    "watchEffect": "readonly",
    "onMounted": "readonly",
    "onUnmounted": "readonly",
    "nextTick": "readonly",
    "defineComponent": "readonly",
    "defineProps": "readonly",
    "defineEmits": "readonly",
    "useRouter": "readonly",
    "useRoute": "readonly"
  }
}
```

**Monorepo 配置建议：**
- 在项目根目录放置通用配置
- 在各子项目目录放置特定配置（会覆盖根目录配置）
- 支持不同子项目使用不同的自动导入配置

#### 处理结果示例

##### 单项目处理结果
```
使用配置文件: .eslintrc-auto-import.json
自动检测到源码目录: src

🧹 Vue 自动导入清理结果
==================================================
处理的文件总数: 45
修改的文件数: 12
删除的导入语句总数: 28
保留的导入语句总数: 15
移除的type关键字总数: 8

删除导入语句最多的文件 (Top 10):
1. src/components/UserProfile.vue: 5 个导入
2. src/views/Dashboard.vue: 4 个导入
3. src/components/DataTable.vue: 3 个导入

不在自动导入配置中的导入:

文件: src/components/AdvancedChart.vue
  createVNode, Teleport

文件: src/utils/renderUtils.vue
  render, h
```

##### Monorepo 批量处理结果
```
找到的子项目:
  1. apps/admin
  2. apps/user
  3. packages/components

📂 处理项目: apps/admin
使用配置文件: apps/admin/.eslintrc-auto-import.json
apps/admin 处理完成

📂 处理项目: apps/user
使用配置文件: .eslintrc-auto-import.json
apps/user 处理完成

📂 处理项目: packages/components
使用配置文件: .eslintrc-auto-import.json
packages/components 处理完成

🏗️ Monorepo Vue 自动导入清理结果
============================================================
总处理项目数: 3
总处理文件数: 128
总修改文件数: 35
总删除导入数: 89
总保留导入数: 43
总移除type关键字数: 22

各项目详细结果:

📦 apps/admin:
  修改文件: 15
  删除导入: 42
  保留导入: 18
  移除type: 12

📦 apps/user:
  修改文件: 12
  删除导入: 28
  保留导入: 15
  移除type: 6

📦 packages/components:
  修改文件: 8
  删除导入: 19
  保留导入: 10
  移除type: 4
```

## 开发

```bash
# 安装依赖
npm install

# 本地开发
npm run dev

# 构建
npm run build
```
