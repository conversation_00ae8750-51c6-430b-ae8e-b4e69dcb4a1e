import path from 'node:path';

// 在运行时计算 monorepo 根目录
// 这个函数会在应用中被调用，所以可以从应用的 cwd 推断出 monorepo 根目录
function getMonorepoRoot(appCwd: string): string {
    // 从应用目录（如 apps/user 或 apps/admin）向上两级就是 monorepo 根目录
    return path.resolve(appCwd, '../../');
}

// 创建别名配置的工厂函数，接受应用的工作目录
export function createAliasConfig(appCwd: string = process.cwd()) {
    const monorepoRoot = getMonorepoRoot(appCwd);

    return [
        // exam-report 模块内部别名（使用专门的别名，避免与应用的 @ 冲突）
        {
            find: '@exam-report',
            replacement: path.resolve(monorepoRoot, 'packages/exam-report/src'),
        },
        {
            find: '@common',
            replacement: path.resolve(monorepoRoot, 'packages/exam-report/src/components-atom'),
        },
        // 解决 exam-report 模块分发后的路径问题
        {
            find: /^@bzl\/zhice-exam-report\/dist\/(.*)/,
            replacement: path.resolve(monorepoRoot, 'packages/exam-report/$1'),
        },
        // monorepo 内其他模块的别名
        { find: '@packages/exam-question', replacement: path.resolve(monorepoRoot, 'packages/exam-question/src') },
        { find: '@packages/exam-report', replacement: path.resolve(monorepoRoot, 'packages/exam-report/src') },
        { find: '@packages/exam-product', replacement: path.resolve(monorepoRoot, 'packages/exam-product/src') },
        { find: '@packages/biz-compositions', replacement: path.resolve(monorepoRoot, 'packages/biz-compositions/src') },
        { find: '@packages/components', replacement: path.resolve(monorepoRoot, 'packages/components/src') },
        { find: '@packages/compositions', replacement: path.resolve(monorepoRoot, 'packages/compositions/src') },
        { find: '@packages/lext-core', replacement: path.resolve(monorepoRoot, 'packages/lext-core/src') },
        { find: '@packages/vite-plugins', replacement: path.resolve(monorepoRoot, 'packages/vite-plugins/src') },
    ];
}

// 为了向后兼容，保留旧的导出，但使用新的工厂函数
export const aliasConfig = createAliasConfig();
