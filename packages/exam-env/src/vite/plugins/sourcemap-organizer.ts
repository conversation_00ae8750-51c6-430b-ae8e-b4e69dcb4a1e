import type { Plugin } from 'vite';
import { promises as fs } from 'node:fs';
import path from 'node:path';
import { glob } from 'glob';

/**
 * Source Map 整理插件配置选项
 */
export interface SourcemapOrganizerOptions {
    /**
     * 目标文件夹名称
     * @default 'sourcemaps'
     */
    targetDir?: string;

    /**
     * 是否启用插件
     * @default true (仅在生产环境)
     */
    enabled?: boolean;

    /**
     * 文件匹配模式
     * @default '**\/*.map'
     */
    pattern?: string;

    /**
     * 是否保持原有的目录结构
     * @default true
     */
    preserveStructure?: boolean;

    /**
     * 是否在控制台输出详细日志
     * @default false
     */
    verbose?: boolean;
}

/**
 * 默认配置
 */
const DEFAULT_OPTIONS: Required<SourcemapOrganizerOptions> = {
    targetDir: 'sourcemaps',
    enabled: true,
    pattern: '**/*.map',
    preserveStructure: true,
    verbose: false,
};

/**
 * 日志工具类
 */
class Logger {
    constructor(private verbose: boolean) {}

    info(message: string) {
        if (this.verbose) {
            console.log(`[sourcemap-organizer] ${message}`);
        }
    }

    error(message: string) {
        console.error(`[sourcemap-organizer] ERROR: ${message}`);
    }

    warn(message: string) {
        console.warn(`[sourcemap-organizer] WARN: ${message}`);
    }
}

/**
 * Source Map 文件处理器
 */
class SourcemapProcessor {
    private logger: Logger;

    constructor(
        private options: Required<SourcemapOrganizerOptions>,
        private distDir: string
    ) {
        this.logger = new Logger(options.verbose);
    }

    /**
     * 扫描并收集所有 source map 文件
     */
    async collectSourcemapFiles(): Promise<string[]> {
        try {
            const pattern = path.join(this.distDir, this.options.pattern);
            this.logger.info(`Scanning for source map files with pattern: ${pattern}`);

            const files = await glob(pattern, {
                absolute: true,
                nodir: true,
            });

            this.logger.info(`Found ${files.length} source map files`);
            return files;
        } catch (error) {
            this.logger.error(`Failed to collect source map files: ${error}`);
            throw error;
        }
    }

    /**
     * 计算目标文件路径
     */
    private calculateTargetPath(sourceFile: string): string {
        const relativePath = path.relative(this.distDir, sourceFile);
        const targetDir = path.join(this.distDir, this.options.targetDir);

        if (this.options.preserveStructure) {
            // 保持原有目录结构
            return path.join(targetDir, relativePath);
        } else {
            // 扁平化结构，只保留文件名
            const fileName = path.basename(sourceFile);
            return path.join(targetDir, fileName);
        }
    }

    /**
     * 确保目录存在
     */
    private async ensureDir(dirPath: string): Promise<void> {
        try {
            await fs.mkdir(dirPath, { recursive: true });
        } catch (error) {
            this.logger.error(`Failed to create directory ${dirPath}: ${error}`);
            throw error;
        }
    }

    /**
     * 移动单个文件
     */
    private async moveFile(sourceFile: string, targetFile: string): Promise<void> {
        try {
            // 确保目标目录存在
            const targetDir = path.dirname(targetFile);
            await this.ensureDir(targetDir);

            // 移动文件
            await fs.rename(sourceFile, targetFile);
            this.logger.info(`Moved: ${path.relative(this.distDir, sourceFile)} -> ${path.relative(this.distDir, targetFile)}`);
        } catch (error) {
            this.logger.error(`Failed to move file from ${sourceFile} to ${targetFile}: ${error}`);
            throw error;
        }
    }

    /**
     * 处理所有 source map 文件
     */
    async processSourcemaps(): Promise<void> {
        try {
            const files = await this.collectSourcemapFiles();

            if (files.length === 0) {
                this.logger.info('No source map files found to process');
                return;
            }

            this.logger.info(`Processing ${files.length} source map files...`);

            // 并行处理文件移动
            const movePromises = files.map(async (sourceFile) => {
                const targetFile = this.calculateTargetPath(sourceFile);

                // 检查源文件和目标文件是否相同
                if (sourceFile === targetFile) {
                    this.logger.info(`Skipping ${path.relative(this.distDir, sourceFile)} (already in target location)`);
                    return;
                }

                await this.moveFile(sourceFile, targetFile);
            });

            await Promise.all(movePromises);
            this.logger.info(`Successfully processed ${files.length} source map files`);
        } catch (error) {
            this.logger.error(`Failed to process source maps: ${error}`);
            throw error;
        }
    }
}

/**
 * Source Map 整理插件
 *
 * 该插件在生产构建完成后自动收集和整理 source map 文件，
 * 将它们移动到指定的子目录中，保持文件结构的整洁。
 *
 * @param options 插件配置选项
 * @returns Vite 插件
 *
 * @example
 * ```typescript
 * import { sourcemapOrganizer } from '@bzl/zhice-exam-env/vite';
 *
 * export default defineConfig({
 *   plugins: [
 *     // 其他插件...
 *     sourcemapOrganizer({
 *       targetDir: 'maps',
 *       verbose: true,
 *     }),
 *   ],
 * });
 * ```
 */
export function sourcemapOrganizer(options: SourcemapOrganizerOptions = {}): Plugin {
    const mergedOptions: Required<SourcemapOrganizerOptions> = {
        ...DEFAULT_OPTIONS,
        ...options,
    };

    return {
        name: 'sourcemap-organizer',

        // 仅在构建时应用
        apply: 'build',

        // 在所有文件写入完成后执行
        async writeBundle(outputOptions) {
            // 检查是否启用插件
            if (!mergedOptions.enabled) {
                return;
            }

            // 获取输出目录
            const distDir = outputOptions.dir || 'dist';
            const absoluteDistDir = path.resolve(distDir);

            const logger = new Logger(mergedOptions.verbose);
            logger.info('Starting source map organization...');

            try {
                const processor = new SourcemapProcessor(mergedOptions, absoluteDistDir);
                await processor.processSourcemaps();
                logger.info('Source map organization completed successfully');
            } catch (error) {
                logger.error(`Source map organization failed: ${error}`);
                // 不抛出错误，避免中断构建过程
            }
        },
    };
}

/**
 * 插件工厂函数的别名，提供更简洁的导入方式
 */
export const createSourcemapOrganizer = sourcemapOrganizer;

/**
 * 默认导出
 */
export default sourcemapOrganizer;
