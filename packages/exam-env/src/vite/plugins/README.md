# Vite 插件集合

这个目录包含了 exam-env 包提供的所有 Vite 插件。

## Source Map 整理插件 (sourcemap-organizer)

### 功能描述

`sourcemapOrganizer` 插件用于在生产构建完成后自动收集和整理 source map 文件。它会扫描 dist 目录中的所有 `.map` 文件，并将它们移动到指定的子目录中，保持项目结构的整洁。

### 核心特性

- ✅ **自动检测**: 自动扫描并收集所有类型的 source map 文件（.js.map、.css.map、.mjs.map 等）
- ✅ **智能移动**: 将 map 文件移动到指定目录，避免与其他构建产物混合
- ✅ **结构保持**: 可选择保持原有的相对路径结构，避免文件名冲突
- ✅ **生产专用**: 仅在生产构建时运行，不影响开发体验
- ✅ **错误处理**: 完整的错误处理机制，构建失败不会中断整个流程
- ✅ **可配置**: 提供丰富的配置选项，满足不同项目需求
- ✅ **Monorepo 兼容**: 与 exam-monorepo 项目完全兼容

### 配置选项

```typescript
interface SourcemapOrganizerOptions {
  /**
   * 目标文件夹名称
   * @default 'sourcemaps'
   */
  targetDir?: string;
  
  /**
   * 是否启用插件
   * @default true (仅在生产环境)
   */
  enabled?: boolean;
  
  /**
   * 文件匹配模式
   * @default '**\/*.map'
   */
  pattern?: string;
  
  /**
   * 是否保持原有的目录结构
   * @default true
   */
  preserveStructure?: boolean;
  
  /**
   * 是否在控制台输出详细日志
   * @default false
   */
  verbose?: boolean;
}
```

### 使用方法

#### 方法 1: 在 base.ts 中已集成（推荐）

插件已经在 `getBaseConfig` 函数中集成，会在生产模式下自动启用：

```typescript
// 在 packages/exam-env/src/vite/base.ts 中已包含
sourcemapOrganizer({
  targetDir: 'sourcemaps',
  enabled: mode === 'prod',
  verbose: false,
  preserveStructure: true,
})
```

#### 方法 2: 手动集成到项目中

如果需要自定义配置，可以在项目的 `vite.config.ts` 中手动添加：

```typescript
import { defineConfig } from 'vite';
import { sourcemapOrganizer } from '@bzl/zhice-exam-env/vite';

export default defineConfig({
  plugins: [
    // 其他插件...
    sourcemapOrganizer({
      targetDir: 'maps',           // 自定义目标目录名
      verbose: true,               // 启用详细日志
      preserveStructure: false,    // 使用扁平化结构
    }),
  ],
  build: {
    sourcemap: true, // 确保生成 source map
  },
});
```

#### 方法 3: 使用工厂函数

```typescript
import { createSourcemapOrganizer } from '@bzl/zhice-exam-env/vite';

export default defineConfig({
  plugins: [
    createSourcemapOrganizer({
      targetDir: 'source-maps',
      enabled: process.env.NODE_ENV === 'production',
    }),
  ],
});
```

### 构建结果示例

**构建前的目录结构:**
```
dist/
├── index.html
├── static/
│   ├── main-abc123.js
│   ├── main-abc123.js.map
│   ├── vendor-def456.js
│   ├── vendor-def456.js.map
│   ├── style-ghi789.css
│   └── style-ghi789.css.map
```

**使用插件后的目录结构 (preserveStructure: true):**
```
dist/
├── index.html
├── static/
│   ├── main-abc123.js
│   ├── vendor-def456.js
│   └── style-ghi789.css
└── sourcemaps/
    └── static/
        ├── main-abc123.js.map
        ├── vendor-def456.js.map
        └── style-ghi789.css.map
```

**使用插件后的目录结构 (preserveStructure: false):**
```
dist/
├── index.html
├── static/
│   ├── main-abc123.js
│   ├── vendor-def456.js
│   └── style-ghi789.css
└── sourcemaps/
    ├── main-abc123.js.map
    ├── vendor-def456.js.map
    └── style-ghi789.css.map
```

### 日志输出示例

当 `verbose: true` 时，插件会输出详细的处理信息：

```
[sourcemap-organizer] Starting source map organization...
[sourcemap-organizer] Scanning for source map files with pattern: /path/to/dist/**/*.map
[sourcemap-organizer] Found 3 source map files
[sourcemap-organizer] Processing 3 source map files...
[sourcemap-organizer] Moved: static/main-abc123.js.map -> sourcemaps/static/main-abc123.js.map
[sourcemap-organizer] Moved: static/vendor-def456.js.map -> sourcemaps/static/vendor-def456.js.map
[sourcemap-organizer] Moved: static/style-ghi789.css.map -> sourcemaps/static/style-ghi789.css.map
[sourcemap-organizer] Successfully processed 3 source map files
[sourcemap-organizer] Source map organization completed successfully
```

### 注意事项

1. **生产环境专用**: 插件默认仅在 `command === 'build'` 时运行
2. **依赖要求**: 需要 `glob` 依赖，已包含在 exam-env 包中
3. **错误处理**: 插件失败不会中断构建过程，只会输出错误日志
4. **文件权限**: 确保构建进程有足够权限读写 dist 目录
5. **路径解析**: 支持绝对路径和相对路径的 source map 文件

### 故障排除

如果插件没有按预期工作，请检查：

1. 确保 `build.sourcemap` 设置为 `true`
2. 检查 `enabled` 选项是否正确设置
3. 验证 `pattern` 匹配模式是否正确
4. 查看控制台日志（设置 `verbose: true`）
5. 确认构建输出目录的权限设置
