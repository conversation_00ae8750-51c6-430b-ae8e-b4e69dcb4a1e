import type { UserConfig, ConfigEnv } from 'vite';
import type { EnvMode } from '../environments.js';

export type AppType = 'admin' | 'user';

// 重新导出 EnvMode 类型以保持向后兼容
export type { EnvMode };

export interface AppConfig {
    /** 应用类型 */
    appType: AppType;
    /** 当前工作目录 */
    cwd?: string;
    /** 共享的别名配置 */
    aliasConfig?: any[];
    /** 自定义配置，会与默认配置进行深度合并 */
    customConfig?: UserConfig;
}

export interface ViteConfigOptions extends AppConfig {
    /** vite 配置环境信息 */
    configEnv: ConfigEnv;
}

export interface ProxyConfig {
    [key: string]: {
        target: string;
        changeOrigin?: boolean;
        secure?: boolean;
    };
}

export interface AppSpecificConfig {
    /** 服务器配置 */
    server?: {
        port?: number;
        proxy?: ProxyConfig;
    };
    /** 构建配置 */
    build?: {
        rollupOptions?: {
            input?: Record<string, string>;
        };
    };
    /** 其他自定义配置 */
    [key: string]: any;
}
