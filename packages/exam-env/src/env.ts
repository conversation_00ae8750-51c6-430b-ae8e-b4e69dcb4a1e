import type { EnvMode } from './environments';

// 声明构建时注入的全局变量
declare const __VITE_BUILD_MODE__: string | undefined;
declare const __DEPLOY_VERSION__: string | undefined;

/** 环境变量类型定义 */
export interface AppEnvVars {
    /** 部署环境 */
    DEPLOY_ENV: EnvMode;
    /** 基础路径 */
    BASE_URL: string;
    /** 是否开发模式 */
    DEV: boolean;
    /** 是否生产模式 */
    PROD: boolean;
    /** 构建模式 */
    MODE: string;
    /** 部署版本 */
    DEPLOY_VERSION?: string;
}

/** 运行时环境变量缓存 */
let envCache: AppEnvVars | null = null;

/**
 * 获取环境变量
 * 这个函数会在运行时被调用，确保 import.meta.env 可用
 */
function getEnvVars(): AppEnvVars {
    if (envCache) {
        return envCache;
    }

    // 确保在浏览器环境中使用
    if (typeof import.meta !== 'undefined' && import.meta.env) {
        // 优先使用构建时注入的模式，如果没有则回退到环境变量
        const buildMode = typeof __VITE_BUILD_MODE__ !== 'undefined' ? __VITE_BUILD_MODE__ : undefined;
        const deployVersion = typeof __DEPLOY_VERSION__ !== 'undefined' ? __DEPLOY_VERSION__ : import.meta.env.VITE_DEPLOY_VERSION;

        envCache = {
            // 使用构建时的 mode 作为 DEPLOY_ENV，如果没有则使用环境变量
            DEPLOY_ENV: (buildMode as EnvMode) || (import.meta.env.VITE_DEPLOY_ENV as EnvMode) || 'dev',
            BASE_URL: import.meta.env.BASE_URL || '/',
            DEV: import.meta.env.DEV || false,
            PROD: import.meta.env.PROD || false,
            MODE: import.meta.env.MODE || 'development',
            DEPLOY_VERSION: deployVersion || undefined,
        };
    } else {
        // 服务端或构建时的默认值
        const buildMode = typeof __VITE_BUILD_MODE__ !== 'undefined' ? __VITE_BUILD_MODE__ : 'dev';
        const deployVersion = typeof __DEPLOY_VERSION__ !== 'undefined' ? __DEPLOY_VERSION__ : undefined;

        envCache = {
            DEPLOY_ENV: buildMode as EnvMode,
            BASE_URL: '/',
            DEV: buildMode !== 'prod',
            PROD: buildMode === 'prod',
            MODE: buildMode === 'prod' ? 'production' : 'development',
            DEPLOY_VERSION: deployVersion,
        };
    }

    return envCache;
}

/**
 * 环境变量访问器
 */
export const env = {
    /** 获取部署环境 */
    get DEPLOY_ENV(): EnvMode {
        return getEnvVars().DEPLOY_ENV;
    },

    /** 获取基础路径 */
    get BASE_URL(): string {
        return getEnvVars().BASE_URL;
    },

    /** 是否开发模式 */
    get DEV(): boolean {
        return getEnvVars().DEV;
    },

    /** 是否生产模式 */
    get PROD(): boolean {
        return getEnvVars().PROD;
    },

    /** 获取构建模式 */
    get MODE(): string {
        return getEnvVars().MODE;
    },

    /** 获取部署版本 */
    get DEPLOY_VERSION(): string | undefined {
        return getEnvVars().DEPLOY_VERSION;
    },

    /** 是否为开发环境 */
    get isDev(): boolean {
        return this.DEPLOY_ENV === 'dev';
    },

    /** 是否为生产环境 */
    get isProd(): boolean {
        return this.DEPLOY_ENV === 'prod';
    },

    /** 是否为开发模式 */
    get isDevelopment(): boolean {
        return this.MODE === 'development';
    },

    /** 获取所有环境变量 */
    getAll(): AppEnvVars {
        return getEnvVars();
    },

    /** 重置缓存（主要用于测试） */
    _resetCache(): void {
        envCache = null;
    },
};

/**
 * 环境判断工具函数
 */
export const isEnv = {
    dev: () => env.DEPLOY_ENV === 'dev',
    qa: () => env.DEPLOY_ENV === 'qa',
    rd: () => env.DEPLOY_ENV === 'rd',
    pre: () => env.DEPLOY_ENV === 'pre',
    prod: () => env.DEPLOY_ENV === 'prod',
    development: () => env.MODE === 'development',
    production: () => env.MODE === 'production',
};
