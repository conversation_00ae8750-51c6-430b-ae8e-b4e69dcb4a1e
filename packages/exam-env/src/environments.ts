/** 环境模式类型 */
export type EnvMode = 'dev' | 'qa' | 'rd' | 'pre' | 'prod';

/** 环境配置类型 */
export interface EnvConfig {
    userApiBaseUrl: string;
    adminApiBaseUrl: string;
}

const environments: Record<EnvMode, EnvConfig> = {
    dev: {
        userApiBaseUrl: 'https://zhice-qa.weizhipin.com',
        adminApiBaseUrl: 'https://zhice-admin-qa.weizhipin.com',
    },
    qa: {
        userApiBaseUrl: 'https://zhice-qa.weizhipin.com',
        adminApiBaseUrl: 'https://zhice-admin-qa.weizhipin.com',
    },
    rd: {
        userApiBaseUrl: 'https://zhice-rd.weizhipin.com',
        adminApiBaseUrl: 'https://zhice-admin-rd.weizhipin.com',
    },
    pre: {
        userApiBaseUrl: 'https://pre-zhice.zhipin.com',
        adminApiBaseUrl: 'https://pre-zhice-admin.zhipin.com',
    },
    prod: {
        userApiBaseUrl: 'https://zhice.zhipin.com',
        adminApiBaseUrl: 'https://zhice-admin.zhipin.com',
    },
};

/**
 * 获取指定环境的配置
 * @param mode 环境模式
 * @returns 环境配置
 */
export function getEnvConfig(mode: string): EnvConfig {
    return environments[mode as EnvMode] || environments.qa;
}
