{"name": "@bzl/zhice-exam-env", "version": "1.0.1", "private": false, "description": "统一的环境变量和 Vite 配置管理包", "keywords": ["vite", "config", "environment", "monorepo"], "license": "ISC", "author": "", "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js", "require": "./dist/index.cjs"}, "./vite": {"types": "./dist/vite/index.d.ts", "import": "./dist/vite/index.js", "require": "./dist/vite/index.cjs"}}, "scripts": {"build": "vite build"}, "peerDependencies": {"@boss/design-resolver": ">=1.7.0", "@rollup/plugin-inject": ">=5.0.0", "@vitejs/plugin-vue": ">=5.2.0", "@vitejs/plugin-vue-jsx": ">=4.2.0", "unplugin-auto-import": ">=19.3.0", "unplugin-vue-components": ">=28.7.0", "vite": ">=6.3.0", "vite-plugin-svg-icons": ">=2.0.0"}, "devDependencies": {"fast-glob": "3.3.3", "typescript": "5.8.3", "vite": "6.3.5", "vite-plugin-dts": "4.5.4"}, "dependencies": {"glob": "^11.0.3"}}