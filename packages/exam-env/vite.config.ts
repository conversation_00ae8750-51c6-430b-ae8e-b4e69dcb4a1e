import { defineConfig } from 'vite'
import path from 'node:path'
import dts from 'vite-plugin-dts'

export default defineConfig({
  build: {
    sourcemap: true,
    lib: {
      entry: {
        index: path.resolve(__dirname, 'src/index.ts'),
        'vite/index': path.resolve(__dirname, 'src/vite/index.ts'),
      },
      name: 'BzZhiceExamEnv',
      formats: ['es', 'cjs'],
      fileName: (format, entryName) => `${entryName}.${format === 'es' ? 'js' : 'cjs'}`,
    },
    rollupOptions: {
      // 确保外部化处理那些你不想打包进库的依赖
      external: [
        'vite',
        '@vitejs/plugin-vue',
        '@vitejs/plugin-vue-jsx',
        '@rollup/plugin-inject',
        'unplugin-auto-import/vite',
        'unplugin-vue-components/vite',
        'vite-plugin-svg-icons',
        '@boss/design-resolver',
        'node:path',
        'node:fs',
        'node:fs/promises',
        'node:process',
        'glob',
      ],
    },
  },
  plugins: [
    dts({
      entryRoot: 'src',
      tsconfigPath: './tsconfig.json',
    }),
  ],
})
