import type { ElementFormatType, LexicalNode, NodeKey, SerializedLexicalNode, Spread } from 'lexical'

import { DecoratorNode } from 'lexical'

export type SerializedDecoratorBlockNode = Spread<{ format: ElementFormatType }, SerializedLexicalNode>

export class Decorator<PERSON>lockNode extends DecoratorNode<Component> {
    __format: ElementFormatType

    constructor(format?: ElementFormatType, key?: NodeKey) {
        super(key)
        this.__format = format || ''
    }

    exportJSON(): SerializedDecoratorBlockNode {
        return {
            format: this.__format || '',
            type: 'decorator-block',
            version: 1,
        }
    }

    createDOM(): HTMLElement {
        const dom = document.createElement('div')
        return dom
    }

    updateDOM(): false {
        return false
    }

    setFormat(format: ElementFormatType): void {
        const self = this.getWritable()
        self.__format = format
    }

    canIndent(): false {
        return false
    }

    isInline(): false {
        return false
    }
}

export function $isDecoratorBlockNode(node: LexicalNode | null | undefined): node is DecoratorBlockNode {
    return node instanceof DecoratorBlockNode
}
