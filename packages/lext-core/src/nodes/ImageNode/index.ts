

import type { ImageComponentProps, ImageConfig, SerializedImageNode } from './type'
import {
    $applyNodeReplacement,
    type DOMConversionMap,
    type DOMConversionOutput,
    type DOMExportOutput,
    type ElementFormatType,
    type LexicalEditor,
    type LexicalNode,
    type <PERSON>deK<PERSON>,
} from 'lexical'

import { DecoratorBlockNode } from '../DecoratorBlockNode'

function convertImageElement(domNode: HTMLElement): null | DOMConversionOutput {
    const imgSrc = domNode.getAttribute('src')
    const config = JSON.parse(domNode.getAttribute('data-lexical-img-config') || '{}')
    const width = Number(domNode.getAttribute('width') || 0) || undefined
    const height = Number(domNode.getAttribute('height') || 0) || undefined

    if (imgSrc && config) {
        const node = $createImageNode({ src: imgSrc, config, width, height })

        return { node }
    }
    return null
}

export class ImageNode extends DecoratorBlockNode {
    #src: string
    #config: Required<ImageConfig>
    #width: number
    #height: number

    get src(): string {
        return this.#src
    }

    get config(): Required<ImageConfig> {
        return this.#config
    }

    get width(): number {
        return this.#width
    }

    get height(): number {
        return this.#height
    }

    set src(value: string) {
        this.#src = value
    }

    set config(value: Required<ImageConfig>) {
        this.#config = value
    }

    set width(value: number) {
        this.#width = value
    }

    set height(value: number) {
        this.#height = value
    }

    constructor(props: ImageComponentProps, format?: ElementFormatType, key?: NodeKey) {
        super(format, key)

        this.#src = props.src
        this.#config = {
            defaultHeight: props.config.defaultHeight,
            defaultWidth: props.config.defaultWidth,
            heightLimit: props.config.heightLimit || 2000,
            widthLimit: props.config.widthLimit || 2000,
        }

        this.#height = props.height || Math.min(this.#config.defaultHeight, this.#config.heightLimit)
        this.#width = props.width || Math.min(this.#config.defaultWidth, this.#config.widthLimit)
    }

    static getType(): string {
        return 'img'
    }

    static clone(node: ImageNode): ImageNode {
        return new ImageNode({ src: node.#src, config: node.#config }, node.__format, node.__key)
    }

    static importJSON(serializedNode: SerializedImageNode): ImageNode {
        const node = $createImageNode(serializedNode)
        node.setFormat(serializedNode.format)
        return node
    }

    static importDOM(): DOMConversionMap | null {
        return {
            img: (domNode: HTMLElement) => {
                if (!domNode.hasAttribute('data-lexical-img')) {
                    return null
                }

                return {
                    conversion: convertImageElement,
                    priority: 1,
                }
            },
        }
    }

    getTextContent(_includeInert?: boolean | undefined, _includeDirectionless?: false | undefined): string {
        // return this.#src;
        return ''
    }

    onChange(state: { src: string, height: number, width: number }): void {
        this.#src = state.src
        this.#height = state.height
        this.#width = state.width
    }

    exportJSON(): SerializedImageNode {
        return {
            ...super.exportJSON(),
            type: 'img',
            src: this.#src,
            config: this.#config,
            width: this.#width,
            height: this.#height,
        }
    }

    createDOM(): HTMLElement {
        return document.createElement('div')
    }

    exportDOM(): DOMExportOutput {
        const element = document.createElement('img')
        element.setAttribute('src', this.#src)
        element.setAttribute('width', String(this.#width || ''))
        element.setAttribute('height', String(this.#height || ''))
        element.setAttribute('data-lexical-img', String(''))
        element.setAttribute('data-lexical-img-config', JSON.stringify(this.#config))

        return { element }
    }

    updateDOM(): false {
        return false
    }

    decorate(_editor: LexicalEditor): Component {
        return h('img', { key: this.#src, src: this.#src, width: this.#width, height: this.#height })
    }
}

export function $createImageNode(props: ImageComponentProps, format?: ElementFormatType, key?: NodeKey): ImageNode {
    return $applyNodeReplacement(new ImageNode(props, format, key))
}

export function $isImageNode(node: ImageNode | LexicalNode | null | undefined): node is ImageNode {
    return node instanceof ImageNode
}
