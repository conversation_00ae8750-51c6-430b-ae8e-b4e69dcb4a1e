import type { EditorState } from 'lexical'

import type { LextEditor } from '../composables'

import { useMount } from '@crm/vueuse-pro'
import { $trimTextContentFromAnchor } from '@lexical/selection'
import { $restoreEditorState, mergeRegister } from '@lexical/utils'
import { $getRoot, $getSelection, $isRangeSelection, RootNode } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { useCanShowPlaceholder } from '../composables/useCanShowPlaceholder'
import { createPlugin } from './PluginFactory'

export const CharacterCountPluginKey = 'CharacterCountPlugin'

interface OptionalProps {
    /**
     * 字符数限制
     */
    limit?: MaybeRef<number>
    /**
     * 是否禁止超过限制的输入
     */
    forbiddenOverLimitInput?: MaybeRef<boolean>
    /**
     * 是否包装溢出的节点
     */
    wrapOverflowedNodes?: MaybeRef<boolean>
    /**
     * 字符集
     */
    charset?: 'UTF-8' | 'UTF-16'
}

export interface CharacterCount {
    /**
     * 字符数
     */
    charactersLen: Ref<number>
    /**
     * 字符数限制
     */
    limit: Ref<number> | number
    /**
     * 是否显示占位符
     */
    placeholderDisplay: Ref<boolean>
    /**
     * 是否溢出
     */
    isOverflowed: Ref<boolean>
}

/**
 * 字符数限制插件，使用本插件需要引入 OverflowNode
 * @param optional
 * @returns
 */
export function CharacterCountPlugin(optional: OptionalProps) {
    return createPlugin<{}>(CharacterCountPluginKey, (editor) => {
        const charactersLen = ref(0)
        if (unref(optional.limit)) {
            useMount(() => {
                let lastRestoredEditorState: EditorState | null = null
                return mergeRegister(
                    unref(editor).registerNodeTransform(RootNode, (rootNode: RootNode) => {
                        const selection = $getSelection()
                        if (!$isRangeSelection(selection) || !selection.isCollapsed()) {
                            return
                        }
                        const prevEditorState = unref(editor).getEditorState()
                        const prevTextContentSize = prevEditorState.read(() => rootNode.getTextContentSize())
                        const textContentSize = rootNode.getTextContentSize()
                        if (prevTextContentSize !== textContentSize) {
                            const delCount = textContentSize - unref(optional.limit)!
                            const anchor = selection.anchor

                            if (delCount > 0) {
                                // Restore the old editor state instead if the last
                                // text content was already at the limit.
                                if (prevTextContentSize === optional.limit && lastRestoredEditorState !== prevEditorState) {
                                    lastRestoredEditorState = prevEditorState
                                    $restoreEditorState(unref(editor), prevEditorState)
                                } else {
                                    $trimTextContentFromAnchor(unref(editor), anchor, delCount)
                                }
                            }
                        }
                    }),
                    unref(editor).registerUpdateListener(({ dirtyLeaves, dirtyElements, editorState }) => {
                        const isComposing = unref(editor).isComposing()
                        const hasContentChanges = dirtyLeaves.size > 0 || dirtyElements.size > 0
                        if (isComposing || !hasContentChanges) {
                            return
                        }
                        charactersLen.value = editorState.read(() => $getRoot().getTextContentSize())
                    }),
                )
            })
        }

        const placeholderDisplay = useCanShowPlaceholder(unref(editor))

        const isOverflowed = computed(() => charactersLen.value >= unref(optional.limit)!)

        return { charactersLen, isOverflowed, limit: optional.limit, placeholderDisplay }
    })
}

export function useCharacterCount(editor?: MaybeRef<LextEditor<any>>): CharacterCount {
    return useLextPluginsReturn<CharacterCount>(CharacterCountPluginKey, editor)
}
