import type { ElementFormatType } from 'lexical'

import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'
import { mergeRegister } from '@lexical/utils'
import { $getSelection, $isElementNode, $isTextNode, COMMAND_PRIORITY_LOW, FORMAT_ELEMENT_COMMAND, SELECTION_CHANGE_COMMAND } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'

export const ElementStylePluginKey = 'ElementStylePlugin'

export interface ElementStylePluginOptional {
    /**
     * 字体样式列表
     */
    list: MaybeRef<ElementFormatType[]>
}

export interface ElementStyle {
    /**
     * 当前字体样式
     */
    currentValue: Ref<ElementFormatType | undefined>
    /**
     * 更新字体样式
     */
    updateElementStyle: (style: ElementFormatType) => void
}

export interface ElementStyleListItem {
    activated: boolean
    key: ElementFormatType
}

/**
 * 文本样式列表插件
 */
export function ElementStylePlugin() {
    return createPlugin<ElementStyle>(ElementStylePluginKey, (editor) => {
        const currentValue = ref<ElementFormatType>()

        const updateElementStyle = (elementFormatType: ElementFormatType) => {
            unref(editor).dispatchCommand(FORMAT_ELEMENT_COMMAND, elementFormatType)
        }

        const $updateFontSelect = () => {
            unref(editor).update(() => {
                const selection = $getSelection()
                if (selection !== null) {
                    // 判断每个节点的某个属性是否相同，如果全部相同返回该属性值，否则返回null
                    const nodes = selection.getNodes()

                    const elementFormatType = nodes.map((node) => {
                        if ($isTextNode(node)) {
                            return node.getParent()?.getFormatType()
                        }
                        if ($isElementNode(node)) {
                            return node.getFormatType()
                        }
                        return undefined
                    })
                    if (elementFormatType.every(v => v === elementFormatType[0])) {
                        currentValue.value = elementFormatType[0]
                    } else {
                        currentValue.value = undefined
                    }
                }
            })
        }

        useMount(() => {
            return mergeRegister(
                unref(editor).registerUpdateListener(({ editorState }) => {
                    editorState.read(() => {
                        $updateFontSelect()
                    })
                }),
                unref(editor).registerCommand(
                    SELECTION_CHANGE_COMMAND,
                    () => {
                        $updateFontSelect()
                        return false
                    },
                    COMMAND_PRIORITY_LOW,
                ),
            )
        })

        return { currentValue: readonly(currentValue), updateElementStyle }
    })
}

export function useElementStyle(editor?: MaybeRef<LextEditor<any>>): ElementStyle {
    return useLextPluginsReturn<ElementStyle>(ElementStylePluginKey, editor)
}
