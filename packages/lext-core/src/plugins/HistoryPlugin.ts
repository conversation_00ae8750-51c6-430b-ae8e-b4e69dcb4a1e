import type { HistoryState } from '@lexical/history'
import { createEmptyHistoryState, registerHistory } from '@lexical/history'

import { createPlugin } from './PluginFactory'

/**
 * 历史记录插件
 */
export function HistoryPlugin(externalHistoryState?: MaybeRef<HistoryState>, delay?: MaybeRef<number>) {
    return createPlugin('HistoryPlugin', (editor) => {
        const historyState = computed<HistoryState>(() => unref(externalHistoryState) || createEmptyHistoryState())

        watchEffect((onInvalidate) => {
            const unregisterListener = registerHistory(unref(editor), historyState.value, unref(delay) || 1000)

            onInvalidate(unregisterListener)
        })

        return historyState
    })
}
