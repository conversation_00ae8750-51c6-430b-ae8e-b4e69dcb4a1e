
import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'
import { $getSelectionStyleValueForProperty, $patchStyleText } from '@lexical/selection'
import { mergeRegister } from '@lexical/utils'
import { $getSelection, $isRangeSelection, COMMAND_PRIORITY_LOW, SELECTION_CHANGE_COMMAND } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'

export const ColorPluginKey = 'ColorPlugin'

export interface ColorPluginOptional {
    /**
     * 默认颜色
     */
    default: MaybeRef<string>
}

export interface Color {
    /**
     * 当前字体样式
     */
    currentColor: Ref<string | undefined>
    /**
     * 更新字体样式
     */
    updateColor: (newFontSize: string) => void
}

/**
 * 字体样式列表插件
 */
export function ColorPlugin(optional: ColorPluginOptional) {
    return createPlugin<Color>(ColorPluginKey, (editor) => {
        const currentColor = ref(optional.default)

        const color = ref(optional.default)

        function updateStyleInSelection(newStyle: string) {
            currentColor.value = newStyle
            unref(editor).update(() => {
                if (unref(editor).isEditable()) {
                    const selection = $getSelection()
                    if (selection !== null) {
                        $patchStyleText(selection, {
                            color: currentColor.value || '',
                        })
                    }
                }
            })
        }

        const updateColor = (color: string) => {
            updateStyleInSelection(color)
        }

        const $updateFontSelect = () => {
            unref(editor).update(() => {
                const selection = $getSelection()
                if ($isRangeSelection(selection)) {
                    color.value = $getSelectionStyleValueForProperty(selection, 'color')
                }
            })
        }

        useMount(() => {
            return mergeRegister(
                unref(editor).registerUpdateListener(({ editorState }) => {
                    editorState.read(() => {
                        $updateFontSelect()
                    })
                }),
                unref(editor).registerCommand(
                    SELECTION_CHANGE_COMMAND,
                    () => {
                        $updateFontSelect()
                        return false
                    },
                    COMMAND_PRIORITY_LOW,
                ),
            )
        })

        return { currentColor, updateColor }
    })
}

export function useColor(editor?: MaybeRef<LextEditor<any>>): Color {
    return useLextPluginsReturn<Color>(ColorPluginKey, editor)
}
