
import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'
import { registerDragonSupport } from '@lexical/dragon'
import { mergeRegister } from '@lexical/utils'

import { useDecorators, useLextPluginsReturn, useRegisterRichText } from '../composables'
import { createPlugin } from './PluginFactory'

export const RichTextPluginKey = 'RichTextPlugin'

/**
 * 富文本格式插件，如果需要使用富文本相关功能，必须引入此插件
 */
export function RichTextPlugin({ plainPaste }: { plainPaste?: boolean }) {
    return createPlugin<Component>(RichTextPluginKey, (editor) => {
        useMount(() => {
            return mergeRegister(useRegisterRichText(unref(editor), { plainPaste }), registerDragonSupport(unref(editor)))
        })

        return defineComponent({
            name: 'RichTextPlugin',
            setup() {
                const decorators = useDecorators(unref(editor))
                return () => decorators.value
            },
        })
    })
}

export function useRichText(editor?: MaybeRef<LextEditor<any>>): Component {
    return useLextPluginsReturn<Component>(RichTextPluginKey, editor)
}
