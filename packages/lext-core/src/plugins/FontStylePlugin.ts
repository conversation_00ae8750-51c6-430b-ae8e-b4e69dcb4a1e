
import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'
import { $getSelectionStyleValueForProperty, $patchStyleText } from '@lexical/selection'
import { mergeRegister } from '@lexical/utils'
import { $getSelection, $isRangeSelection, COMMAND_PRIORITY_LOW, SELECTION_CHANGE_COMMAND } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'

export const FontStylePluginKey = 'FontStylePlugin'

export interface FontStylePluginOptional {
    /**
     * 字体样式列表
     */
    list: CustomStyle[]
}

export interface FontStyle {
    /**
     * 当前字体样式
     */
    currentFontStyle: Ref<CustomStyle | undefined>
    /**
     * 更新字体样式
     */
    updateFontStyle: (newFontSize: CustomStyle) => void
}

export interface CustomStyle {
    fontSize?: number
    color?: string
    lineHeight?: number
}

/**
 * 字体样式列表插件
 */
export function FontStylePlugin(optional: FontStylePluginOptional) {
    return createPlugin<FontStyle>(FontStylePluginKey, (editor) => {
        const currentFontSize = ref<number>()

        const currentFontStyle = computed(() => optional.list.find(i => currentFontSize.value === i.fontSize))

        function updateFontStyle(newFontSize: CustomStyle) {
            unref(editor).update(() => {
                if (unref(editor).isEditable()) {
                    const selection = $getSelection()
                    if (selection !== null) {
                        $patchStyleText(selection, {
                            'font-size': newFontSize.fontSize ? `${newFontSize.fontSize}px` : '',
                            'color': newFontSize.color || '',
                            'line-height': newFontSize.lineHeight ? `${newFontSize.lineHeight}px` : '',
                        })
                    }
                }
            })
        }

        const $updateFontSelect = () => {
            unref(editor).update(() => {
                const selection = $getSelection()
                if ($isRangeSelection(selection)) {
                    const fontSize = $getSelectionStyleValueForProperty(selection, 'font-size')

                    if (fontSize !== null) {
                        currentFontSize.value = Number(fontSize.replace('px', ''))
                    }
                }
            })
        }

        useMount(() => {
            return mergeRegister(
                unref(editor).registerUpdateListener(({ editorState }) => {
                    editorState.read(() => {
                        $updateFontSelect()
                    })
                }),
                unref(editor).registerCommand(
                    SELECTION_CHANGE_COMMAND,
                    () => {
                        $updateFontSelect()
                        return false
                    },
                    COMMAND_PRIORITY_LOW,
                ),
            )
        })

        return { currentFontStyle, updateFontStyle }
    })
}

export function useFontStyle(editor?: MaybeRef<LextEditor<any>>): FontStyle {
    return useLextPluginsReturn<FontStyle>(FontStylePluginKey, editor)
}
