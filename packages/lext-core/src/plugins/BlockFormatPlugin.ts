import type { HeadingTagType } from '@lexical/rich-text'

import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'
import {
    $handleListInsertParagraph,
    $isListNode,
    INSERT_ORDERED_LIST_COMMAND,
    INSERT_UNORDERED_LIST_COMMAND,
    insertList,
    ListNode,
    REMOVE_LIST_COMMAND,
    removeList,
} from '@lexical/list'
import { $createHeadingNode, $createQuoteNode, $isHeadingNode } from '@lexical/rich-text'
import { $wrapNodes } from '@lexical/selection'
import { $getNearestNodeOfType, mergeRegister } from '@lexical/utils'
import { $createParagraphNode, $getSelection, $isRangeSelection, COMMAND_PRIORITY_LOW, INSERT_PARAGRAPH_COMMAND, SELECTION_CHANGE_COMMAND } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'
import { findParentElement } from './shared'

export const BlockFormatPluginKey = 'BlockFormatPlugin'

export const blockTypeToBlockName = {
    bullet: 'Bulleted List',
    check: 'Check List',
    code: 'Code Block',
    h1: 'Heading 1',
    h2: 'Heading 2',
    h3: 'Heading 3',
    h4: 'Heading 4',
    h5: 'Heading 5',
    h6: 'Heading 6',
    number: 'Numbered List',
    paragraph: 'Normal',
    quote: 'Quote',
}

export interface BlockFormatPluginOptional {}

export interface BlockFormat {
    /**
     * 当前块类型
     */
    blockType: Readonly<Ref<keyof typeof blockTypeToBlockName>>
    /**
     * 块格式化
     */
    formatBulletBlockFormat: () => void
    /**
     * 段落格式化
     */
    formatParagraph: () => void
    /**
     * 格式化标题
     * @param headingSize 标题大小
     */
    formatHeading: (headingSize: HeadingTagType) => void
    /**
     * 引用格式化
     */
    formatQuote: () => void
}

/**
 * 块格式化插件
 */
export function BlockFormatPlugin() {
    return createPlugin<BlockFormat>(BlockFormatPluginKey, (editor) => {
        const blockType = ref<keyof typeof blockTypeToBlockName>('paragraph')

        const formatParagraph = () => {
            if (blockType.value !== 'paragraph') {
                unref(editor).update(() => {
                    const selection = $getSelection()
                    if ($isRangeSelection(selection)) {
                        $wrapNodes(selection, () => $createParagraphNode())
                    }
                })
            }
        }

        const formatBulletBlockFormat = () => {
            if (blockType.value !== 'bullet') {
                unref(editor).dispatchCommand(INSERT_UNORDERED_LIST_COMMAND, undefined)
            } else {
                unref(editor).dispatchCommand(REMOVE_LIST_COMMAND, undefined)
            }
        }

        const updateBlockType = () => {
            const selection = $getSelection()
            if ($isRangeSelection(selection)) {
                const { anchorNode, elementDOM, element, elementKey } = findParentElement(unref(editor), selection)
                if (elementDOM !== null) {
                    if ($isListNode(element)) {
                        const parentList = $getNearestNodeOfType<ListNode>(anchorNode, ListNode)
                        const type = parentList ? parentList.getListType() : element.getListType()
                        blockType.value = type as 'bullet'
                    } else {
                        const type = $isHeadingNode(element) ? element.getTag() : element.getType()
                        if (type in blockTypeToBlockName) {
                            blockType.value = type as keyof typeof blockTypeToBlockName
                        }
                    }
                }
            }
        }

        function formatHeading(headingSize: HeadingTagType) {
            if (blockType.value !== headingSize) {
                unref(editor).update(() => {
                    const selection = $getSelection()

                    if ($isRangeSelection(selection)) {
                        $wrapNodes(selection, () => $createHeadingNode(headingSize))
                    }
                })
            }
        }

        function formatQuote() {
            if (blockType.value !== 'quote') {
                unref(editor).update(() => {
                    const selection = $getSelection()

                    if ($isRangeSelection(selection)) {
                        $wrapNodes(selection, () => $createQuoteNode())
                    }
                })
            }
        }

        useMount(() => {
            return mergeRegister(
                unref(editor).registerUpdateListener(({ editorState }) => {
                    editorState.read(() => {
                        updateBlockType()
                    })
                }),
                unref(editor).registerCommand(
                    SELECTION_CHANGE_COMMAND,
                    () => {
                        updateBlockType()
                        return false
                    },
                    COMMAND_PRIORITY_LOW,
                ),
            )
        })

        useMount(() => {
            return mergeRegister(
                unref(editor).registerCommand(
                    INSERT_ORDERED_LIST_COMMAND,
                    () => {
                        insertList(unref(editor), 'number')
                        return true
                    },
                    COMMAND_PRIORITY_LOW,
                ),
                unref(editor).registerCommand(
                    INSERT_UNORDERED_LIST_COMMAND,
                    () => {
                        insertList(unref(editor), 'bullet')
                        return true
                    },
                    COMMAND_PRIORITY_LOW,
                ),
                unref(editor).registerCommand(
                    REMOVE_LIST_COMMAND,
                    () => {
                        removeList(unref(editor))
                        return true
                    },
                    COMMAND_PRIORITY_LOW,
                ),
                unref(editor).registerCommand(
                    INSERT_PARAGRAPH_COMMAND,
                    () => {
                        const hasHandledInsertParagraph = $handleListInsertParagraph()

                        if (hasHandledInsertParagraph) {
                            return true
                        }

                        return false
                    },
                    COMMAND_PRIORITY_LOW,
                ),
            )
        })

        return { blockType: readonly(blockType), formatHeading, formatQuote, formatParagraph, formatBulletBlockFormat }
    })
}

export function useBlockFormat(editor?: MaybeRef<LextEditor<any>>): BlockFormat {
    return useLextPluginsReturn<BlockFormat>(BlockFormatPluginKey, editor)
}
