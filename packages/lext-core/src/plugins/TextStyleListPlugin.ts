import type { TextFormatType } from 'lexical'

import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'
import { mergeRegister } from '@lexical/utils'
import { $getSelection, $isRangeSelection, COMMAND_PRIORITY_LOW, FORMAT_TEXT_COMMAND, SELECTION_CHANGE_COMMAND } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'

export const TextStylePluginKey = 'TextStylePlugin'

export interface TextStylePluginOptional {
    /**
     * 字体样式列表
     */
    list: MaybeRef<TextFormatType[]>
}

export interface TextStyle {
    /**
     * 当前字体样式
     */
    textStyleList: Ref<TextStyleListItem[] | undefined>
    /**
     * 更新字体样式
     */
    triggerTextStyle: (style: TextFormatType) => void
}

export interface TextStyleListItem {
    activated: boolean
    key: TextFormatType
}

/**
 * 文本样式列表插件
 */
export function TextStylePlugin(optional: TextStylePluginOptional) {
    return createPlugin<TextStyle>(TextStylePluginKey, (editor) => {
        const textStyleList = ref(unref(optional.list).map(i => ({ activated: false, key: i })))

        const triggerTextStyle = (style: TextFormatType) => {
            unref(editor).dispatchCommand(FORMAT_TEXT_COMMAND, style)
        }

        const $updateFontSelect = () => {
            unref(editor).update(() => {
                const selection = $getSelection()
                if ($isRangeSelection(selection)) {
                    textStyleList.value.forEach((i) => {
                        i.activated = selection.hasFormat(i.key)
                    })
                }
            })
        }

        useMount(() => {
            return mergeRegister(
                unref(editor).registerUpdateListener(({ editorState }) => {
                    editorState.read(() => {
                        $updateFontSelect()
                    })
                }),
                unref(editor).registerCommand(
                    SELECTION_CHANGE_COMMAND,
                    () => {
                        $updateFontSelect()
                        return false
                    },
                    COMMAND_PRIORITY_LOW,
                ),
            )
        })
        return { textStyleList, triggerTextStyle }
    })
}

export function useTextStyle(editor?: MaybeRef<LextEditor<any>>): TextStyle {
    return useLextPluginsReturn<TextStyle>(TextStylePluginKey, editor)
}
