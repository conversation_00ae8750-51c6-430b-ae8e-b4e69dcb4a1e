
import type { LextEditor } from '../composables'
import { useMount } from '@crm/vueuse-pro'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'

export const EditableRootPluginKey = 'EditableRootPlugin'

export interface EditableRootPluginOptional {
    /**
     * 编辑根元素
     */
    rootRef: Ref<HTMLElement | undefined>
    /**
     * 可编辑默认值
     */
    default?: MaybeRef<boolean>
}

export interface EditableRoot {
    /**
     * 是否可编辑
     */
    editable: Ref<boolean>
}

/**
 * 可编辑根元素插件，大部分情况都是必须的
 */
export function EditableRootPlugin(optional: EditableRootPluginOptional) {
    return createPlugin<EditableRoot>(EditableRootPluginKey, (editor) => {
        const editable = ref(optional.default || true)

        useMount(() => {
            if (optional.rootRef.value) {
                unref(editor).setRootElement(optional.rootRef.value)
                editable.value = unref(editor).isEditable()
            }

            return unref(editor).registerEditableListener((currentIsEditable) => {
                editable.value = currentIsEditable
            })
        })

        return { editable }
    })
}

export function useEditableRoot(editor?: MaybeRef<LextEditor<any>>): EditableRoot {
    return useLextPluginsReturn<EditableRoot>(EditableRootPluginKey, editor)
}
