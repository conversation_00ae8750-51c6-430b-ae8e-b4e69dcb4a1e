import type { KlassConstructor, <PERSON>calCommand, LexicalNode } from 'lexical'

import type { LextEditor } from '../composables'
import type { ImageComponentProps } from '../nodes/ImageNode/type'
import { useMount } from '@crm/vueuse-pro'
import { $insertNodeToNearestRoot } from '@lexical/utils'
import { $applyNodeReplacement, $createNodeSelection, $setSelection, COMMAND_PRIORITY_EDITOR, createCommand } from 'lexical'

import { useLextPluginsReturn } from '../composables'
import { createPlugin } from './PluginFactory'

export const ImagePluginKey = 'ImagePlugin'

export interface ImagePluginOptional {
    ImageNode: KlassConstructor<typeof LexicalNode>
}

export interface Image {
    imageLoading: Ref<boolean>
    insertImage: (event: Event, onInsertSuccess?: () => void) => void
    file: Ref<File | undefined>
}

export const INSERT_IMAGE_COMMAND: LexicalCommand<ImageComponentProps> = createCommand('INSERT_IMAGE_COMMAND')

/**
 * 富文本格式插件，如果需要使用富文本相关功能，必须引入此插件
 */
export function ImagePlugin(optional: ImagePluginOptional) {
    return createPlugin<Image>(ImagePluginKey, (editor) => {
        const file = ref<File>()

        let url: string

        const insertImage = (event: Event, onInsertSuccess?: () => void) => {
            const input = event.target as HTMLInputElement
            if (input.files && input.files.length > 0) {
                file.value = input.files[0]
            }

            if (file.value) {
                url = URL.createObjectURL(file.value)
                const image = new Image()
                image.onload = () => {
                    unref(editor)!.dispatchCommand(INSERT_IMAGE_COMMAND, { src: image.src, config: { defaultWidth: image.width, defaultHeight: image.height } })
                    file.value = undefined
                    onInsertSuccess?.()
                }
                image.src = url
            }
        }

        onUnmounted(() => {
            URL.revokeObjectURL(url)
        })

        const loading = ref(false)

        useMount(() => {
            if (!unref(editor).hasNodes([optional.ImageNode])) {
                throw new Error('ImagePlugin: ImageNode not registered on editor')
            }

            return unref(editor).registerCommand<ImageComponentProps>(
                INSERT_IMAGE_COMMAND,
                (payload) => {
                    unref(editor).update(() => {
                        const imageNode = $insertNodeToNearestRoot($applyNodeReplacement(new optional.ImageNode(payload)))
                        // Set a node selection
                        const nodeSelection = $createNodeSelection()
                        // Add a node key to the selection.
                        // nodeSelection.add($getNodeByKey(nodeKey)?.getParent()?.getKey() || '');
                        nodeSelection.add(imageNode.__key)

                        $setSelection(nodeSelection)
                    })
                    return true
                },
                COMMAND_PRIORITY_EDITOR,
            )
        })

        return { imageLoading: loading, file, insertImage }
    })
}

export function useImage(editor?: MaybeRef<LextEditor<any>>): Image {
    return useLextPluginsReturn<Image>(ImagePluginKey, editor)
}
