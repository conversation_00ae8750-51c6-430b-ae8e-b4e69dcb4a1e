import type { CreateEditorArgs, EditorThemeClasses } from 'lexical'

import { $generateHtmlFromNodes, $generateNodesFromDOM } from '@lexical/html'
import { $getRoot, $insertNodes, createEditor } from 'lexical'
import invariant from 'tiny-invariant'

const ProviderKey = Symbol('LextEditor')

export interface LextEditorConfig<T> {
    /**
     * 命名空间，用于区分不同的编辑器
     */
    name: string
    /**
     * 默认值
     */
    defaultValue?: MaybeRefOrGetter<string>
    /**
     * node 节点类型初始配置，用于扩展节点
     */
    nodes: any[]
    /**
     * 插件配置
     */
    plugins?: T[]
    /**
     * 是否可编辑，默认为 true
     */
    editable?: boolean
    /**
     * 编辑器样式
     */
    theme?: EditorThemeClasses
}

export interface LextEditor<T> {
    editor: ReturnType<typeof createEditor>
    getEditorValue: () => Promise<string>
    setEditorValue: (htmlValue: string) => Promise<undefined>
    pluginsReturn: Record<string, T | undefined>
}

/**
 * 创建编辑器对象，该对象需传入 Editor 组件，才能被插件消费
 *
 * 注意在外面注入的 Node 对象，必须在 Editor 组件渲染之前声明
 * @param initialConfig
 * @returns editor
 * @returns getEditorValue
 * @returns setEditorValue
 */
export function useLextProvider<T extends Record<string, any>>(initialConfig?: LextEditorConfig<T>): LextEditor<T> {
    const defaultConfig: CreateEditorArgs = {
        editable: true,
    }

    const editor = createEditor({
        editable: initialConfig?.editable || defaultConfig.editable,
        namespace: initialConfig?.name || defaultConfig.namespace,
        nodes: initialConfig?.nodes || defaultConfig.nodes,
        theme: initialConfig?.theme || defaultConfig.theme,
    })

    const initPlugins = () => {
        const pluginsReturn = {} as Record<string, T | undefined>
        if (initialConfig?.plugins) {
            initialConfig.plugins.forEach((plugin) => {
                pluginsReturn[plugin.name] = plugin.builder(editor) || undefined
            })
        }
        return pluginsReturn
    }
    const pluginsReturn = initPlugins()

    onMounted(() => {
        const isEditable = initialConfig?.editable || defaultConfig.editable
        editor.setEditable(isEditable !== undefined ? isEditable : true)
    })

    const getEditorValue = async () => {
        return new Promise<string>((resolve) => {
            editor.getEditorState().read(() => {
                const html = $generateHtmlFromNodes(editor, null)
                resolve(html)
            })
        })
    }

    const setEditorValue = (htmlValue: string) => {
        return new Promise<undefined>((resolve) => {
            editor.update(
                () => {
                    const parser = new DOMParser()
                    const dom = parser.parseFromString(htmlValue, 'text/html')

                    const nodes = $generateNodesFromDOM(editor, dom)

                    $getRoot().clear()
                    $getRoot().select()

                    // nodes.forEach((n) => $getRoot().append(n))
                    $insertNodes(nodes)
                    resolve(undefined)
                },
                { tag: 'skip-scroll-into-view' },
            )
        })
    }

    onMounted(() => {
        const defaultValue = toValue(initialConfig?.defaultValue || '')
        if (defaultValue) {
            setEditorValue(defaultValue)
        }
    })

    const returns = { editor, getEditorValue, setEditorValue, pluginsReturn }

    provide<LextEditor<T>>(ProviderKey, returns)

    return returns
}

export function useLext<T>(): LextEditor<T> {
    const editor = inject<LextEditor<T>>(ProviderKey)

    if (!editor) {
        invariant(false, 'useEditor() must be used within a EditorProvider')
    }

    return editor
}

export function useLextPluginsReturn<T>(key: string, editor?: MaybeRef<LextEditor<T>>) {
    if (editor === undefined) {
        editor = useLext<T>()
    }

    return toValue(editor).pluginsReturn[key]!
}
