
import { $createNodeSelection, $setSelection } from 'lexical'

import { onClickOutside } from './onClickOutside'
import { useLexicalComposer } from './useLexicalComposer'

// 创建一个用于检测元素焦点状态的 Hook
export function useImageFocus(element: Ref<HTMLElement | undefined>, isResizing: Ref<boolean>, nodeKey: string) {
    const isFocused = ref<boolean>(false) // 记录焦点状态

    const editor = useLexicalComposer()
    const selectCustomNode = () => {
        editor.update(() => {
            // Set a node selection
            const nodeSelection = $createNodeSelection()
            // Add a node key to the selection.
            // nodeSelection.add($getNodeByKey(nodeKey)?.getParent()?.getKey() || '');
            nodeSelection.add(nodeKey)

            $setSelection(nodeSelection)
        })
    }

    const setFocus = async () => {
        if (!isFocused.value) {
            // e.stopPropagation();
            isFocused.value = true
            selectCustomNode()
        }
    }

    const setBlur = () => {
        if (!isResizing.value && isFocused.value) {
            isFocused.value = false
            // clearSelectCustomNode();
        }
    }

    onClickOutside(element, () => setBlur())

    onMounted(() => {
        if (element.value) {
            // 添加事件监听器
            element.value.addEventListener('click', setFocus)
        }
    })

    onUnmounted(() => {
        if (element.value) {
            // 移除事件监听器
            element.value.removeEventListener('click', setFocus)
        }
    })

    return isFocused
}
