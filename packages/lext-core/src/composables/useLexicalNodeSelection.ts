import type { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'lexical'

import { $createNodeSelection, $getNodeByKey, $getSelection, $isNodeSelection, $setSelection } from 'lexical'

import { useLexicalComposer } from './useLexicalComposer'

function isNodeSelected(editor: <PERSON>calEdit<PERSON>, key: <PERSON><PERSON><PERSON><PERSON>): boolean {
    return editor.getEditorState().read(() => {
        const node = $getNodeByKey(key)
        if (node === null) {
            return false
        }

        return node.isSelected()
    })
}

export function useLexicalNodeSelection(key: MaybeRef<NodeKey>) {
    const editor = useLexicalComposer()
    const isSelected = ref(isNodeSelected(editor, unref(key)))

    watchEffect((onInvalidate) => {
        const unregisterListener = editor.registerUpdateListener(() => {
            isSelected.value = isNodeSelected(editor, unref(key))
        })

        onInvalidate(() => {
            unregisterListener()
        })
    })

    const setSelected = (selected: boolean) => {
        editor.update(() => {
            let selection = $getSelection()
            if (selected) {
                if (!$isNodeSelection(selection)) {
                    selection = $createNodeSelection()
                }
                if ($isNodeSelection(selection)) {
                    if (selected) {
                        selection.add(unref(key))
                    } else {
                        selection.delete(unref(key))
                    }
                }
                $setSelection(selection)
            }
        })
    }

    const clearSelection = () => {
        editor.update(() => {
            const selection = $getSelection()
            if ($isNodeSelection(selection)) {
                selection.clear()
            }
        })
    }

    return {
        isSelected: readonly(isSelected),
        setSelected,
        clearSelection,
    }
}
