
import { useMount } from '@crm/vueuse-pro'
import { $getSelection, $isNodeSelection } from 'lexical'
import { useLexicalComposer } from '../composables/useLexicalComposer'

/**
 * @param nodeKey 节点 key
 */
export function useBlur(elementRef: Ref<HTMLElement | undefined>) {
    const editor = useLexicalComposer()

    useMount(() => {
        const handleBlur = () => {
            setTimeout(() => {
                const rootElement = editor.getRootElement()
                const activeElement = document.activeElement

                if (rootElement?.contains(activeElement)) {
                    return
                }

                // 如果新的焦点元素不在 rootElement 内部，则清除选中状态

                editor.update(() => {
                    const selection = $getSelection()
                    if ($isNodeSelection(selection)) {
                        selection.clear()
                    }
                })
            }, 0) // 在下一个事件循环中执行
        }

        // 使用局部变量保存元素引用
        const element = elementRef.value
        element?.addEventListener('blur', handleBlur)

        return () => {
            // 使用局部变量确保移除正确的监听器
            element?.removeEventListener('blur', handleBlur)
        }
    })
}
