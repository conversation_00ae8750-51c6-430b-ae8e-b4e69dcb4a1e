

// 创建一个用于检测元素焦点状态的 Hook
export function useHover(element: Ref<HTMLElement | undefined>) {
    const isHover = ref<boolean>(false) // 记录焦点状态

    const handleMouseEnter = () => {
        isHover.value = true
    }

    const handleMouseLeave = () => {
        isHover.value = false
    }

    watchEffect(() => {
        if (element.value) {
            element.value.addEventListener('mouseenter', handleMouseEnter)
            element.value.addEventListener('mouseleave', handleMouseLeave)
        }
    })
    onUnmounted(() => {
        element.value?.removeEventListener('mouseenter', handleMouseEnter)
        element.value?.removeEventListener('mouseleave', handleMouseLeave)
    })

    return isHover
}
