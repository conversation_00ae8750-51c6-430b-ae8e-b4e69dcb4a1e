import type { LexicalEditor } from 'lexical'
import { useMount } from '@crm/vueuse-pro'
import { $canShowPlaceholder } from '@lexical/text'
import { mergeRegister } from '@lexical/utils'

export function useCanShowPlaceholder(editor: LexicalEditor) {
    const canShowPlaceholder = ref(false)

    const updatePlaceholderVisibility = () => {
        editor.getEditorState().read(() => {
            canShowPlaceholder.value = $canShowPlaceholder(editor.isComposing())
        })
    }

    useMount(() => {
        updatePlaceholderVisibility()
        return mergeRegister(
            editor.registerUpdateListener(() => {
                updatePlaceholderVisibility()
            }),
            editor.registerEditableListener(() => {
                updatePlaceholderVisibility()
            }),
        )
    })

    return readonly(canShowPlaceholder)
}
