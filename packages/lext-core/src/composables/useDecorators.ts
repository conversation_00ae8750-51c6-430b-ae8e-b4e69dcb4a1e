import type { LexicalEditor } from 'lexical'
import { DefineComponent } from 'vue';
import { useMount } from '@crm/vueuse-pro'
import { Teleport } from 'vue';

export function useDecorators(editor: LexicalEditor) {
    const decoratedTeleports = shallowRef<(VNode | null)[]>([])

    const updateDecorators = (decorators: Record<string, DefineComponent>) => {
        const decoratorKeys = Object.keys(decorators)

        decoratedTeleports.value = decoratorKeys.map((nodeKey) => {
            const vueDecorator = decorators[nodeKey]
            const element = editor.getElementByKey(nodeKey)

            if (element !== null) {
                return h(Teleport, { key: nodeKey, to: element }, vueDecorator)
            }
            return null
        })
    }

    useMount(() => {
        updateDecorators(editor.getDecorators())
        return editor.registerDecoratorListener(updateDecorators)
    })

    return decoratedTeleports
}
