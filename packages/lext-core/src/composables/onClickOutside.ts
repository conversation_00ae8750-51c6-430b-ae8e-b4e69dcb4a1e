

export function onClickOutside(target: Ref<HTMLElement | undefined>, handler: (event: MouseEvent) => void) {
    let shouldListen = true

    const listener = (event: MouseEvent) => {
        const el = target.value

        if (!el || el === event.target || event.composedPath().includes(el)) {
            return
        }

        if (event.detail === 0) {
            shouldListen = true
        }

        if (!shouldListen) {
            shouldListen = true
            return
        }

        handler(event)
    }

    onMounted(() => {
        window.addEventListener('click', listener, { passive: true })
        window.addEventListener(
            'pointerdown',
            (e) => {
                const el = target.value
                shouldListen = !!(el && !e.composedPath().includes(el))
            },
            { passive: true },
        )

        onUnmounted(() => {
            window.removeEventListener('click', listener)
            window.removeEventListener('pointerdown', (e) => {
                const el = target.value
                shouldListen = !!(el && !e.composedPath().includes(el))
            })
        })
    })
}
