
import { useMount } from '@crm/vueuse-pro'
import { CLICK_COMMAND, COMMAND_PRIORITY_LOW } from 'lexical'
import { useLexicalComposer } from '../composables/useLexicalComposer'
import { useLexicalNodeSelection } from '../composables/useLexicalNodeSelection'

interface NodeClickEventOptions {
    /**
     * 是否深度选中
     */
    deep?: boolean
    /**
     * 是否禁用容器点击事件
     */
    containerDisabled?: boolean
}

/**
 * 节点点击事件注册（选中当前节点，点击全部）
 * @param containerRef 容器元素引用
 * @param nodeKey 节点 key
 * @param isResize 是否处于调整大小状态
 * @param options 选项
 */
export function useNodeClickEvent(containerRef: Ref<HTMLElement | undefined>, nodeKey: string, isResize: Ref<boolean>, options: NodeClickEventOptions) {
    const editor = useLexicalComposer()
    const { setSelected } = useLexicalNodeSelection(nodeKey)

    const handleClickChangeSelect = (event: MouseEvent) => {
        if (!containerRef.value) {
            return false
        }

        let targetElement = event.target as HTMLElement | null
        let isTargetElementInContainer = false
        if (options.deep) {
            while (targetElement && targetElement !== containerRef.value) {
                targetElement = targetElement.parentElement
            }

            if (targetElement === containerRef.value) {
                isTargetElementInContainer = true
            }
        }

        if (!isTargetElementInContainer || !targetElement) {
            return false
        }

        if (options.containerDisabled && event.target === containerRef.value) {
            return false
        }

        return true
    }

    useMount(() => {
        return editor.registerCommand<MouseEvent>(
            CLICK_COMMAND,

            (event) => {
                const shouldSelect = handleClickChangeSelect(event)
                setSelected(shouldSelect)
                return false
            },
            COMMAND_PRIORITY_LOW,
        )
    })
}
