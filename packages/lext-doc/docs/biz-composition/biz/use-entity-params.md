# useEntityParams

`useEntityParams` 是一个 Vue Composition API 钩子，用于在 Vue 应用中管理和获取实体的 URL 参数。它便于从路由中提取实体的 ID，并确定实体是处在创建状态还是编辑状态。

## 功能

- 提取和管理路由参数中的实体 ID。
- 确定实体是否处于创建状态。
- 访问当前路由的查询参数。

## 安装

确保你的项目中已经安装了 Vue 3 和 Vue Router。

## 使用方法

在 Vue 组件的 `setup` 函数中使用 `useEntityParams`，并指定你想要提取的路由参数键名。

```javascript
import { useEntityParams } from '@crm/vueuse-pro'

export default {
    setup() {
        const key = 'id' // 路由参数键名
        const { entityId, isCreating, query } = useEntityParams(key)

        return { entityId, isCreating, query }
    },
}
```

## API

### `useEntityParams(key: string): EntityParams`

#### 参数

- `key`: 路由参数键名，用于指定想要提取的实体 ID 所对应的路由参数。

#### 返回值

返回一个包含以下属性的对象：

- `entityId`: 提取的实体 ID，为 `ComputedRef<string>` 类型。
- `isCreating`: 表明实体是否处于创建状态，为 `ComputedRef<boolean>` 类型。
- `query`: 当前路由的查询参数，为 `LocationQuery | LocationQuery[]` 类型。

## 示例

```vue
<template>
    <div>
        实体 ID: {{ entityId }}
        <div v-if="isCreating">
            创建实体
        </div>
        <div v-else>
            编辑实体
        </div>
    </div>
</template>

<script setup>
import { useEntityParams } from '@crm/vueuse-pro'

const { entityId, isCreating } = useEntityParams('id')
</script>
```

在这个示例中，`useEntityParams` 根据路由参数 "id" 提取实体 ID，并确定实体是处在创建状态还是编辑状态。`isCreating` 为 `true` 时表示创建实体，为 `false` 时表示编辑实体。
