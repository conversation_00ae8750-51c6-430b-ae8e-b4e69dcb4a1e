# useLeaveControl

`useLeaveControl` 是一个 Vue Composition API 钩子，用于在用户尝试离开当前路由前进行控制，例如，当用户有未保存的更改时提示他们。这对提高用户体验非常有用，防止用户在不经意间丢失重要数据。

## 功能

- 监听路由离开事件。
- 在用户离开前基于条件显示确认对话框。
- 允许基于条件通过编程方式控制路由离开行为。

## 安装

确保你的项目中已经安装了 Vue 3 和 Vue Router。

## 使用方法

首先导入 `useLeaveControl` 到你的组件中，并传入当前值和旧值用于比较：

```javascript
import { useLeaveControl } from '@crm/vueuse-pro'

export default {
    setup() {
        const newValue = ref() // 当前值
        const previousValue = ref() // 旧值

        const { leaveControl, onLeave, navigateBack } = useLeaveControl(newValue, previousValue)

        // 在这里继续你的逻辑...

        return { leaveControl, navigateBack }
    },
}
```

## API

### `useLeaveControl(newValue: MaybeRefOrGetter<unknown>, previousValue: MaybeRefOrGetter<unknown>): LeaveControl`

#### 参数

- `newValue`: 新值，与旧值比较确定是否需要控制离开。
- `previousValue`: 旧值，与新值进行比较。

#### 返回值

返回一个包含以下属性和方法的对象：

- `leaveControl`: 表示是否启动离开提示的响应式变量。
- `onLeave`: 打开离开提示对话框的方法。
- `navigateBack`: 返回上一页的方法，可选择跳过离开控制。

## 类型定义

### `LeaveControl`

```typescript
export interface LeaveControl {
    leaveControl: Ref<boolean>
    onLeave: () => Promise<boolean>
    navigateBack: (skipControl?: boolean) => void
}
```

## 示例

假设你有一个表单，用户填写后如果没有保存就尝试离开，你想提示他们：

```vue
<template>
    <Form ref="formRef">
        <!-- 表单内容 -->
    </Form>
    <button @click="navigateBack">
        返回
    </button>
</template>

<script setup>
import { useLeaveControl } from '@crm/vueuse-pro'
import { ref } from 'vue'

const formRef = ref()
const formData = ref(getFormData())
const originalFormData = ref(getFormData()) // 假定这是一个获取初始表单数据的函数

const { navigateBack } = useLeaveControl(formData, originalFormData)

function getFormData() {
    // 这应该是获取表单数据的函数
    return {}
}
</script>
```

在这个例子中，`useLeaveControl` 根据 `formData` 和 `originalFormData` 的比较结果决定是否在用户尝试离开当前页面时显示离开提示。`navigateBack` 方法被用于编程式地处理返回操作，同时考虑了离开控制逻辑。
