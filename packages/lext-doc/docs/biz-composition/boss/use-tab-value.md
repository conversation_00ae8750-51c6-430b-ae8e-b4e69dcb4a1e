# useOptionValue

`useOptionValue` 是一个 Vue Composition API 钩子，专门用于处理标签页（Tab）之间的切换状态。它提供了对选项列表和当前选中的标签值的响应式管理。

## 功能

- 管理标签页选项列表和当前激活状态。
- 自动将当前值设置为选项列表中的第一个标签值或默认值。
- 当当前值变化时，执行提供的回调函数。

## 使用方法

在组件的 `setup` 函数中使用 `useOptionValue`，并提供选项列表、变化回调函数和默认值：

```javascript
import { useOptionValue } from '@crm/vueuse-pro'

export default {
    setup() {
        const tabsOptions = ref([
            { value: 'tab1', label: '标签一' },
            { value: 'tab2', label: '标签二' },
            // ...其他标签
        ])
        const handleTabChange = (newValue, oldValue) => {
            console.log('Tab changed from', oldValue, 'to', newValue)
        }
        const { current } = useOptionValue({
            options: tabsOptions,
            handleChange: handleTabChange,
            defaultValue: 'tab1',
        })

        return { current }
    },
}
```

## API

### `useOptionValue<T extends string, U extends OptionsType<T>>(props: useOptionValueProps<T, U>): useOptionValueReturn<T, U>`

#### 参数

- `props`: 参数对象包括选项列表 `options`，值变化的回调函数 `handleChange` 和可选的默认值 `defaultValue`。

#### 返回值

- 返回一个对象，其中包括响应式的 `options` 和当前选中的 `current` 值。

## 类型定义

### `OptionsType`

```typescript
export type OptionsType<Value extends string> = MaybeRef<{ value: Value, label: string }[]>
```

表示选项列表类型，每个选项是一个对象，包含 `value` 和 `label` 属性。

### `useOptionValueReturn`

```typescript
export interface useOptionValueReturn<T extends string, U extends OptionsType<T>> {
    options: U
    current: Ref<T | undefined>
}
```

表示 `useOptionValue` 钩子返回值的类型，包括 `options` 和 `current`。

### `useOptionValueProps`

```typescript
export interface useOptionValueProps<T extends string, U extends OptionsType<T>> {
    options: U
    handleChange?: (newValue?: T, oldValue?: T) => void
    defaultValue?: MaybeRef<T>
}
```

表示 `useOptionValue` 钩子参数的类型，包括 `options`，`handleChange` 回调和 `defaultValue`。

## 示例

```vue
<template>
    <div>
        <ul>
            <li v-for="option in options" :key="option.value" @click="() => current = option.value">
                {{ option.label }}
            </li>
        </ul>
        <p>当前选中的标签是：{{ current }}</p>
    </div>
</template>

<script setup>
import { useOptionValue } from '@crm/vueuse-pro'
import { ref } from 'vue'

const tabsOptions = ref([
    { value: 'tab1', label: '标签一' },
    { value: 'tab2', label: '标签二' },
    // ...其他标签
])
const { options, current } = useOptionValue({
    options: tabsOptions,
    defaultValue: 'tab1',
})
</script>
```

在这个示例中，`useOptionValue` 提供了对标签页的动态管理。当用户点击不同的标签时，`current` 值将相应更新。
