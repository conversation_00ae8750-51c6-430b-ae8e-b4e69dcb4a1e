# useTableColumns

`useTableColumns` 是一个 Vue Composition API 钩子，它提供了一种简便的方法来处理和显示表格数据，特别是支持对表格列的分组功能。此钩子允许开发者对表格行进行分组，使得同一组的行可以共享表格列的某些属性，如合并单元格。

## 功能

- 对表格数据进行分组。
- 根据分组的需要合并表格列。
- 自动处理表格列配置，以支持分组功能。

## 使用方法

在组件的 `setup` 函数中使用 `useTableColumns`，并提供表格数据、默认列配置以及分组列键名：

```vue
import { useTableColumns } from '@crm/vueuse-pro'

export default {
  setup() {
    const tableData = ref([...]); // 表格数据
    const defaultColumns = ref([...]); // 默认列配置
    const groupColKeys = ref([...]); // 分组列键名

    const { columns, tableData: groupedData } = useTableColumns({
      tableData,
      default: defaultColumns,
      groupColKeys,
    });

    return { columns, groupedData };
  },
};
```

## API

### `useTableColumns<T extends Record<string, any>>(props: useTableColGroupProps<T>): useTableColGroupRetrun<T>`

#### 参数

- `props`: 参数对象，包含表格数据 `tableData`，默认列配置 `default` 和可选的分组列键名 `groupColKeys`。

#### 返回值

- 返回一个对象，其中包括经过处理的表格列 `columns` 和表格数据 `tableData`。

## 类型定义

### `OptionsType`

```typescript
export type OptionsType<Value extends string> = MaybeRef<{ value: Value, label: string }[]>
```

表示选项列表类型，每个选项包含值和标签。

### `useTableColGroupRetrun`

```typescript
export interface useTableColGroupRetrun<T extends Record<string, any>> {
    columns: ComputedRef<ITableColumnDataRaw[]>
    tableData: Ref<T[]>
}
```

表示 `useTableColumns` 钩子返回值的类型，包括 `columns` 和 `tableData`。

### `useTableColGroupProps`

```typescript
export interface useTableColGroupProps<T extends Record<string, any>> {
    tableData: MaybeRefOrGetter<T[]>
    default: MaybeRefOrGetter<Partial<ITableColumnDataRaw>[]>
    groupColKeys?: MaybeRefOrGetter<Array<keyof T>>
}
```

表示 `useTableColumns` 钩子参数的类型，包括 `tableData`，`default` 和可选的 `groupColKeys`。

## 示例

假设您有一个用户列表，您希望根据用户的部门对这些用户进行分组，并在同一部门的用户行中合并“部门”列：

```vue
<template>
    <Table :columns="columns" :data="groupedData" />
</template>

<script setup>
import { useTableColumns } from '@crm/vueuse-pro'
import { ref } from 'vue'

const tableData = ref([]) // 表格数据
const defaultColumns = ref([]) // 默认列配置
const groupColKeys = ref(['department']) // 分组列键名

const { columns, groupedData } = useTableColumns({
    tableData,
    default: defaultColumns,
    groupColKeys,
})
</script>
```

在这个示例中，`useTableColumns` 钩子处理了表格列的配置，使得同一部门的用户行可以在“部门”列中合并单元格。
