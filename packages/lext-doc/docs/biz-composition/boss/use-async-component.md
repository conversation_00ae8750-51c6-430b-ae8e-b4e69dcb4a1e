# useAsyncComponent

`useAsyncComponent` 是一个高阶组件（HOC）工具，用于简化 Vue 应用中异步组件的使用。它支持自定义加载中和加载失败时的显示组件，以及设置加载延迟和超时时间。

## 安装

确保你的项目中已经安装了 Vue 3 以及任何你想在异步组件中使用的第三方库。本例中使用了 `@boss/design` 包的 `Skeleton` 组件作为默认的加载中显示组件。

## 使用方法

为了创建一个异步组件，你需要提供一个组件加载器（一个返回 `import()` 调用的函数）和可选的配置项。

```javascript
import { useAsyncComponent } from '@crm/vueuse-pro'

// 定义一个异步加载的组件
const MyAsyncComponent = useAsyncComponent(() => import('./MyComponent.vue'))

// 额外的配置选项也可以被提供
const MyCustomAsyncComponent = useAsyncComponent(() => import('./MyCustomComponent.vue'), {
    loadingComponent: MyCustomLoadingComponent, // 自定义加载中组件
    errorComponent: MyCustomErrorComponent, // 自定义加载失败组件
    timeout: 30000, // 设置超时时间
    delay: 200, // 设置延迟加载时间
})
```

## API

### `useAsyncComponent(loader: AsyncComponentLoader<Component>, options?: UseAsyncComponentOptions): Raw<Component>`

#### 参数

- `loader`: 组件加载器，必须是一个异步函数，通常是通过 `import()` 来动态导入组件。
- `options`: 配置项，包括 `loadingComponent`, `errorComponent`, `timeout`, `delay` 等。

#### 配置项 `UseAsyncComponentOptions`

- `loadingComponent`: 加载中的组件，默认是使用 `Skeleton` 组件。
- `errorComponent`: 加载失败时显示的组件，默认显示 '加载失败，请刷新重试'。
- `timeout`: 超时时间，超过这个时间如果组件还未加载完成，则显示 `errorComponent`，默认 `60000` 毫秒。
- `delay`: 延迟加载时间，延迟显示加载中的组件，以避免快速闪烁，默认 `0` 毫秒。

## 示例

```html
<template>
    <div>
        <!-- 使用异步组件 -->
        <MyAsyncComponent />
        <MyCustomAsyncComponent />
    </div>
</template>

<script setup>
    import { useAsyncComponent } from '@crm/vueuse-pro'

    const MyAsyncComponent = useAsyncComponent(() => import('./MyComponent.vue'))

    // 使用自定义的加载中和加载失败显示组件
    const MyCustomAsyncComponent = useAsyncComponent(() => import('./MyCustomComponent.vue'), {
        loadingComponent: MyCustomLoadingComponent,
        errorComponent: MyCustomErrorComponent,
        timeout: 30000,
        delay: 200,
    })
</script>
```

在这个例子中，`MyAsyncComponent` 和 `MyCustomAsyncComponent` 是通过 `useAsyncComponent` 创建的异步组件。`MyCustomAsyncComponent` 使用了自定义的加载中和加载失败显示组件，并设置了超时时间和延迟加载时间。
