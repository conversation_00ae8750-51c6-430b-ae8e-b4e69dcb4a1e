# useFormValidator

`useFormValidator` 是一个 Vue Composition API 钩子，用于提供表单组件的校验功能。它支持自定义预校验函数和自定义校验函数，灵活处理复杂的表单校验逻辑。

## 功能

- 执行整个表单的校验。
- 执行指定字段的校验。
- 使用预校验函数集来处理校验前的操作。
- 支持自定义校验逻辑。

## 安装

确保你的项目中已经安装了 Vue 3 和 `@boss/design` 库。

## 使用方法

在组件的 `setup` 函数中使用 `useFormValidator`，并传入表单组件的引用 `formRef`：

```javascript
import { useFormValidator } from '@crm/vueuse-pro'

export default {
    setup() {
        const formRef = ref(null)
        const { validateAll, validateField, injectPreFunc } = useFormValidator({ formRef })

        // 可以在这里定义其他逻辑...

        return {
            formRef,
            validateAll,
            validateField,
            injectPreFunc,
        }
    },
}
```

## API

### `useFormValidator(props: useFormValidatorProps): FormValidator`

#### 参数

- `props`: 包含表单组件引用和自定义校验函数的参数对象。

#### 返回值

返回一个包含以下方法和属性的对象：

- `validateAll`: 执行整个表单的校验，并返回校验是否通过。
- `validateField`: 执行指定字段的校验。
- `preFuncs`: 存储预校验函数的引用对象。
- `injectPreFunc`: 用于注入预校验函数的方法。

## 类型定义

### `useFormValidatorProps`

```typescript
export interface useFormValidatorProps {
    formRef: Ref<_FormComponent | undefined>
    customValidate?: () => Promise<boolean> | boolean
}
```

### `FormValidator`

```typescript
export interface FormValidator {
    validateAll: () => Promise<boolean>
    validateField: (fieldNames: string[]) => Promise<void>
    preFuncs: Ref<Record<string, () => boolean>>
    injectPreFunc: (key: string, funcs: () => boolean) => void
}
```

## 示例

```vue
<template>
    <Form ref="formRef">
        <!-- 表单内容 -->
    </Form>
    <Button @click="validateAll">
        提交
    </Button>
</template>

<script setup>
import Button from '@boss/design/es/button'
import Form from '@boss/design/es/form'
import { useFormValidator } from '@crm/vueuse-pro'
import { useRef } from 'vue'

const formRef = ref(null)
const { validateAll } = useFormValidator({ formRef })

// 提交时进行校验
async function onSubmit() {
    const isValid = await validateAll()
    if (isValid) {
        // 通过校验，执行提交操作
    }
}
</script>
```

在这个示例中，`useFormValidator` 钩子为 `Form` 表单组件提供了校验功能。当点击提交按钮时，`validateAll` 函数会被调用来执行校验，并根据返回的校验结果决定是否执行提交操作。
