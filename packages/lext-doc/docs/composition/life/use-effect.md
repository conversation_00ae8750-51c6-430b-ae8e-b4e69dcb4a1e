# useEffect

`useEffect` 是一个用于深度监听并执行清理函数的实用函数。该函数主要用于响应式事件绑定的情况，当组件卸载时自动执行清理函数。

## 使用

```ts
import { useEffect } from '@crm/vueuse-pro'
import { ref } from 'vue'

export default {
    setup() {
        const count = ref(0)

        // 示例：使用 useEffect 来监听 count 的变化
        useEffect(() => {
            console.log(`count已更新: ${count.value}`)

            // 返回一个清理函数
            return () => {
                console.log('清理函数被调用')
            }
        })

        // 更改 count
        count.value++
    },
}
```

## API

```ts
// 返回一个 WatchStopHandle 函数，用于手动停止监听。
export function useEffect(unregister: () => undefined | (() => any)): WatchStopHandle
```

## 示例

以下是一个使用 useEffect 来进行事件监听和清理的示例：

```javascript
import { useEffect } from '@crm/vueuse-pro'
import { ref } from 'vue'

export default {
    setup() {
        const isListening = ref(true)

        // 使用 useEffect 进行事件监听
        useEffect(() => {
            if (isListening.value) {
                window.addEventListener('resize', resizeHandler)
            }

            // 返回清理函数
            return () => {
                window.removeEventListener('resize', resizeHandler)
            }
        })

        function resizeHandler() {
            console.log('窗口大小改变了')
        }
    },
}
```

在这个示例中，useEffect 被用来监听窗口大小的变化，并在组件卸载时自动移除事件监听器。
