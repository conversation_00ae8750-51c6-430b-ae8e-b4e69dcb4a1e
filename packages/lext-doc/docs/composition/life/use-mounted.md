# useMount

`useMount` 是一个 Vue Composition API 实用函数，它为在组件的生命周期挂载（`mounted`）和卸载（`unmounted`）时提供了一种简洁的方式来执行和清理回调函数。

## 使用

```javascript
import { useMount } from '@crm/vueuse-pro'

export default {
    setup() {
        useMount(() => {
            // 在这里编写你的挂载时执行的逻辑
            console.log('组件已挂载')

            // 返回一个函数，用于在组件卸载时执行清理逻辑
            return () => {
                console.log('组件已卸载')
            }
        })
    },
}
```

## API

### `useMount(unregister: () => undefined | (() => any)): void`

- `unregister`: 一个回调函数，该函数在组件挂载时执行，并应该返回一个清理函数或者 `undefined`。返回的清理函数将在组件卸载时执行。

## 示例

以下是使用 `useMount` 来设置和清理事件监听器的示例：

```javascript
import { useMount } from '@crm/vueuse-pro'

export default {
    setup() {
        useMount(() => {
            // 在这里设置事件监听器
            const handleResize = () => {
                console.log('窗口尺寸变化了')
            }
            window.addEventListener('resize', handleResize)

            // 返回一个清理函数，用于移除事件监听器
            return () => {
                window.removeEventListener('resize', handleResize)
            }
        })
    },
}
```

在这个示例中，当组件挂载时，将添加一个窗口尺寸变化的事件监听器。然后，在组件卸载时，将移除该事件监听器，以避免潜在的内存泄漏。
