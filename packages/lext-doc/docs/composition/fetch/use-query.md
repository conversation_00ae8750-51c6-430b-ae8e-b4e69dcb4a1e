# useQueryLite

`useQueryLite` 是一个用于远程数据请求的 Vue Composition API 钩子。它提供了数据请求、缓存、重新获取和数据选择功能，优化数据获取工作流程，并增强组件与远程数据交互的能力。

## 使用

```javascript
import { useQueryLite } from '@crm/vueuse-pro'

const { raw, isPending, data, refetch } = useQueryLite({
    keys: ['someUniqueKey'],
    fetcher: fetchDataFunction,
    select: dataSelectorFunction,
    onSuccess: successCallbackFunction,
    onError: errorCallbackFunction,
    // ...其他选项
})
```

其中 `fetchDataFunction` 是您的数据请求函数，`dataSelectorFunction` 是用于选择并返回所需数据块的函数。

## API

### `useQueryLite(option: useQueryLiteOption<T, U>): QueryResult<T, U>`

- `option`: 包含请求的各种选项的配置对象。

返回一个对象，包含以下属性：

- `raw`: 原始响应数据，为 `Ref<T | undefined>` 类型。
- `isPending`: 请求是否正在进行，为 `Ref<boolean>` 类型。
- `data`: 经过选择器处理后的数据，为 `Ref<U | undefined>` 类型。
- `refetch`: 函数，调用后重新获取远程数据。

## 类型定义

### `QueryKey`

```typescript
export type QueryKey = string | number | boolean | null | undefined | Record<string, any>
```

### `QueryResult`

```typescript
export interface QueryResult<T, U> {
    raw: Ref<T | undefined>
    isPending: Ref<boolean>
    data: Ref<U | undefined>
    refetch: () => Promise<T | undefined>
}
```

### `useQueryLiteOption`

```typescript
export interface useQueryLiteOption<T extends Record<string, any> | undefined, U> {
    // ...其他属性
    select?: (raw?: T) => U
    disabled?: MaybeRefOrGetter<boolean>
    keepPreviousData?: boolean
    cacheDuration?: number
    // ...其他属性
}
```

## 示例

以下是使用 `useQueryLite` 进行数据请求和缓存处理的示例：

```html
<template>
    <div v-if="isPending">数据加载中...</div>
    <div v-else-if="data">
        <!-- 显示处理后的数据 -->
    </div>
</template>

<script>
    import { useQueryLite } from '@crm/vueuse-pro'

    export default {
        setup() {
            const { raw, isPending, data, refetch } = useQueryLite({
                keys: ['userProfile', userId],
                fetcher: () => getUserProfile(userId),
                select: (rawData) => rawData?.profile,
                onSuccess: (data) => console.log('数据获取成功:', data),
                onError: (error) => console.error('数据获取失败:', error),
                // 可能的其他配置
            })

            // 使用 raw, isPending, data, refetch 等进行数据操作

            return {
                raw,
                isPending,
                data,
                refetch,
            }
        },
    }
</script>
```

在此示例中，`useQueryLite` 用于为 `userId` 发起用户配置文件的请求，并在请求成功时展示用户的 `profile`。通过 `raw`，您可以访问未处理的响应数据；`isPending` 表示请求的进行状态；`data` 提供了经过选择器处理后的数据；`refetch` 可以用于重新触发数据请求。
