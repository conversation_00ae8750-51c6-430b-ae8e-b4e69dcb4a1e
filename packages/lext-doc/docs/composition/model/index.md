# 层状态

本分类下的组合式函数用于处理层状态相关的逻辑。

## 简介

一个网页可以划分为两个部分，Layout 和 Model，Layout 是固有的一层页面结构，而 Model 则是一个可以自由动态增加或者减少的页面层。

Model 按位置关系分为，孤立 Model （如 dialog、drawer）和关系 Model（如 popover、dropdown、tooltip 等）

- 孤立 Model：位置关系完全独立于 Layout，布局一般相对于 Layout 的最外层元素，是比较独立、复杂，希望用户 focus 的操作流程（有遮罩层）
- 关系 Model：位置关系相对于某个 Layout 内部的元素，并且当 Layout 位置发生变化的时候，关系 Model 位置会跟随变化，逻辑计算相对复杂，一般是比较简单的内容展示或者操作，希望用户不离开当前流程就能达成操作，有较大可能会被其他层级的元素遮挡（没有遮罩层）

针对这些 Model 的特性，我设计了一套基于 vue provide/inject 的 Model 架构，设计该架构的目的有几个：

1. 集中控制各种 Model 的实例生命周期、开启关闭状态，减少无效的实例注册，提高性能
2. 提供一套 Model 之间的数据流转机制，便于单流程多 Model 的数据交互
3. 使用 HOC 统一 Model 的输入输出，降低 Model 的接入成本，提高开发效率
4. Model 实例状态可追溯，便于实现特定场景下的 Model 无操作打开或者关闭

## 概念

### `ModelProvider`

顶层数据提供组件，为内部包裹的全部组件提供整个 Model 层的数据接口

### `useModelProvider`

组合式函数，数据提升的同时，将返回的数据对象传入 `ModelProvider` 即可

### `useModel`

组合式函数，接收一个 Model 的配置，该 Model 会被动态注册到 `ModelProvider` ，返回该 Model 动态 `ModelProvider` 注入的附加逻辑数据接口

### `useModelClient`

获取整个 Model 层的数据接口，在确保某 Model 已经注入的情况下，可以直接调用该 Model 的相关能力
