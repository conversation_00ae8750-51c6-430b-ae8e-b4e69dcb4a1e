# useUrlState

`useUrlState` 是一个 Vue Composition API 钩子，旨在简化 URL 查询参数的处理。它允许开发者定义 URL 参数的默认值，并且以响应式的方式同步状态到 URL 的查询参数中。

## 功能

- 管理 URL 查询参数的状态。
- 自动处理查询参数的序列化和反序列化。
- 响应式更新 URL 查询参数。

## 使用方法

在组件的 `setup` 函数中使用 `useUrlState`：

```javascript
import { useUrlState } from '@crm/vueuse-pro'

export default {
    setup() {
        const defaultValues = { page: 1, size: 10 }
        const urlParams = useUrlState(defaultValues)

        return {
            urlParams,
        }
    },
}
```

## API

### `useUrlState<T extends Record<string, any>>(defaultValue?: T): UnwrapNestedRefs<T>`

- `defaultValue`: 默认值，建议与您要同步的 URL 查询参数结构相匹配的对象。

- 返回响应式的查询参数对象 `params`。

## 类型定义

### `T extends Record<string, any>`

这是一个 TypeScript 泛型类型，表示 `useUrlState` 钩子可以处理任何形状的对象作为 URL 查询参数。

## 示例

假设你正在构建一个分页功能，需要根据 URL 查询参数来显示当前页：

```html
<template>
    <div>
        <p>当前页：{{ urlParams.page }}</p>
        <!-- 其他内容 -->
    </div>
</template>

<script>
    import { useUrlState } from '@crm/vueuse-pro'

    export default {
        setup() {
            const defaultValues = { page: 1 }
            const urlParams = useUrlState(defaultValues)

            // 这里还可以根据 urlParams 的变化来执行其他逻辑，如发起 API 请求等

            return {
                urlParams,
            }
        },
    }
</script>
```

在上面的例子中，`useUrlState` 钩子将会监听 URL 查询参数的变化，并将其与 `urlParams` 保持同步。当 `urlParams` 发生变化时，它会自动更新 URL 查询参数。

请注意，通过 `useUrlState` 修改 `urlParams` 将导致路由变化，可能会触发与路由相关的其他副作用，如视图重新渲染、路由守卫等。
