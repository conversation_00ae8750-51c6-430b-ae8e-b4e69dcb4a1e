# useFormState

`useFormState` 是一个为表单设计的 Vue Composition API，它提供了一套完备的表单状态管理机制。你可以透过它来初始化数据、编辑、保存，以及处理远程数据的默认状态。

## 使用

```javascript
import { useFormState } from '@crm/vueuse-pro'

const { remoteDefaultState, initializingState, editingState, getSaveState, dataLoading, buildInitDataEffect, buildSaveDataEffect } = useFormState(
    fetchData,
    initializeData,
    mockOptions,
)
```

确保你提供了数据获取 (`fetchData`) 和初始化 (`initializeData`) 方法。

## API

### `useFormState(fetcher, initData, mockOptions)`

- `fetcher`: 异步获取数据的函数，必须返回一个 `Promise`。
- `initData`: 数据初始化函数，用于创建初始表单状态。
- `mockOptions`: 模拟数据的配置选项，可选。

返回一个对象，其中包括以下状态和函数：

- `remoteDefaultState`: 远程默认状态，为 `ShallowRef<T | null>` 类型。
- `initializingState`: 数据初始化状态，为 `ShallowRef<T>` 类型。
- `editingState`: 数据编辑状态，为 `Ref<UnwrapRef<T>>` 类型。
- `getSaveState`: 数据保存状态，为 `() => UnwrapRef<T>` 类型。
- `dataLoading`: 表示数据是否正在加载的状态，为 `Ref<boolean>` 类型。
- `buildInitDataEffect`: 创建数据初始化副作用的方法。
- `buildSaveDataEffect`: 创建数据保存副作用的方法。

## 类型定义

### `MockOptions`

```typescript
export interface MockOptions<T> {
    enabled?: MaybeRefOrGetter<boolean>
    mockData?: MaybeRefOrGetter<T>
}
```

### `FormState`

```typescript
export interface FormState<T> {
    // ...省略其他属性
    buildInitDataEffect: (effect: (initData: T, rawData: DeepReadonly<T> | null) => void, disabled?: MaybeRefOrGetter<boolean>) => void
    buildSaveDataEffect: (effect: (editingData: UnwrapRef<T>, rawData: DeepReadonly<T> | null) => void) => void
}
```

## 示例

以下是如何使用 `useFormState` 来管理一个表单状态的示例：

```html
<template>
    <!-- Your form template here -->
</template>

<script>
    import { useFormState } from '@crm/vueuse-pro'

    export default {
        setup() {
            // 通过 fetcher 函数异步获取数据，通过 initData 函数初始化数据
            // 可选地，您可以启用 mock 数据
            const {
                initializingState,
                remoteDefaultState,
                // ...其他状态
            } = useFormState(
                async () => {
                    /* ... 获取数据 ... */
                },
                (value) => {
                    /* ... 初始化数据 ... */
                },
                {
                    enabled: true,
                    mockData: {
                        /* ... 模拟数据 ... */
                    },
                },
            )

            // 使用 initializingState, editingState 等来构建你的表单

            return {
                // ... 返回状态
            }
        },
    }
</script>
```
