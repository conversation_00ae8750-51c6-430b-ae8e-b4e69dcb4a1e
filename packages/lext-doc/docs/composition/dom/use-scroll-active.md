# useScrollActive

`useScrollActive` 是一个 Vue Composition API 钩子，用于监听页面滚动位置并相应地返回页面当前的滚动状态。它支持自动判断页面是否位于顶部、底部、中间，或是无法滚动状态。

## 功能

- 监听页面滚动位置。
- 自动判断并返回页面的滚动状态：顶部、底部、中间或无法滚动。
- 使用 `MutationObserver` 监听 DOM 变化，以确保滚动状态的准确性。

## 使用方法

```javascript
import { useScrollActive } from '@crm/vueuse-pro'

export default {
    setup() {
        const offset = 100 // 可选。顶部偏移量，默认为 0
        const scrollPosition = useScrollActive(offset)

        return {
            scrollPosition,
        }
    },
}
```

在模板中，你可以根据 `scrollPosition` 的值来决定执行特定的行为或显示不同的内容：

```html
<template>
    <div :class="{ 'at-top': scrollPosition === 'top', 'at-bottom': scrollPosition === 'bottom' }">
        <!-- 页面内容 -->
    </div>
</template>
```

## API

### `useScrollActive(offset = 0): Ref<ScrollPostion>`

- `offset`: 顶部偏移量，默认为 `0`。当滚动位置距离顶部小于或等于这个偏移量时，滚动状态将被设置为 `top`。

- 返回一个响应式对象 `position`，其值为类型 `ScrollPostion` 的枚举，包括 `"top"`、`"bottom"`、`"center"` 或 `"none"`。

## 类型定义

### `ScrollPostion`

```typescript
export type ScrollPostion = 'top' | 'bottom' | 'center' | 'none'
```

`ScrollPostion` 定义了四种可能的页面滚动位置状态：

- `"top"`: 页面滚动到顶部。
- `"bottom"`: 页面滚动到底部。
- `"center"`: 页面处于滚动状态，但既不在顶部也不在底部。
- `"none"`: 页面内容不足以滚动。

## 示例

假设你的页面中有一个滚动容器 `.b-layout-is-main`，你想要在用户滚动时显示一个返回顶部的按钮：

```html
<template>
    <button v-if="scrollPosition === 'center' || scrollPosition === 'bottom'" @click="scrollToTop">返回顶部</button>
</template>

<script>
    import { useScrollActive } from '@crm/vueuse-pro'

    export default {
        setup() {
            const scrollPosition = useScrollActive()

            const scrollToTop = () => {
                const el = document.querySelector('.b-layout-is-main')
                if (el) {
                    el.scrollTo({ top: 0, behavior: 'smooth' })
                }
            }

            return {
                scrollPosition,
                scrollToTop,
            }
        },
    }
</script>
```

这个例子展示了如何使用 `useScrollActive` 来监听页面滚动，并在滚动到中间或底部时显示一个返回顶部的按钮。
