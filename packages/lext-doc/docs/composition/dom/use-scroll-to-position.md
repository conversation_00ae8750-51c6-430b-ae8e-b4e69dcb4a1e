# useScrollToPosition

`useScrollToPosition` 是一个 Vue Composition API 钩子，它提供了一个简洁的方法来滚动到容器的指定位置，无论是顶部还是底部。

## 使用方法

```javascript
import { useScrollToPosition } from '@crm/vueuse-pro'

export default {
    setup() {
        const container = ref(null)
        const { scroll } = useScrollToPosition(container, 'top')

        return {
            scroll,
            container,
        }
    },
}
```

在模板中，假设你有一个可以引用的滚动容器，并想提供一个按钮来触发滚动：

```html
<template>
    <div ref="container" class="scroll-container">
        <!-- Container content -->
    </div>
    <button @click="scroll">滚动到顶部</button>
</template>
```

## API

### `useScrollToPosition(container, position = 'bottom'): ScrollToPosition`

- `container`: 容器的引用 (`Ref<ComponentPublicInstance | undefined>`)。
- `position`: 滚动位置，可选 `'top'` 或 `'bottom'`，默认为 `'bottom'`。

- 返回一个对象，其中包含 `scroll` 方法。

## 类型定义

### `ScrollPosition`

```typescript
export type ScrollPosition = 'top' | 'bottom'
```

`ScrollPosition` 是一个类型别名，定义了两种滚动位置：

- `"top"`: 滚动到容器顶部。
- `"bottom"`: 滚动到容器底部。

### `ScrollToPosition`

```typescript
export interface ScrollToPosition {
    scroll: () => Promise<void>
}
```

`ScrollToPosition` 接口中，`scroll` 是一个异步函数，调用时将执行滚动操作。

## 示例

假设你有一个长列表，并需要提供用户在这个列表中快速导航的功能：

```html
<template>
    <div ref="container" class="list-container">
        <!-- Long list content -->
    </div>
    <button @click="scrollToTop">滚动到顶部</button>
    <button @click="scrollToBottom">滚动到底部</button>
</template>

<script>
    import { ref } from 'vue'
    import { useScrollToPosition } from '@crm/vueuse-pro'

    export default {
        setup() {
            const container = ref(null)
            const { scroll: scrollToTop } = useScrollToPosition(container, 'top')
            const { scroll: scrollToBottom } = useScrollToPosition(container, 'bottom')

            return {
                container,
                scrollToTop,
                scrollToBottom,
            }
        },
    }
</script>
```

这个例子展示了如何使用 `useScrollToPosition` 钩子来实现快速滚动到列表的顶部和底部的功能。
