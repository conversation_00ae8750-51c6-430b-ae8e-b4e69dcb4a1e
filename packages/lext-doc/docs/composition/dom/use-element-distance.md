# useElementDistance

`useElementDistance` 是一个用于计算两个元素之间的距离（相对特定容器）的 Vue Composition API 函数。此函数特别适用于需要根据元素位置动态调整样式或执行逻辑的场景。

## 使用

```html
<div ref="containerRef" class="container">
    <div ref="currentRef" class="target">目标元素</div>
</div>
```

```ts
import { useElementDistance } from '@crm/vueuse-pro'
import { ref } from 'vue'

export default {
    setup() {
        const containerRef = ref(null)
        const currentRef = ref(null)
        const distances = useElementDistance(containerRef, currentRef)

        return { containerRef, currentRef, distances }
    },
}
```

## API

```ts
interface ElementDistance {
    /**
     * 元素距离容器顶部的距离
     */
    top: number
    /**
     * 元素距离容器左侧的距离
     */
    left: number
}
function useElementDistance(containerRef: Ref<HTMLElement | undefined>, currentRef: Ref<HTMLElement | undefined>, scrollListen = true): Ref<ElementDistance>
```
