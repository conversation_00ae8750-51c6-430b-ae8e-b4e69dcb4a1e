# useFocus

`useFocus` 用于监听元素的 `focus` 和 `blur` 事件，以及追踪元素的聚焦状态。

## 使用

在这个示例中，`useFocus` 被用于监听输入框的聚焦状态。当输入框被聚焦时，`isFocused` 的值将变为 `true`，从而显示提示信息。

```html
<template>
    <input ref="inputRef" placeholder="聚焦我试试！" />
    <p v-if="isFocused">看，我被聚焦了！</p>
</template>

<script>
    import { ref } from 'vue'
    import useFocus from '@crm/vueuse-pro'

    export default {
        setup() {
            const inputRef = ref(null)
            const { isFocused } = useFocus(inputRef)

            return {
                inputRef,
                isFocused,
            }
        },
    }
</script>
```

## API

```ts
// 返回一个 `Ref<boolean>` 对象，表示元素是否处于聚焦状态。
export function useFocus(element: Ref<HTMLElement | undefined>): UseFocusReturnType
```
