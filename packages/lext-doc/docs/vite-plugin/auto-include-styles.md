# autoIncludeStyles

`auto-include-styles` 是一个 Vite 插件，用于自动查找并包含项目中的样式文件。当使用 Vite 开发服务器时，该插件会递归地搜索指定目录下的所有样式文件，并将它们加入到 Vite 优化依赖（`optimizeDeps`）的配置中。

## 使用方法

设想你有一个项目，其中包含多个组件或页面的样式文件，它们存储在 `src/styles` 目录中。你可以如下配置该插件：

在你的 Vite 配置文件中（如 `vite.config.ts` 或 `vite.config.js`），导入并使用 `auto-include-styles` 插件：

```javascript
// vite.config.js
import { defineConfig } from 'vite'
import { autoIncludeStyles } from './plugins/autoIncludeStyles'

export default defineConfig({
    plugins: [
        autoIncludeStyles({
            baseDir: 'src/styles',
        }),
    ],
})
```

使用上面的配置，`auto-include-styles` 插件将自动查找 `src/styles` 目录下所有的样式文件，并确保 Vite 在开发服务器启动时能够识别并预构建它们。

确保你提供了 `baseDir` 选项，这是你的样式文件所在的目录相对于项目根目录的路径。

## API

### `autoIncludeStyles(options: AutoIncludeStylesOptions): Plugin`

#### 参数

- `options`: 配置对象，包含以下属性：
    - `baseDir`: 你的样式文件所在目录的相对路径。

#### 插件行为

- 插件名：`'auto-include-styles'`
- 在 `serve` 命令下，插件会递归查找在 `baseDir` 目录下的所有以 `style.js` 结尾的文件，并将它们添加到优化依赖中。

## 类型定义

### `AutoIncludeStylesOptions`

```typescript
interface AutoIncludeStylesOptions {
    baseDir: string
}
```
