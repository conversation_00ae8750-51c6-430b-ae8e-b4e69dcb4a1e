---
# https://vitepress.dev/reference/default-theme-home-page
layout: home

hero:
    name: 'Nexus Toolkit'
    text: Vue 3 开发套件
    tagline: 快速落地最佳实践
    image:
        src: /n2.png
        alt: Nexus Logo
    actions:
        - theme: brand
          text: 快速开始
          link: /begin
        - theme: alt
          text: API 文档
          link: /composition

features:
    - icon: 🚀
      title: Vue 3.4 + Vite 5
      details: 紧跟最新技术栈，应用最佳实践
    - icon: 🛠
      title: 全面使用 TypeScript 5
      details: 自文档化，代码更加健壮，开发更加高效
    - icon: 😆
      title: 优雅的设计，易用的 API
      details: 参考了大量开源项目，弃其糟粕，取其精华
    - icon: 📚
      title: 详尽的文档，丰富的示例
      details: 新人 10 分钟快速上手开发
---
