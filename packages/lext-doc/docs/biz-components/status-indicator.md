# StatusIndicator

开发中...

<!-- ## 示例

<ClientOnly>
<div style="display: flex; gap: 4px;">
    <StatusIndicator :status="1" :config="statusConfig"/>
    <StatusIndicator :status="2" :config="statusConfig"/>
    <StatusIndicator :status="3" :config="statusConfig"/>
</div>

<script setup>
import { defineAsyncComponent } from 'vue';
import { ref } from "vue";

const statusConfig = ref({
    1: { bgColor: 'var(--status-warning-bg-color)', pointColor: 'var(--status-warning-primary-color)', text: '未开放' },
    2: { bgColor: 'var(--status-success-bg-color)', pointColor: 'var(--status-success-primary-color)', text: '开放中' },
    3: { bgColor: 'var(--status-disable-bg-color)', pointColor: 'var(--status-disable-primary-color)', text: '已关闭' }
});

const StatusIndicator = defineAsyncComponent(() => import('@crm/nexus-biz-component').then((module) => module.StatusIndicator))

</script>
</ClientOnly>

<style scoped>
</style>

```vue
<div style="display: flex; gap: 4px;">
    <StatusIndicator :status="1" :config="statusConfig"/>
    <StatusIndicator :status="2" :config="statusConfig"/>
    <StatusIndicator :status="3" :config="statusConfig"/>
</div>

<script setup>
import { StatusIndicator } from '@crm/nexus-biz-component'

import { ref } from 'vue'

const statusConfig = ref({
  1: { bgColor: 'var(--status-warning-bg-color)', pointColor: 'var(--status-warning-primary-color)', text: '未开放' },
  2: { bgColor: 'var(--status-success-bg-color)', pointColor: 'var(--status-success-primary-color)', text: '开放中' },
  3: { bgColor: 'var(--status-disable-bg-color)', pointColor: 'var(--status-disable-primary-color)', text: '已关闭' },
})
</script>
``` -->
