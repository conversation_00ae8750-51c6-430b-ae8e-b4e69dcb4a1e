// import { NoticeConfig, IEmailTemplateParams } from "@crm/nexus-biz-component";

// export const config: NoticeConfig = {
//     email: {
//         toolbar: 'forecolor italic bold underline strikethrough',
//         label: '邮件通知',
//         titleMaxLength: 50,
//         defaultContent: {
//             default: `<input class="mce-insert-params" type="button" value="【姓名】" />同学您好：<br />
//           感谢您参加，今年的校园招聘笔试，很荣幸的通知您，您的简历筛选已经通过，现邀请您进入我们的笔试阶段，线上笔试时间为：<input class="mce-insert-params" type="button" value="【开始时间】" />-<input class="mce-insert-params" type="button" value="【结束时间】" />。<br />
//           为方便您参加测试，且综合考虑疫情防控等因素，此次笔试采取线上的形式进行，考试前请点击“试测”进行网络环境测试，正式考试时请您在规定的时间内完成笔试！<br />`,
//             anti: `<input class="mce-insert-params" type="button" value="【姓名】" />你好，<br />
//           感谢投递，你的简历已通过筛选，现邀请你进入笔试环节，线上笔试时间为：<input class="mce-insert-params" type="button" value="【开始作答时间】" />-<input class="mce-insert-params" type="button" value="【试卷关闭时间】" />。<br />
//           为了确保笔试顺利进行，请你在<input class="mce-insert-params" type="button" value="【调试设备区间】" />期间完成设备调试，按照要求和指引检查你的设备是否能够流畅使用。时间有限，请务必重视并按规定操作，以避免不必要的设备问题影响正式笔试。感谢你的配合，祝笔试顺利！`
//         },
//         maxLength: 5000,
//         info: `作答说明：
//           一、关于网络
//           请确保您的网络稳定，最好有固定WiFi网络，或固定4G/5G网络。
//           二、关于时间
//           考试过程中，系统将自动计时，请您在规定时间完成作答，时间一到，系统将自动交卷。
//           三、关于作答
//           若考试中断或网络异常退出。可用原账号继续登录考试。若其他问题，请咨询本次考试客服。
//           四、关于诚信
//           测试过程中监考系统将自动开启，请您选择安静环境独立完成答题，全程开启摄像头和音频，避免出现其他声音。
//           请您考前关闭电脑内可能弹窗的软件，考试过程中严禁切换屏幕，否则系统将强制交卷。
//           五、关于隐私
//           考生个人信息、IP地址等信息，未经考生本人同意，不向任何第三方透漏。
//           若作答中遇到问题，请致电**** ，客服只解答流程问题，不解答笔试、面试、简历、应聘结果等其他相关问题！`,
//         contentStyle: `body {margin: 0} .mce-content-body[data-mce-placeholder]:not(.mce-visualblocks)::before {color: #B3B3B3} .mce-offscreen-selection{display:none}p{margin:0}.mce-insert-params{border: none;background: transparent;padding: 0 2px;font-size: 14px;outline: none;color: #00A6A7} ::-webkit-scrollbar{
//               width:10px;
//               height:10px;
//           }
//           ::-webkit-scrollbar-track{
//               background: transparent;
//           }
//           ::-webkit-scrollbar-thumb{
//               background: #d3d8e6;
//               border-radius: 8px;
//           }`,
//         params: ['【姓名】', '【开始时间】', '【结束时间】', '【邮箱】'],
//         genTemplate: ({ emailTemplate, emailRemarks, examUrl = '', emailSubject, anti }: IEmailTemplateParams, preview?: boolean) => {
//             let text = `<!doctype html>
//             <html lang="und" dir="auto" xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
//             <head>
//             <title></title>
//             <!--[if !mso]><!-->
//             <meta http-equiv="X-UA-Compatible" content="IE=edge">
//             <!--<![endif]-->
//             <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
//             <meta name="viewport" content="width=device-width, initial-scale=1">
//             <style type="text/css">
//             #outlook a{padding:0;}body{margin:0;padding:0;-webkit-text-size-adjust:100%;-ms-text-size-adjust:100%;}table,td{border-collapse:collapse;mso-table-lspace:0pt;mso-table-rspace:0pt;}img{border:0;height:auto;line-height:100%;outline:none;text-decoration:none;-ms-interpolation-mode:bicubic;}p{display:block;margin:13px 0;}
//             </style>
//             <!--[if mso]> <noscript><xml><o:OfficeDocumentSettings><o:AllowPNG/><o:PixelsPerInch>96</o:PixelsPerInch></o:OfficeDocumentSettings></xml></noscript>
//             <![endif]-->
//             <!--[if lte mso 11]>
//             <style type="text/css">
//             .mj-outlook-group-fix{width:100% !important;}
//             </style>
//             <![endif]-->
//             <style type="text/css">
//             @media only screen and (min-width:480px){.mj-column-per-100{width:100%!important;max-width:100%;}}
//             </style>
//             <style media="screen and (min-width:480px)">.moz-text-html .mj-column-per-100{width:100%!important;max-width:100%;}
//             </style>
//             <style type="text/css">
//             </style>
//             <style type="text/css">
//             </style>
//             </head>
//             <body style="word-spacing:normal;"><div lang="und" dir="auto" style=""><!-- Content -->
//             <!--[if mso | IE]>
//             <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:672px;" width="672"><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
//             <![endif]--><div style="margin:0px auto;max-width:672px;">
//             <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;"><tbody><tr><td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
//             <!--[if mso | IE]>
//             <table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:672px;">
//             <![endif]--><div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
//             <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%"><tbody><tr><td style="vertical-align:top;padding:0 0;">
//             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="" width="100%"><tbody><tr><td align="left" style="font-size:0px;padding:0 0;word-break:break-word;"><div style="font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:14px;line-height:22px;text-align:left;color:#1F1F1F;"><mj-raw>
//             <style>.mce-insert-params{border:none;background:transparent;padding:0 2px;font-size:14px;outline:none;color:var(--primary-color-6);}
//             </style> &#x24;{emailTemplate} </mj-raw></div>
//             </td></tr><tr><td style="font-size:0px;word-break:break-word;"><div style="height:20px;line-height:20px;">&#8202;</div>
//             </td></tr><tr><td align="left" style="font-size:0px;padding:0 0;word-break:break-word;">
//             <table role="presentation" cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;"><tr style="padding-left:20px"><td style="width:43px;padding:0px;text-align:center;"> <a data-preview-disabled target="_blank" href="$$acceptUrl$$" style="cursor:pointer;text-decoration:none;display:inline-block;width:120px;height:46px;background:#00A6A7;border-radius:23px;font-size:16px;border:none;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#FFFFFF;line-height:46px;">&#x63a5;&#x53d7;&#x9080;&#x8bf7;</a>
//             </td><td style="width:43px;padding-left:19px;text-align:center;"> <a data-preview-disabled target="_blank" href="$$rejectUrl$$" style="cursor:pointer;text-decoration:none;display:inline-block;width:120px;height:46px;background:#FFFFFF;border-radius:23px;border:1px solid #DFDFDF;font-size:16px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#1F1F1F;line-height:46px;">&#x62d2;&#x7edd;&#x9080;&#x8bf7;</a>
//             </td><td>
//             </td></tr></table>
//             </td></tr><tr><td style="font-size:0px;word-break:break-word;"><div style="height:6px;line-height:6px;">&#8202;</div>
//             </td></tr></tbody></table>
//             </td></tr></tbody></table></div>
//             <!--[if mso | IE]>
//             </td></tr></table>
//             <![endif]-->
//             </td></tr></tbody></table></div>
//             <!--[if mso | IE]>
//             </td></tr></table>
//             <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:672px;" width="672" bgcolor="#F6F6F6"><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
//             <![endif]--><div style="background:#F6F6F6;background-color:#F6F6F6;margin:0px auto;border-radius:8px;max-width:672px;">
//             <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:#F6F6F6;background-color:#F6F6F6;width:100%;border-radius:8px;"><tbody><tr><td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
//             <!--[if mso | IE]>
//             <table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:672px;">
//             <![endif]--><div class="mj-column-per-100 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
//             <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%"><tbody><tr><td align="center" style="font-size:0px;padding:10px 25px;padding-top:12px;padding-bottom:24px;word-break:break-word;"><div style="font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:24px;font-weight:500;line-height:1;text-align:center;color:#1F1F1F;">&#x24;{emailSubject}</div>
//             </td></tr>
//             <table role="presentation" style="border-collapse:separate;padding:0 20px"><tr style="padding-left:20px;"><td data-changeable style="width:43px;padding:10px 0;text-align:center;"><div style="font-size:14px;margin:0 auto;border:1px solid #DFDFDF;border-radius:14px;text-align:center;line-height:28px;width:28px;height:28px;background:#FFFFFF;"> 01 </div><div style="margin:0 auto;width:0px;height:91px;border:1px dashed #DFDFDF;"></div><div style="font-size:14px;margin:0 auto;border:1px solid #DFDFDF;border-radius:14px;text-align:center;line-height:28px;width:28px;height:28px;background:#FFFFFF;"> 02 </div>
//             </td><td>
//             <table role="presentation" style="border-collapse:separate;padding:0"><tr data-changeable><td style="vertical-align:middle;box-sizing:content-box;padding:19px 20px;border:1px solid #DFDFDF;border-radius:12px;height:50px;background:#FFFFFF;"><div>
//             <table role="presentation"><tr><td style="vertical-align:middle;width:48px"><div> <img alt="" style="width:48px;height:48px;" src="https://img.bosszhipin.com/static/file/2024/5buhksfxkd1706585643677.svg"/></div>
//             </td><td style="width:14px">
//             </td><td style="vertical-align:middle;width:385px"><div><div style="font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#1F1F1F;line-height:20px;"> &#x8003;&#x524d;&#x8bbe;&#x5907;&#x8c03;&#x8bd5; </div><div style="height:8px"></div><div style="font-size:12px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#4D4D4D;line-height:18px;"> &#x4e3a;&#x907f;&#x514d;&#x6b63;&#x5f0f;&#x8003;&#x8bd5;&#x65f6;&#x51fa;&#x73b0;&#x8bbe;&#x5907;&#x6545;&#x969c;&#x7b49;&#x95ee;&#x9898;&#xff0c;&#x5f71;&#x54cd;&#x8003;&#x8bd5;&#x7ed3;&#x679c;&#xff0c;&#x8bf7;&#x63d0;&#x524d;&#x8fdb;&#x884c;&#x8bbe;&#x5907;&#x8c03;&#x8bd5; </div></div>
//             </td><td style="width:14px">
//             </td><td> <a target="_blank" href="${examUrl}" style="cursor:pointer;text-decoration:none;text-align:center;line-height:32px;display:inline-block;width:99px;height:32px;border-radius:17px;border:1px solid #00A6A7;background:#FFFFFF;color:#00A6A7;font-size:14px;">&#x70b9;&#x51fb;&#x8c03;&#x8bd5; <img alt="" width="10px" src="https://img.bosszhipin.com/static/file/2024/d27y1l1uba1706585643475.svg"/></a>
//             </td></tr></table></div>
//             </td></tr><tr data-changeable style="height:20px"></tr><tr><td style="vertical-align:middle;box-sizing:content-box;padding:24px 20px;border:1px solid #DFDFDF;border-radius:12px;height:50px;background:#FFFFFF;"><div>
//             <table role="presentation"><tr><td style="vertical-align:middle;width:48px"><div> <img alt="" style="width:48px;height:48px;" src="https://img.bosszhipin.com/static/file/2024/iglnkpi4ng1706585643850.svg"/></div>
//             </td><td style="width:14px">
//             </td><td style="vertical-align:middle;width:385px"><div><div style="font-size:14px;font-family:PingFangSC-Medium,PingFang SC;font-weight:500;color:#1F1F1F;line-height:20px;"> &#x6b63;&#x5f0f;&#x7b54;&#x9898;&#x5165;&#x53e3; </div><div style="height:8px"></div><div style="font-size:12px;font-family:PingFangSC-Regular,PingFang SC;font-weight:400;color:#4D4D4D;line-height:18px;"> &#x70b9;&#x51fb;&#x6b64;&#x5904;&#x6b63;&#x5f0f;&#x5f00;&#x59cb;&#x7b54;&#x9898; </div></div>
//             </td><td style="width:14px">
//             </td><td> <a target="_blank" href="${examUrl}" style="cursor:pointer;text-decoration:none;text-align:center;line-height:32px;display:inline-block;width:99px;height:32px;border-radius:17px;border:1px solid #00A6A7;background:#FFFFFF;color:#00A6A7;font-size:14px;">&#x7acb;&#x5373;&#x7b54;&#x9898; <img alt="" width="10px" src="https://img.bosszhipin.com/static/file/2024/d27y1l1uba1706585643475.svg"/></a>
//             </td></tr></table></div>
//             </td></tr></table>
//             </td></tr></table><tr><td align="center" style="font-size:0px;padding:10px 25px;word-break:break-word;"><p style="border-top:solid 1px #DFDFDF;font-size:1px;margin:0px auto;width:100%;"></p>
//             <!--[if mso | IE]>
//             <table align="center" border="0" cellpadding="0" cellspacing="0" style="border-top:solid 1px #DFDFDF;font-size:1px;margin:0px auto;width:622px;" role="presentation" width="622px"><tr><td style="height:0;line-height:0;"> &nbsp;
//             </td></tr></table>
//             <![endif]-->
//             </td></tr><tr><td align="left" style="font-size:0px;padding:10px 25px;word-break:break-word;"><div style="font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:14px;font-weight:400;line-height:22px;text-align:left;color:#4D4D4D;">&#xb7;&#x8bf7;&#x4f7f;&#x7528;72&#x7248;&#x672c;&#x6216;&#x4ee5;&#x4e0a;&#x7248;&#x672c;&#x7684;chrome&#x6d4f;&#x89c8;&#x5668;<br> &#xb7;&#x8bf7;&#x4fdd;&#x8bc1;&#x7f51;&#x7edc;&#x901f;&#x5ea6;&#x5728;300K/S&#x6216;&#x4ee5;&#x4e0a;<br> &#xb7;&#x5168;&#x7a0b;&#x8bf7;&#x5728;&#x5b89;&#x9759;&#x3001;&#x72ec;&#x5904;&#x7684;&#x73af;&#x5883;&#x4e0b;&#x5b8c;&#x6210;&#x4f5c;&#x7b54;</div>
//             </td></tr><tr><td style="font-size:0px;word-break:break-word;"><div style="height:9px;line-height:9px;">&#8202;</div>
//             </td></tr><tr><td align="left" style="font-size:0px;padding:10px 25px;padding-bottom:0px;word-break:break-word;"><div style="font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:16px;font-weight:500;line-height:24px;text-align:left;color:#1F1F1F;">&#x4f5c;&#x7b54;&#x8bf4;&#x660e;</div>
//             </td></tr><tr><td align="left" style="font-size:0px;padding:10px 25px;padding-top:8px;word-break:break-word;"><div style="font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:14px;font-weight:400;line-height:22px;text-align:left;color:#4D4D4D;">&#x24;{emailRemarks}</div>
//             </td></tr><tr><td style="font-size:0px;word-break:break-word;"><div style="height:9px;line-height:9px;">&#8202;</div>
//             </td></tr></tbody></table></div>
//             <!--[if mso | IE]>
//             </td></tr></table>
//             <![endif]-->
//             </td></tr></tbody></table></div>
//             <!--[if mso | IE]>
//             </td></tr></table>
//             <table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:672px;" width="672"><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;">
//             <![endif]--><div style="margin:0px auto;max-width:672px;">
//             <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="width:100%;"><tbody><tr><td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
//             <!--[if mso | IE]>
//             <table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr></tr></table>
//             <![endif]-->
//             </td></tr></tbody></table></div>
//             <!--[if mso | IE]>
//             </td></tr></table>
//             <![endif]--></div>
//             </body>
//             </html>`;
//             text = text.replace(/&#x24;{emailTemplate}/g, emailTemplate.replace(/\n/g, '<br/>') || (preview ? '邮件正文未填写' : ''));
//             text = text.replace(/&#x24;{emailRemarks}/g, emailRemarks.replace(/\n/g, '<br/>'));
//             text = text.replace(/&#x24;{emailSubject}/g, emailSubject.replace(/\n/g, '<br/>') || (preview ? '邮件标题未填写' : ''));

//             const parser = new DOMParser();
//             const doc = parser.parseFromString(text, 'text/html');

//             if (!anti) {
//                 // 获取所有带有"data-changeable"属性的元素
//                 const changeableElements = doc.querySelectorAll('[data-changeable]');

//                 // 删除这些元素
//                 changeableElements.forEach(element => {
//                     element.remove();
//                 });
//             }

//             if (preview) {
//                 // 获取所有带有"data-preview-disabled"属性的链接
//                 const disabledPreviewLinks = doc.querySelectorAll('a[data-preview-disabled]');

//                 // 移除这些链接的href属性
//                 disabledPreviewLinks.forEach(link => {
//                     link.removeAttribute('href');
//                 });
//             }

//             // 返回处理后的完整HTML字符串
//             text = doc.documentElement.outerHTML;

//             return text;
//         },
//         getAllParamsCount: (v: string) => {
//             const reg = /<input class="mce-insert-params" type="button" value="(.*?)" \/>/g;
//             let totalCount = 0;
//             (v || '').replace(reg, (s, $1) => {
//                 totalCount += $1?.length || 0;
//                 return $1;
//             });
//             return totalCount;
//         }
//     },
//     sms: {
//         label: '短信通知',
//         titleMaxLength: 20,
//         maxLength: 300,
//         defaultContent: {
//             default: {
//                 value: `线上笔试时间为<hr params="【开始时间】">-<hr params="【结束时间】">，具体笔试信息已发送到您的邮箱<hr params="【邮箱】">（开考前请点击邮箱中的试测链接进行试测），如确认参加请点击邮箱链接参加考试，祝您笔试顺利！`,
//                 // 短信不可修改主题（用于传给后端）
//                 subject: '【BOSS直聘】<hr params="【姓名】">您好，<hr params="【公司名称】">诚邀您参加笔试，',
//                 // 短信不可修改主题（用户前端渲染，⚠️如果修改文案内容，两个字段得同步处理）
//                 modifiableContent: ''
//             },
//             anti: {
//                 value: `<input class="mce-insert-params" type="button" value="【姓名】" />你好，<br />
//               感谢投递，你的简历已通过筛选，现邀请你进入笔试环节，线上笔试时间为：<input class="mce-insert-params" type="button" value="【开始作答时间】" />-<input class="mce-insert-params" type="button" value="【试卷关闭时间】" />。<br />
//               为了确保笔试顺利进行，请你在<input class="mce-insert-params" type="button" value="【调试设备区间】" />期间完成设备调试，按照要求和指引检查你的设备是否能够流畅使用。时间有限，请务必重视并按规定操作，以避免不必要的设备问题影响正式笔试。感谢你的配合，祝笔试顺利！`,
//                 subject: '【BOSS直聘】<hr params="【姓名】">您好，<hr params="【公司名称】">诚邀您参加笔试，',
//                 modifiableContent: ''
//             }
//         },
//         params: ['【姓名】', '【开始时间】', '【结束时间】'],
//         getContentText: (content: string) => {
//             const reg = /<hr params="(.*?)">/g;
//             return content.replace(reg, (s, $1) => {
//                 return $1;
//             });
//         }
//     }
// };
