# ExamNoticeDialog

开发中...

## 示例

<!-- <button @click="handleOpened">打开弹窗</button>

<script setup>
import { ExamNoticeDialog } from '@crm/nexus-biz-component';
import { config } from "./config.ts";
import { ref } from "vue";

const opened = ref(false);

const selectConfig = ref({
  // 全选带的查询参数
  filters: {
    keyword: "",
    noticeStatus: 0, // 通知状态 0未通知 1已通知
    examStatus: 0, // 考试状态 0未参加 1未完成 2已完成
    debugDeviceTypes: "4,6",
  },
  // 加密考生id
  keysSelected: ["asdbasjdhjs1osd1hio1"],
  isCheckTotalAll: false,
  keysExcludes: [],
});

const handleOpened = () => (opened.value = !opened.value);
</script>

<ExamNoticeDialog v-if="opened"> </ExamNoticeDialog> -->

<!-- <ExamNoticeDialog
v-if="opened"
v-model="opened"
:config="config"
:encryptExamId="111"
:selectConfig="selectConfig"
:apis="{}"

> </ExamNoticeDialog> -->

<!-- <script setup>
import { defineAsyncComponent } from 'vue';
import { config } from "./config.ts";
import { ref } from "vue";

const opened = ref(false);

const selectConfig = ref({
  // 全选带的查询参数
  filters: {
    keyword: "",
    noticeStatus: 0, // 通知状态 0未通知 1已通知
    examStatus: 0, // 考试状态 0未参加 1未完成 2已完成
    debugDeviceTypes: "4,6",
  },
  // 加密考生id
  keysSelected: ["asdbasjdhjs1osd1hio1"],
  isCheckTotalAll: false,
  keysExcludes: [],
});

const handleOpened = () => (opened.value = !opened.value);

</script> -->

```vue
<button @click="handleOpened">
打开弹窗
</button>

<ExamNoticeDialog v-if="opened" v-model="opened" :config="config" :encryptExamId="111" :selectConfig="selectConfig" :apis="{}">
</ExamNoticeDialog>

<script setup>
import { ExamNoticeDialog } from '@crm/nexus-biz-component'
import { ref } from 'vue'
import { config } from './config.ts'

const opened = ref(false)

const selectConfig = ref({
    // 全选带的查询参数
    filters: {
        keyword: '',
        noticeStatus: 0, // 通知状态 0未通知 1已通知
        examStatus: 0, // 考试状态 0未参加 1未完成 2已完成
        debugDeviceTypes: '4,6',
    },
    // 加密考生id
    keysSelected: ['asdbasjdhjs1osd1hio1'],
    isCheckTotalAll: false,
    keysExcludes: [],
})

const handleOpened = () => (opened.value = !opened.value)
</script>
```
