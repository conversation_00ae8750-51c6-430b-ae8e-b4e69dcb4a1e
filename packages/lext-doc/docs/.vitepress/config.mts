import { fileURLToPath, URL } from 'node:url'
import { defineConfig } from 'vitepress'
import { pagefindPlugin } from 'vitepress-plugin-pagefind'
import { generateSidebar } from 'vitepress-sidebar'
// import components from "unplugin-vue-components/vite"; // 自动导入组件
// import autoImport from "unplugin-auto-import/vite"; // 自动导入组件
// import { BossDesignResolver } from "@boss/design-resolver";

const vitepressSidebarOptions = {
    /* Options... */
    documentRootPath: '/docs',
    useTitleFromFileHeading: true,
    useFolderTitleFromIndexFile: true,
    useFolderLinkFromIndexFile: true,
    sortMenusOrderByDescending: true,
    sortMenusByFrontmatterOrder: true,
    hyphenToSpace: true,
    collapsed: false,
}

// https://vitepress.dev/reference/site-config
export default defineConfig({
    title: 'Nexus Toolkit',
    description: 'Vue 3 开发套件',
    head: [['link', { rel: 'icon', href: '/icon.png' }]],
    lastUpdated: true,
    vite: {
        plugins: [pagefindPlugin()],
        server: {
            port: 8888,
        },
        resolve: {
            alias: {
                '@': fileURLToPath(new URL('../../packages', import.meta.url)),
            },
        },
    },
    vue: {
        // @vitejs/plugin-vue options
    },
    themeConfig: {
        logo: '/icon.png',
        // https://vitepress.dev/reference/default-theme-config
        nav: [
            { text: 'Home', link: '/' },
            { text: '文档', link: '/begin' },
            {
                text: '1.1.x',
                items: [
                    {
                        // 也可以省略标题
                        items: [
                            { text: '更新日志', link: 'https://git.kanzhun-inc.com/fe/fe-crm-library-nexus/-/blob/master/CHANGELOG.md' },
                            { text: '参与贡献', link: 'https://git.kanzhun-inc.com/fe/fe-crm-library-nexus' },
                        ],
                    },
                ],
            },
            {
                text: '友情链接',
                items: [
                    {
                        // 也可以省略标题
                        items: [
                            { text: 'BOSS Design', link: 'https://boss-design.weizhipin.com/zh-CN/home' },
                            { text: 'Vue Use', link: 'https://vueuse.org/' },
                        ],
                    },
                ],
            },
        ],

        sidebar: generateSidebar(vitepressSidebarOptions),

        socialLinks: [{ icon: 'github', link: 'https://git.kanzhun-inc.com/fe/fe-crm-library-nexus' }],

        footer: {
            message: 'Contact: <EMAIL> 📧',
            copyright: 'Powered by <a href="https://vitepress.dev/">VitePress</a> 🚀',
        },
    },
})
