<template>
    <b-layout type="content">
        <b-layout direction="vertical" class="box">
            <b-section type="search">
                <b-form ref="formRef" labelAlign="inner" :model="queryData" useGrid :gridProps="{ gap: 15 }" layout="inline" size="large" @submit="onSearch">
                    <b-form-item label="姓名" field="name">
                        <b-input v-model.trim="queryData.name" placeholder="请输入" :maxLength="20" allowClear @enter="onSearch" />
                    </b-form-item>
                    <b-form-item label="手机号" field="mobile">
                        <b-input v-model.trim="queryData.mobile" v-customTxt="{ reg: '\d' }" placeholder="请输入" :maxLength="11" allowClear @enter="onSearch" />
                    </b-form-item>
                    <b-form-item label="账号类型" field="accountType">
                        <b-select v-model="queryData.accountType">
                            <b-option v-for="item of accountTypeList" :key="item.value" :value="item.value" :label="item.label" />
                        </b-select>
                    </b-form-item>
                    <b-form-item label="" field="scoreInterval">
                        <ScoreInterval v-model="queryData.examineeNum" label="管理考生人数" :placeholder="['请输入', '请输入']" />
                    </b-form-item>
                    <b-form-item label="强制交卷" field="forceCommitAuth">
                        <b-select v-model="queryData.forceCommitAuth">
                            <b-option v-for="item of forceCommitAuthList" :key="item.value" :value="item.value" :label="item.label" />
                        </b-select>
                    </b-form-item>
                    <b-form-item label="发布公告" field="groupAnnounceAuth">
                        <b-select v-model="queryData.groupAnnounceAuth">
                            <b-option v-for="item of groupAnnounceAuthList" :key="item.value" :value="item.value" :label="item.label" />
                        </b-select>
                    </b-form-item>
                    <b-form-item label="巡考官" field="inspector">
                        <b-select v-model="queryData.inspector">
                            <b-option v-for="item of inspectorStatusList" :key="item.value" :value="item.value" :label="item.label" />
                        </b-select>
                    </b-form-item>
                    <b-form-item :alwaysShow="true" :showMore="true">
                        <b-space>
                            <b-button type="outline" status="primary" htmlType="submit" size="large"> 查询 </b-button>
                        </b-space>
                    </b-form-item>
                </b-form>
            </b-section>
            <b-section justify="between" align="center">
                <div class="title-group">
                    <b-title size="small"> 监考者列表 </b-title>
                    <span v-if="allocateExamineeStatus" class="btn-checked-count"
                        >已选 <span style="color: #d27400">{{ allocateExamineeCount }}</span
                        >人</span
                    >
                    <b-action size="small" @click.prevent="onAllocateExamineeStatus(!allocateExamineeStatus)">
                        {{ !allocateExamineeStatus ? '全部选中' : '撤销全选' }}
                    </b-action>
                </div>
                <div class="btn-group">
                    <b-button type="primary" size="large" :disabled="isExamStart" @click="onShowAssign"> 分配考生 </b-button>
                    <b-button status="primary" type="outline" size="large" :disabled="isExamStart" @click="onClearAllocate"> 清空分配结果 </b-button>
                    <b-button status="primary" type="outline" size="large" @click="exportAllocationResult"> 导出分配结果 </b-button>
                </div>
            </b-section>
            <b-section type="list" direction="vertical">
                <b-table
                    ref="listTableRef"
                    v-model:checkedKeys="tableCheckedKeys"
                    :queryFun="getList"
                    :columns="COLUMNS"
                    :rowKey="(raw) => raw.encryptId"
                    fullHeight
                    stickyHeader
                    :border="false"
                    :scroll="{ x: '100%' }"
                    tableLayoutFixed
                    :dataSelectable="(data) => !data.raw.inspector"
                    :pagination="{ pageType: 'server', showTotal: true, showSizer: true, pageSizeOpts }"
                >
                    <template #td-forceCommitAuthDesc="{ raw }">
                        <p :class="{ 'inspector-disabled': tableAuthProcess(raw, 'forceCommitAuth') }">
                            {{ raw.forceCommitAuthDesc }}<template v-if="tableAuthProcess(raw, 'forceCommitAuth')"> （暂不可用） </template>
                        </p>
                    </template>
                    <template #td-groupAnnounceAuthDesc="{ raw }">
                        <p :class="{ 'inspector-disabled': tableAuthProcess(raw, 'groupAnnounceAuth') }">
                            {{ raw.groupAnnounceAuthDesc }}<template v-if="tableAuthProcess(raw, 'groupAnnounceAuth')"> （暂不可用） </template>
                        </p>
                    </template>
                    <template #td-inspector="{ raw }">
                        <b-switch v-model="raw.inspector" :disabled="isExamEnd" @change="handleSwitchChange(raw)" />
                    </template>
                    <template #td-examineeCount="{ raw }">
                        {{ raw.inspector ? '/' : formatNumberWithThousandSeparator(raw.examineeCount || 0) }}
                    </template>
                    <template #td-opt="{ raw }">
                        <b-action :class="[{ disabled: raw.inspector }]" :disabled="raw.inspector" size="small" @click="showUpdateAllocate(raw)"> 调整管理考生人数 </b-action>
                    </template>
                    <template #empty>
                        <img class="empty-status-img" src="@/assets/images/no-data.png" alt="" />
                        <p class="empty-status-text">暂无监考者信息</p>
                    </template>
                </b-table>
            </b-section>
        </b-layout>
    </b-layout>
    <!-- 弹框：分配考生 -->
    <Assign v-if="assignDialogShow" :encryptExamId="encryptExamId" :queryForm="setFromData('allocate')" :success="onAssignSuccess" @close="closeDialog" />

    <!-- 弹框：调整管理考生人数 -->
    <UpdateAllocateDialog v-if="updateAllocateDialogShow" :encryptExamId="encryptExamId" :currentRow="currentRow" :success="onUpdateAllocateSuccess" @close="closeDialog" />

    <!-- 弹框：巡考开关提示 -->
    <InspectorTipDialog v-if="inspectorTipDialogShow" :row="rowData" :success="userInvigilatorInspectorUpdate" @close="closeDialog" />
</template>

<script setup lang="ts" name="Invigilator">
import type { ITablePagination } from '@boss/design';
import { pageSizeOpts } from '@/constant/common';
import {
    _userInvigilatorClearAllocate,
    _userInvigilatorInspectorHintStatus,
    _userInvigilatorInspectorQuery,
    _userInvigilatorInspectorUpdate,
    _userInvigilatorIsExamStart,
    _userInvigilatorList,
    _downloadAllocationResult,
} from '@/services/api/exam';
import { formatNumberWithThousandSeparator } from '@/utils/index';

import Assign from './assign.vue';
import { accountTypeList, COLUMNS, forceCommitAuthList, groupAnnounceAuthList, inspectorStatusList } from './contants';
import InspectorTipDialog from './inspector-tip-dialog.vue';
import UpdateAllocateDialog from './update-allocate.vue';

const queryData = reactive({
    name: '',
    mobile: '',
    accountType: -1,
    examineeNum: {},
    forceCommitAuth: -1,
    groupAnnounceAuth: -1,
    inspector: -1,
});
const listTableRef = ref();
function onSearch() {
    listTableRef.value.query();
    includeEncryptIds.value = new Set([]);
    excludeEncryptIds.value = new Set([]);
}
const formRef = ref();
const inspectorList = ref<any>(new Set([]));
const allocateExamineeStatus = ref(false);
const totalCount = ref(0);
const includeEncryptIds = ref(new Set()); // 选中的账号加密id
const excludeEncryptIds = ref(new Set()); // 未选中的账号加密id
const allocateExamineeCount = computed(() => {
    const inspectorSize = queryData.inspector === 0 ? 0 : inspectorList.value.size;
    const count = allocateExamineeStatus.value ? totalCount.value - excludeEncryptIds.value.size - inspectorSize : includeEncryptIds.value.size;
    return count >= 0 ? count : 0;
});
const encryptExamId: any = inject('encryptExamId');
function tableAuthProcess(row: { [x: string]: any; inspector: any }, key: string | number) {
    return row.inspector && row[key];
}
const switchLoading = ref(false);

async function exportAllocationResult() {
    const res: any = await _downloadAllocationResult({
        encryptExamId: encryptExamId.value,
    });
    if (res?.code === 0) {
        Toast.success('操作完成');
    } else {
        Toast.danger(res?.message ?? '操作失败');
    }
}
async function getInvigilatorInspectorHintStatus() {
    try {
        const { data } = await _userInvigilatorInspectorHintStatus();
        return data;
    } catch (e: any) {
        Toast.danger(e?.message);
        switchLoading.value = false;
        return false;
    }
}
/**
 * 打开巡考提示
 */
const inspectorTipDialogShow = ref(false);
const rowData = ref();
async function handleSwitchChange(value: { inspector: boolean }) {
    value.inspector = !value.inspector;
    rowData.value = value;
    if (switchLoading.value) {
        return;
    }
    switchLoading.value = true;
    const hintStatus = await getInvigilatorInspectorHintStatus();
    if (value.inspector || hintStatus) {
        await userInvigilatorInspectorUpdate(hintStatus);
        return;
    }
    inspectorTipDialogShow.value = true;
}
const tableData = ref<any>([]);
const tableCheckedKeys = ref<any>([]);
async function userInvigilatorInspectorUpdate(notHintAgain: any) {
    const { encryptId, inspector } = rowData.value;
    try {
        const { code } = await _userInvigilatorInspectorUpdate({ encryptId, inspector: !inspector, notHintAgain, encryptExamId: encryptExamId.value });
        if (code === 0) {
            const index = tableData.value.findIndex((item: { encryptId: any }) => item.encryptId === encryptId);
            tableData.value[index].inspector = !inspector;
            tableData.value[index].examineeCount = 0;
            if (!inspector) {
                const item = tableData.value.find((item: { encryptId: any }) => item.encryptId === encryptId);
                if (item) {
                    tableCheckedKeys.value = tableCheckedKeys.value.filter((v: any) => v !== item.encryptId);
                }
                includeEncryptIds.value.delete(encryptId);
                excludeEncryptIds.value.delete(encryptId);
                inspectorList.value.add(encryptId);
            } else {
                excludeEncryptIds.value.add(encryptId);
                inspectorList.value.delete(encryptId);
            }
            closeDialog();
            Toast.success('操作完成');
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    } finally {
        switchLoading.value = false;
    }
}

/**
 * 全部选中或撤销全选
 */
async function onAllocateExamineeStatus(status: boolean) {
    await getInspectorQuery();
    allocateExamineeStatus.value = status;
    setRowSelection(status);
    nextTick(() => {
        if (!status) {
            resetAllocateData();
        }
    });
}
function setRowSelection(status: boolean) {
    tableData.value?.forEach((item: { inspector: any; encryptId: unknown }) => {
        if (item.inspector) {
            return;
        }
        if (status) {
            tableCheckedKeys.value.push(item.encryptId);
        }
        setSetData(status, item.encryptId);
    });
}
function setSetData(key: any, encryptId: unknown) {
    if (key) {
        includeEncryptIds.value.add(encryptId);
        excludeEncryptIds.value.delete(encryptId);
    } else {
        includeEncryptIds.value.delete(encryptId);
        excludeEncryptIds.value.add(encryptId);
    }
}
/**
 * 重置分配数据
 */
function resetAllocateData() {
    allocateExamineeStatus.value = false;
    includeEncryptIds.value = new Set([]);
    excludeEncryptIds.value = new Set([]);
    tableCheckedKeys.value = [];
}
/**
 * 分配考生
 */
const assignDialogShow = ref(false);
function onShowAssign() {
    const list = listTableRef.value.getDataByKeys(tableCheckedKeys.value);
    includeEncryptIds.value = new Set(list.map((item: { raw: { encryptId: any } }) => item.raw.encryptId));

    if ((!allocateExamineeStatus.value && !includeEncryptIds.value.size) || (allocateExamineeStatus.value && allocateExamineeCount.value <= 0)) {
        Toast.danger('请选中至少一行后才能操作');
        return;
    }
    assignDialogShow.value = true;
}
watchEffect(() => {
    for (const i of includeEncryptIds.value) {
        if (!tableCheckedKeys.value.includes(i)) {
            excludeEncryptIds.value.add(i);
        }
    }
});
/**
 * 分配考生成功,关闭弹窗并且更新列表数据
 */
function onAssignSuccess() {
    assignDialogShow.value = false;
    resetAllocateData();
    listTableRef.value.query({ keepPage: true });
}
/**
 * 调整管理考生人数
 * @param row
 */
const currentRow = ref({});
const updateAllocateDialogShow = ref(false);
function showUpdateAllocate(row: { inspector?: any }) {
    if (row.inspector) {
        return;
    }
    currentRow.value = row || {};
    updateAllocateDialogShow.value = true;
}
/**
 * 调整管理考生成功,关闭弹窗，更新列表数据
 */
function onUpdateAllocateSuccess() {
    resetAllocateData();
    updateAllocateDialogShow.value = false;
    listTableRef.value.query();
}
/**
 * 关闭dialog弹窗
 */
function closeDialog() {
    assignDialogShow.value = false;
    updateAllocateDialogShow.value = false;
    inspectorTipDialogShow.value = false;
    switchLoading.value = false;
}
/**
 * 清空分配结果
 */
function onClearAllocate() {
    const list = listTableRef.value.getDataByKeys(tableCheckedKeys.value);
    includeEncryptIds.value = new Set(list.map((item: { raw: { encryptId: any } }) => item.raw.encryptId));

    if ((!allocateExamineeStatus.value && !includeEncryptIds.value.size) || (allocateExamineeStatus.value && allocateExamineeCount.value <= 0)) {
        Toast.danger('请选中至少一行后才能操作');
        return;
    }
    Dialog.open({
        type: 'warning',
        title: '提示',
        content: () => `确定将选中的${allocateExamineeCount.value}名监考官管理考生的人数重置为0吗？该操作不可逆，请慎重。`,
        confirm() {
            _userInvigilatorClearAllocate(setFromData('allocate')).then((res) => {
                if (res.code !== 0) {
                    return;
                }
                resetAllocateData();
                Toast.success('操作完成');
                listTableRef.value.query({ keepPage: true });
            });
        },
    });
}
/**
 * 获取考试是否开始
 * 考试开始后【分配考生】和【清空分配结果】按钮置灰
 * 考试开始后（从考试开始当天的提前入场开始）巡考开关状态不支持修改
 */
const isExamStart = ref(false); // （不包括提前入场时间）
const isExamEnd = ref(false);
async function getIsExamStart() {
    try {
        const { data } = await _userInvigilatorIsExamStart({ encryptExamId: encryptExamId.value });
        isExamStart.value = data?.examStart;
        isExamEnd.value = data?.examEnd;
    } catch (e: any) {
        Toast.danger(e?.message);
    }
}
/**
 * 接口传参获取
 * @param key(key 等于allocate时加入分配相关的字段)
 * @returns {*&{groupAnnounceAuth: (string|*), forceCommitAuth: (string|*), accountType: (string|*), encryptExamId: *}}
 */
function setFromData(key: string) {
    const { accountType, forceCommitAuth, groupAnnounceAuth, examineeNum, inspector } = queryData;
    const params: any = {
        ...queryData,
        encryptExamId: encryptExamId.value,
        accountType: accountType === -1 ? '' : accountType,
        forceCommitAuth: forceCommitAuth === -1 ? '' : forceCommitAuth,
        groupAnnounceAuth: groupAnnounceAuth === -1 ? '' : groupAnnounceAuth,
        minExamineeNum: examineeNum.min && examineeNum.min >= 0 ? Number(examineeNum.min) : '',
        maxExamineeNum: examineeNum.max && examineeNum.max >= 0 ? Number(examineeNum.max) : '',
        inspector: inspector === -1 ? '' : inspector,
    };
    delete params.examineeNum;

    if (key === 'allocate') {
        params.includeEncryptIds = allocateExamineeStatus.value ? '' : [...includeEncryptIds.value].join(',');
        params.excludeEncryptIds = allocateExamineeStatus.value ? [...excludeEncryptIds.value].join(',') : '';
    }
    return params;
}

async function getList({ page, pageSize }: Partial<ITablePagination>): Promise<any> {
    try {
        const params = {
            ...setFromData(''),
            page,
            pageSize,
        };
        const { data } = await _userInvigilatorList(params);
        totalCount.value = data?.total || 0;
        tableData.value = data?.items || [];
        setRowSelection(allocateExamineeStatus.value);
        return {
            total: data?.total || 0,
            data: data?.items || [],
        };
    } catch (error: any) {
        Toast.danger(error?.message);
    }
}
/**
 * 查询巡考官ID
 */
async function getInspectorQuery() {
    try {
        const { code, data } = await _userInvigilatorInspectorQuery({ encryptExamId: encryptExamId.value });
        if (code === 0) {
            inspectorList.value = new Set(data);
        }
    } catch (e: any) {
        Toast.danger(e?.message);
    }
}
onMounted(() => {
    listTableRef.value.query();
});
getIsExamStart();
</script>

<style scoped lang="less">
@import 'style/index';
</style>
