<!-- 分配考生 -->
<template>
    <b-dialog
        v-model="visible"
        title="分配考生"
        width="580px"
        wrapClass="allocate-invigilator-dialog"
        lock
        :layerClosable="false"
        :confirmLoading="loading"
        :beforeConfirm="submit"
        @cancel="closeDialog"
        @close="closeDialog"
    >
        <b-form ref="formComponent" :model="formData" @submit.prevent>
            <b-row>
                <b-col :span="8" class="grid-content">
                    <div class="value">
                        {{ formatNumberWithThousandSeparator(invigilatorInfo.unAllocateCount || 0) }}
                    </div>
                    <div class="label">未分配考生</div>
                </b-col>
                <b-col :span="8" class="grid-content">
                    <div class="value">
                        {{ formatNumberWithThousandSeparator(invigilatorInfo.allocatedCount || 0) }}
                    </div>
                    <div class="label">已分配考生</div>
                </b-col>
                <b-col :span="8" class="grid-content">
                    <div class="value">
                        {{ formatNumberWithThousandSeparator(invigilatorInfo.examineeCount || 0) }}
                    </div>
                    <div class="label">考生总人数</div>
                </b-col>
            </b-row>
            <div class="invigilator-tip">
                将 <span class="number">{{ formatNumberWithThousandSeparator(invigilatorInfo.examineeCount || 0) }}</span> 名考生分配给
                <span class="number">{{ formatNumberWithThousandSeparator(invigilatorInfo.selectInvigilatorCount || 0) }}</span> 个监考官，请选择分配方式
            </div>
            <b-form-item hideLabel field="allocateType" class="allocate-type">
                <b-radio-group v-model="formData.allocateType" @change="onRadioChange">
                    <b-radio :value="1">
                        <span class="allocate-type-text">
                            平均分配考生
                            <span class="allocate-type-sub-text">一考生对应一监考</span>
                        </span>
                    </b-radio>
                    <b-radio :value="2">
                        <span class="allocate-type-text">
                            限定人均监考上限
                            <span class="allocate-type-sub-text">一考生对应一监考</span>
                        </span>
                    </b-radio>
                </b-radio-group>
            </b-form-item>
            <div v-if="formData.allocateType" class="result">
                <template v-if="formData.allocateType === 1">
                    <div class="result-text">
                        预计结果：每名监考管理&nbsp;<span class="number">{{ manageExamineeCount }}</span> 个考生
                    </div>
                    <div class="tip">为保障监考质量，建议每名监考官管理考生人数不要超过18</div>
                </template>
                <template v-if="formData.allocateType === 2">
                    <b-form-item label="人均管理考生 " class="max-allocate-count" field="maxAllocateCount">
                        <b-input v-model.trim="formData.maxAllocateCount" v-customTxt="{ reg: '\d' }" placeholder="请输入" @blur="blur">
                            <template #suffix> 人 </template>
                        </b-input>
                    </b-form-item>
                    <div class="tip">为保障监考质量，建议每名监考官管理考生人数不要超过18</div>
                    <span class="result-text">
                        <span class="residue-count-text">剩余考生：</span>{{ examineeResidueCount }} 人
                        <span v-if="examineeResidueCount" class="residue-count-text">（请不要忘记为ta们分配监考官~）</span>
                    </span>
                </template>
            </div>
        </b-form>
    </b-dialog>
</template>

<script setup>
import { _userInvigilatorAllocateExaminee, _userInvigilatorInfo } from '@/services/api/exam';
import { formatNumberWithThousandSeparator } from '@/utils/index.ts';

defineOptions({
    name: 'Assign',
});

const props = defineProps({
    encryptExamId: String,
    queryForm: {
        type: Object,
        default: () => ({}),
    },
    success: {
        type: Function,
        default: () => {},
    },
});
const emit = defineEmits(['close']);
const visible = ref(true);
const formData = ref({
    allocateType: '',
    maxAllocateCount: '',
});

const manageExamineeCount = computed(() => {
    if (Number(formData.value.allocateType) === 1) {
        const { examineeCount, selectInvigilatorCount } = invigilatorInfo.value;
        const isInteger = examineeCount % selectInvigilatorCount === 0;
        const result = examineeCount / selectInvigilatorCount;
        if (!selectInvigilatorCount) {
            return 0;
        }
        return isInteger ? result : `${Math.floor(result)}-${Math.ceil(result)}`;
    }
    return 0;
});
const examineeResidueCount = computed(() => {
    if (formData.value.maxAllocateCount) {
        const { examineeCount, selectInvigilatorCount } = invigilatorInfo.value;
        const result = examineeCount - selectInvigilatorCount * Number(formData.value.maxAllocateCount);
        return result <= 0 ? 0 : result;
    }
    return formatNumberWithThousandSeparator(invigilatorInfo.value.examineeCount || 0);
});
function onRadioChange(value) {
    if (Number(value) === 1) {
        formData.value.maxAllocateCount = '';
    }
}
function blur(e) {
    const value = e.target.value.startsWith('0') ? 0 : Number(e.target.value);
    if (value < 1 || value > 9999) {
        formData.value.maxAllocateCount = '';
    }
}
const formComponent = ref();
async function submit() {
    if (!formData.value.allocateType) {
        Toast.danger('请选择分配方式');
        return false;
    }
    if (formData.value.allocateType === 2 && !formData.value.maxAllocateCount) {
        Toast.danger('请填写人均管理考生上限');
        return false;
    }
    const res = await formComponent.value.validate();
    if (!res) {
        return fetchAllocateExaminee();
    }
}
/**
 * 调整管理人数
 * @returns {Promise<void>}
 */
const loading = ref(false);
async function fetchAllocateExaminee() {
    try {
        loading.value = true;
        const { code } = await _userInvigilatorAllocateExaminee({
            queryParam: props.queryForm,
            allocateParam: {
                allocateType: formData.value.allocateType,
                maxAllocateCount: Number(formData.value.maxAllocateCount),
            },
        });
        if (code === 0) {
            Toast.success('操作完成');
            props.success();
        }
        return code === 0;
    } catch (e) {
        Toast.danger(e?.message);
        return false;
    } finally {
        loading.value = false;
    }
}
/**
 * 关闭
 */
function closeDialog() {
    emit('close');
}
/**
 * 获取考生基本信息
 */
const invigilatorInfo = ref({
    examineeCount: 0,
    selectInvigilatorCount: 0,
    allocatedCount: 0,
    unAllocateCount: 0,
});
async function getInvigilatorInfo() {
    try {
        const { data } = await _userInvigilatorInfo({
            ...props.queryForm,
            inspector: 0, // 1代表巡考官，0代表监考官
        });
        invigilatorInfo.value = data || {};
    } catch (e) {
        Toast.danger(e?.message);
    }
}
// ------------------------------ 初始化 ------------------------------
getInvigilatorInfo();
</script>

<style lang="less" scoped>
@import 'style/assign';
</style>

<style lang="less">
.allocate-invigilator-dialog {
    .dialog-header h3.title {
        height: 28px;
        font-size: 20px;
        font-weight: 500;
        color: #1f1f1f;
        line-height: 28px;
    }
    .dialog-container {
        padding: 24px;
    }
}
</style>
