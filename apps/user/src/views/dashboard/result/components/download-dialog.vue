<template>
    <b-dialog :modelValue="visible" width="540px" :layerClosable="false" wrapClass="dialog-add-examinee" @cancel="closeDialog">
        <template v-if="title" #title>
            <b-layout style="gap: 8px" align="center">
                <div class="dialog-title-text">
                    {{ title }}
                </div>
            </b-layout>
        </template>
        <b-layout v-loading="fetching" direction="vertical" style="gap: 24px; min-height: 100px">
            <template v-if="downloadType === 1">
                <template v-for="report in listItems" :key="report.encEvaPaperId">
                    <b-layout class="report-item">
                        <div class="text-bold">{{ report.paperName }}</div>
                        <StatusPoint :status="report.statusByTime" :description="`${report.answerFinishCount}人完成`" :config="examStatusConfig" />
                    </b-layout>
                    <b-grid v-if="report.statusByTime !== 1" :gap="[12, 12]" :columns="2" class="choose-list">
                        <b-grid-item
                            v-for="item in report.examVersionInfo"
                            :key="item.encVersionId"
                            class="choose-item"
                            :checked="isChecked(report.encPaperId, item.encVersionId)"
                            :disabled="report.statusByTime === 1 ? true : undefined"
                            @click.stop="changeChecked(report.encPaperId, item.encVersionId)"
                        >
                            <b-checkbox :modelValue="isChecked(report.encPaperId, item.encVersionId)" :ripple="false" :disabled="report.statusByTime === 1" />
                            <div class="text-bold">{{ item.versionName }}</div>
                        </b-grid-item>
                    </b-grid>
                </template>
            </template>
            <template v-if="downloadType === 2">
                <template v-for="report in listItems" :key="report.encEvaPaperId">
                    <b-layout v-if="report.canDownloadResult" class="report-list" direction="vertical">
                        <div
                            v-if="report.paperType === 1 || report.canDownloadResult"
                            class="choose-item"
                            :checked="isChecked(report.encPaperId, report.encVersionId)"
                            :disabled="report.statusByTime === 1 ? true : undefined"
                            @click="changeChecked(report.encPaperId, report.encVersionId)"
                        >
                            <b-checkbox :modelValue="isChecked(report.encPaperId, report.encVersionId)" :disabled="report.statusByTime === 1" />
                            <div class="text-bold">
                                {{ report.paperName }}
                                <StatusPoint :status="report.statusByTime" :description="`${report.answerFinishCount}人完成`" :config="examStatusConfig" />
                            </div>
                        </div>
                    </b-layout>
                </template>
            </template>
        </b-layout>
        <template #footer>
            <b-layout align="end" justify="end" style="gap: 12px">
                <template v-if="dialogMode === 'download'">
                    <b-button type="outline" @click="closeDialog"> 取消 </b-button>
                    <b-button type="primary" :loading="downloadLoading" :disabled="fetching" @click="handleBeforeConfirm"> 下载 </b-button>
                </template>
                <b-button v-else type="primary" :loading="downloadLoading" :disabled="fetching" @click="onView"> 查看 </b-button>
            </b-layout>
        </template>
    </b-dialog>
</template>
<script setup lang="ts">
import type { VersionInfoReq, VersionInfoRes } from '@/services/api/result/interface';

import ResultService from '@/services/api/result';
import { examStatusConfig } from '../constant';
import { startAnimation } from '@/utils/animate';
import { qs } from '@boss/utils';

interface PaperInfoListT {
    encPaperIds: string;
    paperType: number;
    encRptVersionIds: string[];
}

const emit = defineEmits(['onView']);

const visible = ref(false);
const fetching = ref(true);
const downloadLoading = ref(false);
const title = ref('');
const dialogMode = ref<'download' | 'view' | 'download-candidate'>('download');
const downloadType = ref(1); // 下载类型 // 1:报告 2:明细
const isCandidate = ref(false);
const prodcutId = ref(0); // 产品id 当前只有预览用到了
const queryData = ref<VersionInfoReq>({
    encExamId: '', // 场次加密id ，下载全部时传大场次id，下载单个时，传小场次id
    encPaperId: '', // 试卷加密id，下载全部时不传，下载单个时，传试卷id
    encExamineeIds: [],
    teamName: '',
    encExamineeId: '',
    divideReportPropertyId: '',
});

const paperInfoList = ref<PaperInfoListT[]>([]); // 下载需要用到的数据  选中状态也是此数据控制
const listItems = ref<VersionInfoRes[]>([]); // 展示的选项内容

function getSelectVersionIds() {
    let encVersionIds: string[] = [];

    paperInfoList.value.forEach((item) => {
        if (item.encRptVersionIds && item.encRptVersionIds.length) {
            item.encRptVersionIds.forEach((id) => {
                encVersionIds.push(id);
            });
        }
    });

    return encVersionIds;
}

// 查看事件回调
function onView() {
    const encVersionIds = getSelectVersionIds();

    if (encVersionIds.length !== 1) {
        Toast.danger('必须选中一个版本');
        return;
    }

    const encVersionId = encVersionIds[0];
    const { encExamineeIds } = queryData.value;
    const encExamineeId = encExamineeIds.length ? encExamineeIds[0] : '';

    const queryDataStr = qs.stringify({
        encSeqTempId: listItems.value[0].encSeqTempId,
        encExamineeId,
        encRptVersionId: encVersionId,
        productId: prodcutId.value,
    });

    const url = `/extend/report/competency?${queryDataStr}`;
    window.open(url);
    closeDialog();
}

// 获取选中 版本id 下标
function getVersionIdIndex(encPaperId: string, versionId: string) {
    const newPaperInfoList = [...paperInfoList.value];

    const index = newPaperInfoList.findIndex((item) => item.encPaperIds === encPaperId);
    const versionIds = newPaperInfoList[index].encRptVersionIds;

    const versionIdIndex = versionIds.findIndex((vid: string) => vid === versionId);

    return versionIdIndex;
}

// 判断是否被选中
const isChecked = (encPaperId: string, versionId: string) => {
    const versionIdIndex = getVersionIdIndex(encPaperId, versionId);
    return versionIdIndex >= 0;
};

// 切换选中状态
const changeChecked = (encPaperId: string, versionId: string) => {
    const newPaperInfoList = [...paperInfoList.value];
    const index = newPaperInfoList.findIndex((item) => item.encPaperIds === encPaperId);
    const versionIdIndex = getVersionIdIndex(encPaperId, versionId);
    const checked = isChecked(encPaperId, versionId);

    if (checked) {
        newPaperInfoList[index].encRptVersionIds.splice(versionIdIndex, 1);
    } else {
        newPaperInfoList[index].encRptVersionIds.push(versionId);
    }
};

async function getVersionInfo(query: any) {
    fetching.value = true;

    const { code, data } = await ResultService.versionInfo({ ...query });

    if (code !== 0) {
        fetching.value = false;
        return;
    }

    fetching.value = false;
    let defaultPaperInfoList: any[] = [];

    // 转换API响应数据为所需格式
    listItems.value = data.map((item: VersionInfoRes) => {
        const { encPaperId, paperType } = item || {};

        // 提前预设好下载需要的数据格式
        const PaperInfoListItem: PaperInfoListT = {
            encPaperIds: encPaperId,
            paperType,
            encRptVersionIds: [],
        };

        // 只有下载模式需要默认选中
        if (dialogMode.value === 'download') {
            // 报告需要默认选中版本
            if (downloadType.value === 1) {
                (item?.examVersionInfo || []).forEach((info) => {
                    PaperInfoListItem.encRptVersionIds.push(info.encVersionId);
                });
            }

            // 明细需要默认选中试卷 id
            if (downloadType.value === 2) {
                PaperInfoListItem.encRptVersionIds.push(item.encVersionId);
            }
        }

        defaultPaperInfoList.push(PaperInfoListItem);

        return {
            ...item,
        };
    });

    paperInfoList.value = defaultPaperInfoList;
}

// 处理确认按钮点击事件
async function handleBeforeConfirm(e: any) {
    const newPaperInfoList = paperInfoList.value.filter((item) => item.encRptVersionIds.length > 0);
    if (newPaperInfoList.length === 0) {
        const text = downloadType.value === 1 ? '请选择至少一种报告版本！' : '请选择至少一种试卷！';
        Toast.danger(text);
        return false;
    }

    const { encExamineeId, teamName } = queryData.value;

    // 单报告下载
    if (isCandidate.value) {
        const encVersionIds = getSelectVersionIds();
        if (encVersionIds.length !== 1) {
            Toast.danger('必须选中一个版本');
            return;
        }
    }

    downloadLoading.value = true;

    try {
        if (isCandidate.value) {
            await reportDownload({ ...queryData.value, paperInfoList: newPaperInfoList });
            return;
        }

        // 批量报告下载
        if (downloadType.value === 1) {
            await downloadBatchReports(e, {
                ...queryData.value,
                paperInfoList: newPaperInfoList,
            });
        }

        // 批量下载结果明细
        if (downloadType.value === 2) {
            await downloadResultDetails({
                ...queryData.value,
                paperInfoList: newPaperInfoList,
            });
        }

        closeDialog();
    } finally {
        downloadLoading.value = false;
    }
}

// 下载单份报告
async function reportDownload(params: any) {
    const encRptVersionId = params.paperInfoList[0].encRptVersionIds[0];
    const { code, data, message } = await ResultService.reportDownload({
        ...params,
        encRptVersionId,
        paperInfoList: null,
    });

    if (code !== 0) {
        Toast.danger(message);
        return Promise.reject(message);
    }

    closeDialog();
    window.open(data);
}

// 下载结果明细
async function downloadResultDetails(query: any): Promise<void> {
    try {
        await ResultService.resultDownload({ ...query });
        Toast.success('操作完成');
    } catch (error: any) {
        Toast.danger(error.message || error);
    }
}

// 批量下载报告
async function downloadBatchReports(e: any, query: any): Promise<void> {
    const { code, message } = await ResultService.reportDownloadBatch(query);

    if (code !== 0) {
        Toast.danger(message);
        return Promise.reject(message);
    }

    startAnimation(e);
}

function openDialog(query: any) {
    visible.value = true;
    title.value = query.title;
    downloadType.value = query.downloadType;
    dialogMode.value = query?.dialogMode || 'download';
    prodcutId.value = query.prodcutId || 0;

    queryData.value = {
        ...queryData.value,
        encExamId: query.encExamId,
        encPaperId: query?.encPaperId || '',
        encExamineeIds: query?.encExamineeIds || [],
    };

    getVersionInfo(queryData.value);
}

function closeDialog() {
    isCandidate.value = false;
    visible.value = false;
    listItems.value = [];
}

// 单个报告下载
function openCandidate(query: any) {
    isCandidate.value = true;
    visible.value = true;
    title.value = query.title;
    downloadType.value = query.downloadType;

    queryData.value = {
        encExamId: query.encExamId,
        encPaperId: query?.encPaperId || '',
        encExamineeId: query?.encExamineeId,
        teamName: query?.teamName || '', // 团队版 独有参数
        divideReportPropertyId: query?.divideReportPropertyId || '', // 团队版 独有参数
    };

    getVersionInfo(queryData.value);
}

defineExpose({
    open: openDialog,
    close: closeDialog,
    openCandidate, // 单个报告下载
    downloadReports: downloadBatchReports, // 批量报告下载
    downloadResultDetails: downloadResultDetails, // 批量下载结果明细
});
</script>
<style lang="less" scoped>
.text-bold {
    font-size: 14px;
    font-weight: 500;
    color: #1f1f1f;
    line-height: 22px;
}

.report-list {
    gap: 12px;
}

.report-item {
    gap: 12px;
}

.choose-list {
    flex-wrap: wrap;
    gap: 12px;
}

.choose-item {
    user-select: none;
    display: flex;
    border-radius: 8px;
    border: 1px solid #00a6a7;
    flex: 0 0 calc(50% - 12px);
    gap: 12px;
    padding: 16px 21px;

    &[checked],
    &:hover {
        cursor: pointer;
        outline: 3px solid #caebeb;
    }

    &[disabled] {
        opacity: 0.5;
        cursor: not-allowed;
        outline: none;
    }
}
</style>
