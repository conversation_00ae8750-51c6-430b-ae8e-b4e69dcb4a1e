<template>
    <b-layout type="content" class="page-examinee-list">
        <BackButton :clickBack="() => $router.push('/result/list')" />
        <b-layout v-loading="loading" direction="vertical">
            <b-section v-if="detailData" justify="start" align="center" class="page-title-wrap">
                <b-title size="large"> 试卷：{{ detailData?.paperName }} </b-title>
                <p v-if="detailData.paperType === 5" class="tag-type">组合试卷</p>
                <div style="margin-left: 21px">
                    <b-split size="xlarge">
                        <span class="label">场次：</span><span class="content">{{ detailData?.examName }}</span>
                    </b-split>
                </div>
            </b-section>
            <b-tab @change="handleTabChange">
                <b-tab-panel v-for="tab in tabConfig" :key="tab.key" :title="tab.title" />
            </b-tab>
            <template v-if="tabKey === 1">
                <TableFilter v-model="queryData" :paperStatusList @search="onSearch" />
                <b-section type="title" justify="between" class="sticky-title-wrap">
                    <p></p>
                    <b-space gap="12px">
                        <b-button v-if="!detailData?.showTeamResult" type="primary" size="large" @click="handleDownloadReport"> 批量下载报告 </b-button>
                        <b-button v-if="detailData?.canDownloadResult" type="outline" status="primary" size="large" @click="handleDownloadReportDetail"> 下载结果明细 </b-button>
                    </b-space>
                </b-section>
                <TableList
                    ref="listTableRef"
                    :query="queryDataFormat"
                    :getData="getTableData"
                    :columns="columns"
                    :rowKey="(raw: any) => raw.encExamineeId"
                    v-model:checkedKeys="keysSelected"
                >
                    <template #td-name="{ raw }">
                        {{ raw.name }}
                        <IdShow :content="`考生ID：${raw.examineeId}`" />
                    </template>
                    <template #td-examStatus="{ raw }">
                        <StatusPoint :config="paperStatusConfig" :status="raw.examStatus" />
                    </template>
                    <template #td-operation="{ raw }">
                        <template v-if="!detailData?.showTeamResult && (raw.examStatus === 3 || detailData?.teamReportReady === 1)">
                            <b-action @click="() => handleDownloadSingleReport(raw)"> 下载报告 </b-action>
                            <template v-if="showDevFunction">
                                <b-action @click="clearPDFCache(raw)">
                                    <SvgIcon name="hicon-no-paper" size="16" />
                                </b-action>

                                <b-action @click="onView(raw)">
                                    <SvgIcon name="hicon-search" size="16" style="cursor: pointer" />
                                </b-action>
                            </template>
                        </template>
                    </template>
                </TableList>
            </template>
            <template v-else>
                <TeamResultDetail :encPaperId="encPaperId" :encExamId="encExamId" :detailData="detailData" />
            </template>
        </b-layout>
    </b-layout>
    <DownloadDialog ref="downloadDialogRef" />
</template>

<script setup lang="ts">
import type { ITableColumnDataRaw } from '@boss/design';
import type { EvaluationDetailRes } from '@/services/api/result/interface';
import StatusPoint from '@/components/business/status-point/index.vue';
import { _clearPDFCache } from '@/services/api/examinee';
import ResultService from '@/services/api/result';
import { BackButton } from '@/views/dashboard/sessions-v2/components';

import { useRoute, useRouter } from 'vue-router';
import TableFilter from './components/table-filter.vue';
import TableList from '@/components/table-list/index.vue';
import TeamResultDetail from './components/team-result-detail.vue';
import { DEPLOY_ENV } from '@/constant';
import { paperStatusConfig, paperStatusList } from './constant';
import DownloadDialog from './components/download-dialog.vue';

const columns: Partial<ITableColumnDataRaw>[] = [
    {
        type: 'checkbox',
        width: 40,
        checkbox: {
            clearOnPageChange: false,
        },
    },
    {
        label: '姓名',
        field: 'name',
        width: 80,
    },
    {
        label: '手机号',
        field: 'mobile',
        width: 80,
    },
    {
        label: '邮箱',
        field: 'email',
        width: 80,
    },
    {
        label: '应聘职位',
        field: 'positionApplied',
        width: 80,
    },
    {
        label: '第三方唯一码',
        field: 'thirdPartyUniqueCode',
        width: 100,
    },
    {
        label: '完成情况',
        field: 'examStatus',
        width: 80,
    },
    {
        label: '完成时间',
        field: 'examFinishTime',
        width: 100,
    },
    {
        label: '操作',
        field: 'operation',
        width: 100,
        fixed: 'right',
    },
];

const $route = useRoute();
const $router = useRouter();
const showDevFunction = ref(DEPLOY_ENV !== 'prod');
const encPaperId = $route.query?.encPaperId as string;
const encExamId = $route.query?.encExamId as string;

const loading = ref(false);
const keysSelected = ref([]);
const detailData = ref<EvaluationDetailRes>();
const downloadDialogRef = ref();

const queryData = ref({
    ...($route.query || {}),
    examStatusStr: '0,1,3',
    examTimeStr: [],
});

const queryDataFormat = computed(() => {
    const { examTimeStr } = queryData.value;
    return {
        ...queryData.value,
        examFinishTimeBeginStr: examTimeStr[0] || '',
        examFinishTimeEndStr: examTimeStr[1] || '',
        examTimeStr: null,
    };
});

const tabKey = ref(1);
const tabConfig = ref([
    {
        key: 1,
        title: '个人结果',
    },
]);

const listTableRef = ref<any>(null);

function onView(raw: any) {
    const { name, encExamineeId } = raw;
    const title = `查看考生【${name}】的报告`;

    downloadDialogRef.value.open({
        title,
        encExamId,
        encPaperId,
        downloadType: 1,
        prodcutId: detailData.value?.prodcutId,
        encExamineeIds: [encExamineeId],
        dialogMode: 'view',
    });
}

function handleTabChange(key: number) {
    tabKey.value = key;

    nextTick(() => {
        if (key === 1) {
            onSearch();
        }
    });
}

async function getTableData(queryData: any) {
    loading.value = true;

    let tableList = {
        list: [],
        total: 0,
    };

    const res = await ResultService.evaluationDetail({
        ...queryData,
        encPaperId,
        encExamId,
    });

    const { code, data, message } = res;
    loading.value = false;
    if (code !== 0) {
        Toast.danger(message);
        return {
            ...res,
            data: tableList,
        };
    }

    detailData.value = data;

    if (data.showTeamResult && tabConfig.value?.length <= 1) {
        tabConfig.value = [
            ...tabConfig.value,
            {
                key: 2,
                title: '团队结果',
            },
        ];
    }

    return {
        ...res,
        data: {
            ...data.examineeList,
        },
    };
}

function onSearch() {
    listTableRef.value.query();
}

async function handleDownloadSingleReport(raw: { name: string; encExamineeId: string }) {
    const { name, encExamineeId } = raw;
    const title = `下载报告（考生：${name} 单人报告）`;

    downloadDialogRef.value.openCandidate({
        title,
        downloadType: 1,
        encExamId,
        encPaperId,
        encExamineeId,
    });
}

async function handleDownloadReportDetail() {
    const length = keysSelected.value?.length;

    const title = length ? `下载结果明细（已选${length}人）` : `下载结果明细（所有考生）`;

    downloadDialogRef.value.open({
        title,
        encExamId,
        encPaperId,
        downloadType: 2,
        encExamineeIds: keysSelected.value,
    });
}

async function handleDownloadReport(e: any) {
    const length = keysSelected.value?.length;

    const title = length ? `下载报告（已选${length}人）` : `下载报告（所有考生）`;

    downloadDialogRef.value.open({
        title,
        encExamId,
        encPaperId,
        downloadType: 1,
        encExamineeIds: keysSelected.value,
    });
}

async function clearPDFCache(raw: any) {
    const params = {
        encExamId,
        encPaperId,
        encExamineeId: raw.encExamineeId,
    };
    const res = await _clearPDFCache(params);

    if (res.code === 0) {
        Toast.success('已清除报告缓存');
    } else {
        Toast.success('清除报告缓存失败');
    }
}

onMounted(() => {
    onSearch();
});
</script>

<style lang="less" scoped>
.page-examinee-list {
    .page-title-wrap {
        height: 64px;
        padding: 0 20px;
        background-color: #f7fbff;
        margin: 0 -20px;
        width: auto;
        :deep(.b-split-pre-line::before) {
            height: 10px;
        }
    }
    .b-title {
        flex-shrink: 0;
        margin-right: 0;
    }

    .tag-type {
        margin-left: 10px;
        padding: 2px 4px;
        background: #eff3ff;
        border: 1px solid #c2d2fa;
        border-radius: 4px;
        color: #4465eb;
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 16px; /* 1.333 */
    }

    .label {
        color: #808080;
    }

    .sticky-title-wrap {
        margin-top: 0;
        padding: 20px 0;
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: #fff;
    }

    .tag-notice-type {
        padding: 0 6px;
        &.tag-email {
            background-color: var(--gray-color-3);
            margin-right: 4px;
        }
        &.tag-sms {
            background-color: #cce7ff;
        }
    }
    :deep(.b-form .b-form-item-label-prefix) {
        height: auto;
    }

    :deep(.b-tab-content) {
        padding: 0;
    }
}
</style>
