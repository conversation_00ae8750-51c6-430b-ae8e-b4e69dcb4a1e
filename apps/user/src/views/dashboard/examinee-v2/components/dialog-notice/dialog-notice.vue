<template>
    <b-dialog
        v-model="visible"
        title="发送通知"
        :layerClosable="false"
        :wrapClass="['dialog-notice', { 'dialog-notice-with-email': formData.noticeTypes.includes('0') }]"
        :beforeConfirm="handleBeforeConfirm"
        width="720px"
        @cancel="onClose"
    >
        <b-checkbox-group v-model="formData.noticeTypes" class="custom-checkbox-wrap">
            <b-checkbox
                v-for="item in NOTICE_TYPE_LIST"
                :key="item.value"
                class="card-checkbox"
                :value="item.value"
                :ripple="false"
                :disabled="formData.noticeTypes.length === 1 && formData.noticeTypes[0] === item.value"
            >
                <template #checkbox="{ checked }">
                    <div class="custom-checkbox-card" :class="{ 'custom-checkbox-card-checked': checked }">
                        <div class="custom-checkbox-card-title">
                            <b-checkbox
                                :modelValue="checked"
                                :value="item.value"
                                :ripple="false"
                                :disabled="formData.noticeTypes.length === 1 && formData.noticeTypes[0] === item.value"
                            />
                            <span>{{ item.label }}</span>
                        </div>
                    </div>
                </template>
            </b-checkbox>
        </b-checkbox-group>
        <div v-show="formData.noticeTypes.includes('0')" class="email-section-wrap">
            <b-section type="title" class="section-title">
                <b-title type="preline" size="small">
                    编辑邮件内容
                    <b-radio-group v-model="formData.emailVersion" class="email-version-radio-group">
                        <b-radio value="all">完整版</b-radio>
                        <b-radio value="simple">精简版</b-radio>
                    </b-radio-group>
                </b-title>
                <template #right>
                    <div class="title-action">
                        <TemplateTotalCount :wordsCount="emailWordsCount" :maxCount="EMAIL_MAX_COUNT" :loading="emailCountLoading > 0" />
                        <b-action @click="previewEmail">预览邮件</b-action>
                    </div>
                </template>
            </b-section>
            <div class="email-template-wrap" :class="{ focusing: isFocus }">
                <b-input
                    v-model.trim="formData.emailSubject"
                    class="email-subject"
                    trimBeforePaste
                    placeholder="请填写"
                    :maxLength="50"
                    @focus="isFocus = true"
                    @blur="isFocus = false"
                >
                    <template #prefix>主题：</template>
                </b-input>
                <Editor
                    ref="editorComponent"
                    v-model="formData.emailTemplate"
                    placeholder="请填写"
                    :content_style="contentStyle"
                    :toolbar="toolbar"
                    @focus="isFocus = true"
                    @blur="isFocus = false"
                    @keyup.delete="handleDeleteKeyup"
                />
                <DynamicParams :paramsList="emailParamsList" @insert="insertContent" />
                <AdditionalInfo v-model="emailAdditionalValue" :paramsList="emailAdditionalList" />
            </div>
            <b-section type="title" class="section-title">
                <div>邮件作答说明</div>
                <template #right>
                    <div class="title-action">
                        <TemplateTotalCount title="作答说明字数" :wordsCount="emailDescribeWordsCount" :maxCount="EMAIL_DESCRIBE_MAX_COUNT" :loading="emailCountLoading > 0" />
                    </div>
                </template>
            </b-section>
            <b-textarea
                v-model="formData.emailRemarks"
                :maxLength="EMAIL_DESCRIBE_MAX_COUNT"
                placeholder="请输入邮件作答说明"
                allowClear
                :autoSize="{
                    minRows: 9,
                    maxRows: 9,
                }"
            />
        </div>
        <div v-show="formData.noticeTypes.includes('1')" class="sms-section-wrap">
            <b-section type="title" class="section-title">
                <b-title type="preline" size="small">编辑短信内容</b-title>
                <template #right>
                    <div class="title-action">
                        <TemplateTotalCount :wordsCount="smsWordsCount" :maxCount="SMS_MAX_COUNT" :loading="emailCountLoading > 0" />
                    </div>
                </template>
            </b-section>
            <NoticeSmsTemplate v-model="formData.smsContent" :paramsList="smsParamsList" />
        </div>
    </b-dialog>
    <DialogEmailPreview
        v-if="dialogPreviewEmailShow"
        v-model="dialogPreviewEmailShow"
        :data="formData"
        :emailPreviewData="emailPreviewData"
        :emailAdditionalValue="emailAdditionalValue"
        :qrcodeDataUrl="qrcodeDataUrl"
        :examUrl="examUrl"
    />
</template>

<script lang="ts" setup>

import Editor from '@/components/business/editor/index.vue';
import {
    _examineeNotice,
    _examineeNoticeDetail,
    _existNoticed,
    _fileUploadAuth,
    _noticeEmailConfigList,
    _noticeEmailConfigOption,
    _noticeEmailConfigSave,
    _validateNotice,
} from '@/services/api/examinee';

import { dataURLtoFile } from '@/utils/index';
import { drawQrcode } from '@/utils/qrcode';
import { cloneDeep } from 'es-toolkit';

import AdditionalInfo from './additional-info.vue';
import {
    contentStyle,
    EMAIL_DEFAULT_CONTENT,
    EMAIL_DYNAMIC_PARAMS,
    EMAIL_MAX_COUNT,
    EMAIL_DESCRIBE_MAX_COUNT,
    renderEmailTemplate,
    getEmailAllParamsCount,
    getSmsContentText,
    INTRO,
    NOTICE_TYPE_LIST,
    SMS_DEFAULT_CONTENT,
    SMS_DEFAULT_SUBJECT,
    SMS_DYNAMIC_PARAMS,
    SMS_MAX_COUNT,
    SMS_NOT_MODIFIABLE_CONTENT,
    toolbar,
    EMAIL_REMARK_CONTENT,
} from './config';
import DialogEmailPreview from './dialog-email-preview.vue';

import DynamicParams from './dynamic-params.vue';
import NoticeSmsTemplate from './notice-sms-template.vue';

import TemplateTotalCount from './template-total-count.vue';
import { getExamineeExamUrl, getExamineeEmailPreview } from '../../services/api';

defineOptions({
    name: 'DialogNotice',
});
// ------------------------------ data ------------------------------
const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    // 全选带的查询参数
    filters: {
        type: Object,
        default: () => ({}),
    },
    // 加密考试id
    encryptExamId: {
        type: String,
        default: '',
    },
    // 加密考生id
    keysSelected: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    isCheckTotalAll: {
        type: Boolean,
        default: false,
    },
    keysExcludes: {
        type: Array as PropType<string[]>,
        default: () => [],
    },
    examName: {
        type: String,
        default: '',
    },
});
const emit = defineEmits(['update:modelValue', 'success']);

const visible = ref<boolean>(true);
const formData = ref({
    noticeTypes: ['0'], // 0 邮箱 1短信
    emailSubject: '', // 邮件主题
    emailContent: '', // 邮件内容（用于实际发邮件时使用）
    emailRemark: EMAIL_REMARK_CONTENT, // 邮件说明（作答说明）
    emailTemplate: '', // 邮件模板
    smsContent: '', // 短信内容
    smsSubject: '', // 短信标题
    emailVersion: 'all', // 邮件版本
    loginType: 0, // 登录方式
});
const formRef = ref();

const editorComponent = ref();
const isFocus = ref(false);
const smsParamsList = ref([]); // 短信动态参数
const emailParamsList = ref([]); // 邮件动态参数
const emailTemplateCount = ref(0); // 邮件正文总字数（不带标签）
const emailCountLoading = ref(0);

const examFieldList = computed(() => {
    return emailAdditionalList.value.filter((x: any) => emailAdditionalValue.value.includes(x.encryptId));
}); // 邮件考试自定义字段
const emailAdditionalList = ref<any>([]); // 全部字段
const emailAdditionalValue = ref<any>([]); // 已选字段
// const isLoading = ref(true);
const qrcodeDataUrl = ref(); // 二维码的base64
const examUrl = ref(); // 考试链接
const qrcodeUri = ref();

// ------------------------------ computed ------------------------------
// const emailWordsCount = computed(() => formData.value.emailRemarks.replace(/\n/g, '').length + emailTemplateCount.value);
const emailWordsCount = computed(() => emailTemplateCount.value);
const emailDescribeWordsCount = computed(() => formData.value.emailRemarks?.replace(/\n/g, '').length);
const smsWordsCount = computed(() => (SMS_NOT_MODIFIABLE_CONTENT + getSmsContentText(formData.value.smsContent)).length);
// const maxCount = computed(() => (formData.value.noticeTypes === 0 ? EMAIL_MAX_COUNT : SMS_MAX_COUNT));

// ------------------------------ watch ------------------------------
watch(
    () => formData.value.emailTemplate,
    (v) => {
        getEmailCharacterCount();
    },
    {
        immediate: true,
    }
);
const emailPreviewData = ref<any>({});
async function getEmailPreview() {
    try {
        const { code, data } = await getExamineeEmailPreview({ encryptExamId: props.encryptExamId });
        if (code === 0) {
            emailPreviewData.value = data || {};
        }
    } catch (error: any) {
        Toast.danger(error.message || '获取预览场次基本信息失败，请刷新页面重试！');
    }
}
// watch(
//     () => formData.value.noticeTypes,
//     (v) => {
//         if (v === 0) {
//             setEmailCountLoading();
//         }
//     }
// );

// ------------------------------ lifecycle ------------------------------
getTemplateDetail();
getOption();
getConfig();
getExamUrl();
getEmailPreview();
// ------------------------------ method ------------------------------
async function uploadQrcode() {
    const errorHandler = () => {
        Toast.danger('上传场次邀请二维码失败，请刷新页面重试！');
    };
    try {
        const filename = 'invite';
        const file = dataURLtoFile(qrcodeDataUrl.value, filename);
        const formData = new FormData();
        formData.append('file', file);
        const res = await _fileUploadAuth(formData);
        if (res.code === 0) {
            qrcodeUri.value = `${window.location.origin}${res.data.uri}`;
        } else {
            errorHandler();
        }
    } catch (error) {
        errorHandler();
    }
}
async function getExamUrl() {
    const errorHandler = () => {
        Toast.danger('生成场次链接失败，请刷新页面重试！');
    };
    const params = {
        encryptExamId: props.encryptExamId,
    };
    try {
        const res = await getExamineeExamUrl(params);
        if (res.code === 0) {
            examUrl.value = res.data;
            qrcodeDataUrl.value = await drawQrcode(res.data, 82);
            uploadQrcode();
        } else {
            errorHandler();
        }
    } catch (error) {
        errorHandler();
    }
}
function getOption() {
    _noticeEmailConfigOption({ encryptExamId: props.encryptExamId }).then(({ code, data }) => {
        if (code === 0) {
            emailAdditionalList.value = data || [];
        }
    });
}
function getConfig() {
    _noticeEmailConfigList({ encryptExamId: props.encryptExamId })
        .then(({ code, data }) => {
            if (code === 0) {
                emailAdditionalValue.value = (data || []).map((x: any) => x.encryptId);
            }
        })
        .finally(() => {
            // isLoading.value = false;
        });
}

// 插入标签
function insertContent(text = '') {
    editorComponent.value.insert(`<input class="mce-insert-params" type="button" value="${text}" />`);
}
// 获取邮件正文字符字数
function getEmailCharacterCount() {
    const len1 = (editorComponent.value?.getContent('text') || '')?.replace(/\n/g, '')?.length; // 纯文本总字数
    const len2 = getEmailAllParamsCount(editorComponent.value?.getContent() || ''); // 参数总字数
    emailTemplateCount.value = len1 + len2;
}

function handleDeleteKeyup() {
    formData.value.emailTemplate = editorComponent.value.getContent();
}

function setEmailCountLoading() {
    emailCountLoading.value += 1;
    setTimeout(() => {
        getEmailCharacterCount();
        emailCountLoading.value -= 1;
    }, 800);
}

// 获取详情
function getTemplateDetail() {
    emailCountLoading.value += 1;
    _examineeNoticeDetail({ encryptExamId: props.encryptExamId })
        .then(({ code, data }) => {
            if (code === 0) {
                fillContent(data || {}); // 回填数据

                // 回填数据后，计算一下邮件字数
                setEmailCountLoading();
            }
        })
        .finally(() => {
            emailCountLoading.value -= 1;
        });
}

// 回填数据
function fillContent(data: any) {
    const { noticeTypes, emailSubject, emailContent, emailRemarks, emailTemplate, smsContent, smsDynamicParams, emailDynamicParams, smsSubject, emailVersion, loginType } = data;

    smsParamsList.value = smsDynamicParams || SMS_DYNAMIC_PARAMS;
    emailParamsList.value = emailDynamicParams || EMAIL_DYNAMIC_PARAMS;

    formData.value = {
        noticeTypes: (noticeTypes ?? '0').split(','),
        smsSubject: SMS_DEFAULT_SUBJECT, // 短信标题
        emailSubject: emailSubject || '',
        emailContent: emailContent || '', // 邮件内容（用于实际发邮件时使用）
        emailRemarks: emailRemarks, // 邮件说明（作答说明）
        emailTemplate: emailTemplate || EMAIL_DEFAULT_CONTENT, // 邮件模板
        smsContent: smsContent || SMS_DEFAULT_CONTENT, // 短信内容
        emailVersion: emailVersion || 'all', // 邮件版本
        loginType: loginType || 0, // 登录方式：1验证码登录 2账号密码登录
    };
}

function onClose() {
    emit('update:modelValue', false);
}
async function sendNotice(params: any, withEmail: boolean) {
    const res = await _examineeNotice(params);
    if (res.code === 0) {
        Toast.success(withEmail ? '操作完成，邮件通知可能出现延迟，请稍后刷新页面查看' : '操作完成');
    }
    return res;
}
function generateParams() {
    let params: any = {
        encryptExamId: props.encryptExamId,
        ...formData.value,
        noticeTypes: formData.value.noticeTypes.join(),
        emailContent: renderEmailTemplate(
            {
                emailTemplate: formData.value.emailTemplate,
                emailRemarks: formData.value.emailRemarks?.replace(/\n/g, '<br>'),
                configList: examFieldList.value,
                examName: props.examName,
                examUrl: examUrl.value,
                qrcodeUri: qrcodeUri.value,
                emailVersion: formData.value.emailVersion,
                loginType: formData.value.loginType,
            },
            examUrl.value,
            emailPreviewData.value
        ),
    };
    if (props.isCheckTotalAll) {
        const filtersClone = cloneDeep(props.filters);
        delete filtersClone.noticeTypes;
        params = {
            ...params,
            ...filtersClone,
            noticeTypesQueryParam: props.filters.noticeTypes,
            excludeEncryptIds: props.keysExcludes,
        };
    } else {
        params.encryptIds = props.keysSelected;
    }
    return params;
}
async function handleBeforeConfirm() {
    if (formData.value.noticeTypes.includes('0')) {
        if (!formData.value.emailSubject) {
            Toast.danger('请填写邮件主题！');
            return false;
        }
        if (emailWordsCount.value > EMAIL_MAX_COUNT) {
            Toast.danger('邮件模板内容字数超出最大值！');
            return false;
        }
        if (!examUrl.value) {
            Toast.danger('未获取到场次链接，无法发送邮件，请刷新页面重试！');
            return false;
        }
        if (!qrcodeUri.value) {
            Toast.danger('未获取到场次邀请二维码图片地址，无法发送邮件，请刷新页面重试！');
            return false;
        }
    }
    if (formData.value.noticeTypes.includes('1')) {
        if (!formData.value.smsContent) {
            Toast.danger('请填写短信正文！');
            return false;
        }
        if (smsWordsCount.value > SMS_MAX_COUNT) {
            Toast.danger('短信模板内容字数超出最大值');
            return false;
        }
    }
    const configSaveRes = await _noticeEmailConfigSave({
        encryptExamId: props.encryptExamId,
        configList: examFieldList.value,
    });
    if (configSaveRes.code === 0) {
        const finalParams = generateParams();
        const validateRes = await _validateNotice(finalParams);
        if (validateRes.code === 0) {
            const existRes = await _existNoticed({ encryptExamId: props.encryptExamId });
            if (existRes.code === 0) {
                if (existRes.data) {
                    const sendRes = await sendNotice(finalParams, formData.value.noticeTypes.includes('0'));
                    if (sendRes.code === 0) {
                        onClose();
                        emit('success');
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    const confirmRes: any = await new Promise<any>((resolve, reject) => {
                        Dialog.open({
                            title: '提示',
                            type: 'warning',
                            content: '发送通知后，场次安排信息将不能修改，是否确定要发送？',
                            layerClosable: false,
                            async beforeConfirm() {
                                const sendRes = await sendNotice(finalParams, formData.value.noticeTypes.includes('0'));
                                if (sendRes.code === 0) {
                                    resolve(sendRes);
                                    return true;
                                } else {
                                    return false;
                                }
                            },
                            cancel() {
                                resolve({ code: 1 });
                            },
                        });
                    });
                    if (confirmRes.code === 0) {
                        onClose();
                        emit('success');
                        return true;
                    } else {
                        return false;
                    }
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    } else {
        return false;
    }
}

const dialogPreviewEmailShow = ref(false);
function previewEmail() {
    dialogPreviewEmailShow.value = true;
}
</script>

<style lang="less">
.dialog-notice {
    --tox-toolbar-background-color: #f8f8f8;
    --text-color-sub-title: #808080;

    background-color: transparent;
    &.dialog-notice-with-email {
        .b-dialog-modal {
            height: 100vh;
            .b-dialog-body {
                display: flex;
                flex-direction: column;
                & > * {
                    flex-shrink: 0;
                }
                .email-section-wrap {
                    display: flex;
                    flex-direction: column;
                    flex-grow: 1;
                    & > * {
                        flex-shrink: 0;
                    }
                    .email-template-wrap {
                        display: flex;
                        flex-direction: column;
                        flex-grow: 1;
                        & > * {
                            flex-shrink: 0;
                        }
                    }
                    .email-version-radio-group {
                        margin-left: 12px;
                    }
                }
            }
        }
    }
    .custom-checkbox-wrap {
        display: flex;
        .card-checkbox.b-checkbox-disabled {
            .custom-checkbox-card {
                cursor: not-allowed;
            }
        }
        .custom-checkbox-card {
            padding: 7px 16px 7px 17px;
            border: 1px solid var(--gray-color-3);
            cursor: pointer;
            border-radius: 8px;
            .custom-checkbox-card-title {
                display: flex;
                align-items: center;
            }

            .b-checkbox {
                margin-right: 7px;
            }

            &:hover,
            &.custom-checkbox-card-checked {
                border-color: var(--primary-color-6);
            }
        }
    }
    .section-title {
        margin-bottom: 12px;
        .title-action {
            display: flex;
            align-items: center;
            .template-counter {
                margin-right: 16px;
            }
        }
    }

    .email-template-wrap {
        border: 1px solid var(--gray-color-3);
        border-radius: 8px;
        overflow: hidden;
        position: relative;

        &.focusing {
            border-color: var(--primary-color-6);
        }
        .email-subject {
            position: absolute;
            top: 60px;
            border: none;
            border-bottom: 1px solid var(--gray-color-2);
            border-radius: 0;
            z-index: 1;
            box-shadow: none;
            width: calc(100% - 24px);
            left: 0;
            right: 0;
            margin: auto;
            padding: 0 0 12px;
            .b-input-prefix {
                color: var(--text-color-sub-title);
            }
        }

        // 重置 tinymce 样式
        .tox-tinymce {
            border: none;
            border-radius: 0;
            flex-grow: 1;
            .tox-toolbar {
                background-color: var(--tox-toolbar-background-color);
            }
            .tox-sidebar-wrap {
                padding: 66px 12px 0;
                .tox-edit-area > label {
                    display: none !important;
                }
            }
        }

        //
        .login-section {
            padding: 0 24px;
            margin: 0 20px 20px;
            background: #f8f8f8;
            border-radius: 4px;

            .tips-wrap {
                padding: 24px 0;
                p {
                    font-size: 14px;
                    color: #5d7080;
                    line-height: 22px;
                }
            }
        }

        .btn-section {
            display: flex;
            margin: 24px 20px 60px 0;
            span {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 120px;
                height: 36px;
                border-radius: 18px;
                opacity: 0.5;
                & + span {
                    margin-left: 12px;
                }
                &.reject-btn {
                    color: #1f384c;
                    border: 1px solid;
                    background-color: #fff;
                }
                &.accept-btn {
                    color: #fff;
                    background-color: #37c2bc;
                }
            }
        }

        .intro-section {
            position: relative;
            background-color: #f8f8f8;
            min-height: 100px;
            .input-wrap {
                height: 100%;
                position: absolute;
                left: 0;
                top: 0;
                & > label {
                    height: 100%;
                }
            }
            textarea {
                height: 100%;
                min-height: 100px;
                padding: 0 0 24px;
                border: none;
                background: transparent;
                line-height: 24px;
                font-size: 14px;
                color: #5d7080;
                overflow: hidden;
            }
            .hidden-text {
                visibility: hidden;
                padding: 0 0 24px;
                line-height: 24px;
                font-size: 14px;
                white-space: pre-wrap;
            }
        }
    }
}
</style>
