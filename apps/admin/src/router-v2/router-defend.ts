import type { Router } from 'vue-router';
import { _userCurrent } from '@/services/api/common';
import { useStore } from '@/store/index';
import { AdminModuleCode } from './types';

// 更新用户信息
async function fetchUserInfo() {
    try {
        const { code, data } = await _userCurrent();
        if (code === 0) {
            useStore().user.setUserInfo(data);
        }
    } catch (e) {
        Toast.danger('获取用户信息失败，请刷新重试');
    }
}

export default function routerDefend(router: Router) {
    router.beforeEach(async (to, from, next) => {
        const $store = useStore();
        const hasInitUserInfo = $store.user.hasInitUserInfo;

        // 兜底逻辑 如果访问404页面 直接跳转首页
        if (to.name === 'page404') {
            next({ name: AdminModuleCode.workbench }); // 重定向到首页
        }

        if (!hasInitUserInfo && !window.location.href?.includes('/dashboard/extend/icon')) {
            await fetchUserInfo();
        }

        next();
    });

    router.afterEach(() => {});
}
