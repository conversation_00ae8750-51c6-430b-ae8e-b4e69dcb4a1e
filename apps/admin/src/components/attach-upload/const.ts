export interface TfileData {
    encryptId: string;
    name: string;
    type: string;
    size: number;
    uri: string;
    url: string;
    showSize: number;
    percentage: number;
    status: string;
    width: number;
    height: number;
    initialProportion: number;
}

export const defaultFileData: TfileData = {
    encryptId: '',
    name: '',
    type: '',
    size: 0,
    uri: '',
    url: '',
    showSize: 0,
    percentage: 0,
    status: '',
};

// 控制图片大小
export function setImgScaleCalculation({ type = 'width', maxWidth = 676, maxHeight = 400, scale = 1, val = 0 }) {
    let inputVal = val;
    let width = 0;
    let height = 0;
    if (type === 'width') {
        inputVal = inputVal > maxWidth ? maxWidth : inputVal;
        width = inputVal;
        height = inputVal / scale;
    }

    if (type === 'height' || height > maxHeight) {
        inputVal = inputVal > maxHeight ? maxHeight : inputVal;
        height = inputVal;
        width = inputVal * scale;
    }

    return {
        width: Math.round(width),
        height: Math.round(height),
    };
}

export function getUploadFileInfo(file: any) {
    return new Promise((resolve, reject) => {
        try {
            // 创建FileReader对象
            const reader = new FileReader();

            reader.onload = function (event: any) {
                // 当图片加载完成后，创建一个Image对象
                const img = new Image();
                img.onload = function () {
                    resolve(img);
                };
                // 设置Image对象的src属性为FileReader读取的结果（DataURL）
                img.src = event.target.result;
            };

            // 以DataURL的形式读取文件内容
            try {
                reader.readAsDataURL(file.raw || file);
            } catch (err) {}
        } catch (error) {
            reject(error);
        }
    });
}
