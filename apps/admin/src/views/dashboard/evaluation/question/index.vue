<template>
    <b-layout type="content">
        <b-layout direction="vertical">
            <b-section type="search">
                <b-form
                    labelAlign="inner"
                    :model="queryData"
                    useGrid
                    :gridProps="{ gap: 10 }"
                    mode="embed"
                    layout="inline"
                    @submit="handleSearch"
                >
                    <b-form-item label="题目ID">
                        <b-input
                            v-model.trim="queryData.idStr"
                            trimBeforePaste
                            allowClear
                            placeholder="精确查询"
                            :maxLength="19"
                        />
                    </b-form-item>
                    <b-form-item label="题干">
                        <b-input
                            v-model.trim="queryData.questionTitle"
                            trimBeforePaste
                            :maxLength="500"
                            allowClear
                            placeholder="模糊搜索"
                        />
                    </b-form-item>
                    <b-form-item label="测评产品">
                        <b-select
                            v-model="queryData.productId"
                            :options="[{ label: '全部', value: -1 }, ...productListQuery.data.value]"
                            allowSearch
                            placeholder="请选择"
                            @change="handleChange"
                        />
                    </b-form-item>
                    <b-form-item label="测评维度">
                        <b-cascader
                            v-model="queryData.encryptDimensionId"
                            :triggerProps="{ contentClass: 'question-cascader-content' }"
                            :options="dimensionFilterList"
                            :fieldNames="{ value: 'encryptId', label: 'name' }"
                            expandTrigger="hover"
                            placeholder="请选择"
                            allowSearch
                        />
                    </b-form-item>
                    <b-form-item label="题型">
                        <b-select
                            v-model="queryData.questionType"
                            :options="queryData.productId !== -1 ? questionsTypeQuery.data.value : []"
                            allowSearch
                            placeholder="请选择"
                        />
                    </b-form-item>
                    <b-form-item label="状态">
                        <b-select v-model="queryData.questionStatus" placeholder="请选择">
                            <b-option
                                v-for="item in DIMENSION_STATUS_LIST"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            />
                        </b-select>
                    </b-form-item>
                    <b-form-item alwaysShow showMore>
                        <b-space>
                            <b-button type="outline" status="primary" htmlType="submit">
                                查询
                            </b-button>
                        </b-space>
                    </b-form-item>
                </b-form>
            </b-section>
            <b-section type="title" justify="between">
                <b-title size="large">
                    题目列表
                </b-title>
                <b-space>
                    <b-dropdown>
                        <b-button type="primary">
                            新增题目 <SvgIcon name="arrow-down" class="button-icon" />
                        </b-button>
                        <template #content>
                            <b-dropdown-option style="width: 98px; justify-content: center" @click="() => handlerAction(undefined, true)">
                                批量新增
                            </b-dropdown-option>
                            <b-dropdown-option style="width: 98px; justify-content: center" @click="() => handlerAction(undefined)">
                                单条新增
                            </b-dropdown-option>
                        </template>
                    </b-dropdown>
                </b-space>
            </b-section>
            <b-section type="list" direction="vertical">
                <b-table
                    ref="listTable"
                    :queryFun="getList"
                    :columns="columns"
                    fullHeight
                    stickyHeader
                    :border="false"
                    :scroll="{ x: '100%' }"
                    tableLayoutFixed
                    :pagination="{ pageType: 'server', pageSize: 10, showTotal: true, showSizer: true, pageSizeOpts }"
                >
                    <template #td-questionStatus="{ raw }: { raw: QuestionListItem }">
                        <div class="account-status-cell">
                            <b-avatar :size="6" :style="{ backgroundColor: STATUS_COLOR_MAP[raw.questionStatus] }" />
                            {{ findLabel(DIMENSION_STATUS_LIST, raw.questionStatus) }}
                        </div>
                    </template>
                    <template #td-operation="{ raw }: { raw: QuestionListItem }">
                        <b-action @click="() => handlerAction(raw.encryptId)">
                            编辑
                        </b-action>
                        <b-action v-if="!(raw as any).parentId" @click="(e: MouseEvent) => confirmDeactivateQuestion(e, raw)">
                            {{ raw.questionStatus === Status.DISABLE ? '启用' : '停用' }}
                        </b-action>
                    </template>
                </b-table>
            </b-section>
        </b-layout>
        <!-- 添加编辑功能 -->
        <ActionDialog
            v-if="actionDialogOpened"
            v-model="actionDialogOpened"
            :question="question"
            :productList="productListQuery.data.value"
            @close="(type) => handleActionClose(false, type)"
        />
        <BatchDialog
            v-if="batchDialogOpened"
            v-model="batchDialogOpened"
            :extraUploadData="{}"
            :url="{
                fileUpload: _questionUpload,
                importBegin: _questionStart,
                importProgress: _questionProgress,
                errorDataDownload: _questionError,
            }"
            :productList="productListQuery.data.value"
            @close="(type) => handleActionClose(true, type)"
            @success="() => listTable.query({ page: 1 })"
        />
    </b-layout>
</template>

<script setup lang="tsx">
import type { Question, QuestionListItem } from '@/services/api/evaluation-question/type'
import type { ITableColumnDataRaw, ITablePagination } from '@boss/design'
import type { MapItem } from './mock-util'
import type { FilterData } from './type'
import { PRODUCT_CAREER_MOTIVATION_CODE, PRODUCT_FINANCE_PERSONNEL_CODE, PRODUCT_GBA_CODE, PRODUCT_HOTS_CODE } from '@/constant/evaluation'
import { pageSizeOpts } from '@/constant/index'
import { _dimensionV2Option } from '@/services/api/dimension'
import {
    _financeCheckDeactivate,
    _questionError,
    _questionInfo,
    _questionList,
    _questionProgress,
    _questionStart,
    _questionUpdateStatus,
    _questionUpload,
} from '@/services/api/evaluation-question/index'
import { Status } from '@/services/api/evaluation-question/type'
import { _getProductOptions } from '@/services/api/project'
import ResultService from '@/services/api/result'
import { useQueryLite } from '@crm/vueuse-pro'

import ActionDialog from './components/action-dialog.vue'
import BatchDialog from './components/batch-dialog.vue'
import { DEFAULT_QUESTION, DIMENSION_STATUS_LIST, questionTypeGroup, STATUS_COLOR_MAP } from './constant'
import { ALL_VALUE, deepClone, findLabel } from './mock-util'

const columns: Partial<ITableColumnDataRaw>[] = [
    {
        label: '题目ID',
        field: 'id',
    },
    {
        label: '测评产品',
        field: 'productName',
        ellipsis: {
            lineNum: 2,
        },
    },
    {
        label: '测评维度',
        field: 'dimensionName',
        ellipsis: {
            lineNum: 2,
        },
    },
    {
        label: '题型',
        field: 'questionTypeDesc',
    },
    {
        label: '题干',
        field: 'questionTitle',
        ellipsis: {
            lineNum: 2,
        },
    },
    {
        label: '状态',
        field: 'questionStatus',
    },
    {
        label: '创建时间',
        field: 'createTime',
    },
    {
        label: '创建人',
        field: 'creatorName',
    },
    {
        label: '操作',
        field: 'operation',
    },
]

const question = ref<Partial<Question>>({ ...DEFAULT_QUESTION })

const actionDialogOpened = ref(false)
const batchDialogOpened = ref(false)
const listTable = ref()

const productListQuery = useQueryLite({
    keys: () => ['productListQuery'],
    fetcher: () =>
        _getProductOptions({
            source: 2,
        }),
    select: (raw) => {
        return (
            raw?.data.map((x: any) => ({
                ...x,
                value: x.id,
                label: x.name,
            })) || []
        )?.filter((item: any) => ![PRODUCT_HOTS_CODE, PRODUCT_GBA_CODE].includes(item.id))
    },
})

const questionsTypeQuery = useQueryLite({
    keys: () => ['questionsTypeQuery'],
    fetcher: ResultService.getQuestionsType,
    select: (raw) => {
        if (!queryData.value?.productId) {
            return []
        }
        const productKey = Object.keys(questionTypeGroup).includes(String(queryData.value?.productId || 1)) ? queryData.value?.productId || 1 : 1
        const questionTypeFilter: number[] = deepClone(questionTypeGroup)[String(productKey)]

        return [
            { label: '全部', value: -1 },
            ...(raw?.data
                .filter(i => questionTypeFilter.includes(i.questionType))
                .map(item => ({
                    label: item.questionTypeDesc,
                    value: item.questionType,
                })) || []),
        ]
    },
})

const dimensionList = ref<MapItem[]>([])
const dimensionFilterList = computed(() => {
    return baseQueryData.productId ? dimensionList.value : []
})

function handleChange(value: number) {
    if (value === Number(ALL_VALUE)) {
        queryData.value.questionType = undefined
    } else {
        queryData.value.questionType = ALL_VALUE
    }
}

const baseQueryData: FilterData = {
    idStr: undefined,
    questionTitle: undefined,
    productId: ALL_VALUE,
    encryptDimensionId: undefined,
    questionStatus: Status.ENABLE,
}
const queryData = ref(baseQueryData)

async function toggleStatus(row: QuestionListItem) {
    try {
        await _questionUpdateStatus({
            encryptId: String(row.encryptId),
            status: row.questionStatus === Status.ENABLE ? Status.DISABLE : Status.ENABLE,
        })
        Toast.success('操作成功')
        listTable.value.query()
        return true
    } catch (error) {
        Toast.danger('操作失败')
        return false
    }
}

async function confirmDeactivateQuestion(e: MouseEvent, row: QuestionListItem) {
    // 如果是 金融Junior人才胜任力 或者 职业动机 测评 需要前置判断是使用
    if (row.productId === PRODUCT_FINANCE_PERSONNEL_CODE || row.productId === PRODUCT_CAREER_MOTIVATION_CODE) {
        const res = await _financeCheckDeactivate({
            encryptId: row.encryptId,
        } as any)

        if (res.code === 1) {
            return false
        }
    }

    const content = `您确定要${row.questionStatus === Status.DISABLE ? '启用' : '停用'}题目吗？`
    const subContent = row.questionStatus === Status.ENABLE && '停用题目，不影响历史考题及数据统计'

    Dialog.open({
        title: '提示',
        type: 'warning',
        content: () => (
            <div>
                {content}
                {subContent}
            </div>
        ),
        beforeConfirm() {
            return toggleStatus(row)
        },
    })
}

async function getList({ page, pageSize }: Partial<ITablePagination>) {
    const { data } = await _questionList({
        idStr: queryData.value.idStr ? queryData.value.idStr : undefined,
        productId: queryData.value.productId !== -1 ? queryData.value.productId : undefined,
        questionType: queryData.value.questionType !== -1 ? queryData.value.questionType : undefined,
        questionStatus: queryData.value.questionStatus !== -1 ? queryData.value.questionStatus : undefined,
        questionTitle: queryData.value.questionTitle,
        encryptDimensionId: queryData.value.encryptDimensionId !== '-1' ? queryData.value.encryptDimensionId : undefined,
        page: page!,
        pageSize: pageSize!,
    })

    return {
        total: data.total,
        data: data.list,
    }
}

async function handlerAction(value?: string, batch?: boolean) {
    if (value) {
        const { data } = await _questionInfo({
            encryptId: value,
        })
        question.value = deepClone({
            ...DEFAULT_QUESTION,
            ...data,
            scrollId: value,
        })
    } else {
        question.value = deepClone(DEFAULT_QUESTION)
    }
    if (batch) {
        batchDialogOpened.value = true
        return
    }
    actionDialogOpened.value = true
}

function handleActionClose(batch?: boolean, type: 'submitSuccess' | 'cancel' = 'cancel') {
    question.value = { ...DEFAULT_QUESTION }
    if (type === 'submitSuccess') {
        queryData.value = { ...baseQueryData }
        listTable.value.query({ page: 1 })
    }
    if (batch) {
        batchDialogOpened.value = false
        return
    }
    actionDialogOpened.value = false
}

function handleSearch() {
    listTable.value.query({ page: 1 })
}

watch(
    () => queryData.value.productId,
    async () => {
        if (queryData.value.productId === ALL_VALUE) {
            dimensionList.value = []
            queryData.value.encryptDimensionId = undefined
            return
        }
        const { data } = await _dimensionV2Option({
            productId: String(queryData.value.productId),
        })
        dimensionList.value = [
            {
                name: '全部',
                encryptId: '-1',
            },
            ...(data || []),
        ]
        queryData.value.encryptDimensionId = String(ALL_VALUE)
    },
)

onMounted(() => {
    listTable.value.query()
})
</script>

<style scoped lang="less">
.account-status-cell {
    display: flex;
    align-items: center;
    .b-avatar {
        margin-right: 8px;
    }
}
.button-icon {
    margin-left: 9px;
}
</style>

<style lang="less">
.question-cascader-content {
    .b-cascader-option-label {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .b-cascader-list-empty {
        width: 180px;
    }
}
</style>
