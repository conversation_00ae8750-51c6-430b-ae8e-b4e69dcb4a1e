<template>
    <!-- 新增编辑测评维度 -->
    <b-drawer
        v-model="visible"
        :title="dialogTitle"
        :width="1000"
        unmountOnClose
        wrapClass="dialog-add-evaluation"
        :confirmIsLoading="confirmIsLoading"
        :layerClosable="false"
        :beforeConfirm="onConfirm"
        :beforeCancel="onBeforeCancel"
    >
        <b-form ref="formRef" :model="formData" autoLabelWidth layout="vertical">
            <b-layout class="form-layout" direction="horizontal">
                <div class="form-content">
                    <div class="base">
                        <b-form-item
                            label="测评产品"
                            :disabled="!isCreate"
                            required
                            asteriskPosition="end"
                            :rules="[{ required: true, message: '请选择测评产品' }]"
                            field="productId"
                        >
                            <b-select
                                v-model="formData.productId"
                                :options="productList"
                                placeholder="请选择"
                                allowClear
                                allowSearch
                                @change="
                                    (v: any) => {
                                        handleProductIdChange(v, true);
                                    }
                                "
                            />
                        </b-form-item>
                        <b-form-item label="题型" required asteriskPosition="end" :rules="[{ required: true, message: '请选择题型' }]" field="questionType">
                            <b-select v-model="formData.questionType" :options="questionsTypeOptions.filterList" placeholder="请选择" allowClear allowSearch />
                        </b-form-item>
                        <b-form-item
                            v-if="![PRODUCT_LEADERSHIP_SKILLS_CODE, PRODUCT_FINANCE_PERSONNEL_CODE, PRODUCT_DISC_CODE, PRODUCT_CAREER_MOTIVATION_CODE].includes(formData.productId)"
                            label="测评维度"
                            :disabled="!isCreate"
                            required
                            asteriskPosition="end"
                            :rules="[{ required: true, message: '请选择测评维度' }]"
                            field="encryptDimensionId"
                        >
                            <Dimension v-model="formData.encryptDimensionId" :isCreate="isCreate" :formData="formData" :isTowLevel="isDimensionTowLevel" />
                        </b-form-item>
                        <Subpackage
                            v-if="[PRODUCT_COGNITIVE_ABILITY_CODE].includes(formData.productId)"
                            v-model:value="formData.paperGroup"
                            :isCreate="isCreate"
                            field="paperGroup"
                        />
                        <b-form-item
                            v-else-if="![PRODUCT_LEADERSHIP_SKILLS_CODE].includes(formData.productId)"
                            label="所属题组"
                            required
                            asteriskPosition="end"
                            :rules="[{ required: true, message: '请选择题型' }]"
                            field="questionGroup"
                        >
                            <b-select v-model="formData.questionGroup" :options="questionGroupList" placeholder="请选择" />
                        </b-form-item>
                        <PreviewIframe :data="formData" />
                    </div>
                    <div class="extra-scroll-wrap">
                        <div class="extra">
                            <component
                                :is="queryTemplate"
                                ref="forcedChoiceRef"
                                v-model:formData="formData"
                                :isCreate="isCreate"
                                :queryTypeOption="questionsTypeOptions.allList"
                                @scrollToBottom="scrollToBottom"
                            />
                        </div>
                    </div>
                </div>
            </b-layout>
        </b-form>
    </b-drawer>
</template>

<script setup lang="ts">
import type { Question } from '@/services/api/evaluation-question/type';
import type { _FormComponent } from '@boss/design';
import {
    PRODUCT_CAREER_MOTIVATION_CODE,
    PRODUCT_COGNITIVE_ABILITY_CODE,
    PRODUCT_DEDICATION_RESEARCH_CODE,
    PRODUCT_DISC_CODE,
    PRODUCT_FINANCE_PERSONNEL_CODE,
    PRODUCT_HIPO_CODE,
    PRODUCT_LEADERSHIP_SKILLS_CODE,
    PRODUCT_MENTAL_HEALTH_TEAM_CODE,
    PRODUCT_POLITICAL_LITERACY_CODE,
} from '@/constant/evaluation';
import { _questionSave } from '@/services/api/evaluation-question';
import { useFormValidator } from '@crm/vueuse-pro';
import { cloneDeep } from 'es-toolkit';

import PreviewIframe from '../components/preview-iframe/index.vue';
import { QUESTION_TYPE_CONFIG, questionTypeGroup } from '../constant';
import Dimension from './form-components/dimension.vue';
import Subpackage from './form-components/subpackage.vue';

const visible = defineModel({ required: true, default: false });
const props = defineProps<{
    question: Partial<Question>;
    productList: Array<any>;
}>();

const emit = defineEmits<{
    close: [type: 'submitSuccess' | 'cancel'];
}>();

const questionGroupList = [
    { label: '1', value: 1 },
    { label: '2', value: 2 },
    { label: '3', value: 3 },
    { label: '4', value: 4 },
    { label: '5', value: 5 },
];

const formRef = ref<_FormComponent>();
const confirmIsLoading = ref(false);
const formData = ref<Partial<Question>>(cloneDeep(props.question));
const isCreate = computed(() => !props.question?.encryptId);
const dialogTitle = computed(() => `${props.question?.encryptId ? '编辑' : '新增'}题目`);
const isDimensionTowLevel = ref(false);
const queryTemplate = computed(() => {
    const type = formData.value.questionType || 1;
    return QUESTION_TYPE_CONFIG[type].template;
});
const questionsTypeOptions = ref<{ allList: any[]; filterList: any[] }>({
    allList: [],
    filterList: [],
});

const forcedChoiceRef = ref();

const { validateAll } = useFormValidator({ formRef });

// 获取题型数据
async function getQuestionsTypeList() {
    const { code, data } = await Invoke.common.getQuestionType({});
    if (code === 0 && data && data.length) {
        const list = data.map((item: any) => ({
            label: item.questionTypeDesc,
            value: item.questionType,
        }));
        questionsTypeOptions.value.allList = list;
    }
}

// 过滤产品可选题型
function questionsTypeOptionsFilter(val: number = 0) {
    const v = val ?? formData.value.productId;
    const newFilterData: any[] = [];
    if (v) {
        const questionTypeGroupList = questionTypeGroup && questionTypeGroup[v] ? questionTypeGroup[v] : [9];

        questionTypeGroupList.forEach((type) => {
            const data = questionsTypeOptions.value.allList.find((obj: any) => obj.value === type);
            if (data) {
                newFilterData.push(data);
            }
        });
        questionsTypeOptions.value.filterList = newFilterData;
    }

    return newFilterData;
}

async function handleProductIdChange(v: number, isInit: any) {
    const newFilterData = questionsTypeOptionsFilter(v);

    // 设置当前产品可选维度类型
    isDimensionTowLevel.value = [PRODUCT_MENTAL_HEALTH_TEAM_CODE, PRODUCT_HIPO_CODE, PRODUCT_DEDICATION_RESEARCH_CODE, PRODUCT_POLITICAL_LITERACY_CODE].includes(v);

    // 创建新题型场景下 过滤不同题型字段
    if (isInit) {
        formData.value.questionGroup = [PRODUCT_LEADERSHIP_SKILLS_CODE].includes(v) ? undefined : 1;
        formData.value.encryptDimensionId = '';
        formData.value.questionType = newFilterData.length <= 1 ? newFilterData[0]?.value : undefined;
    }
}

async function onConfirm() {
    const newFormData = {
        ...formData.value,
    };

    // 判断是否有空选项 （如果有空选项，需要去除）
    const isEmptyOption = newFormData?.questionOptionList?.find((item: any) => {
        return item.optionName === '' && item.files.length === 0;
    });
    if (isEmptyOption) {
        Object.assign(newFormData, {
            questionOptionList: null,
        });
    }

    try {
        if (!(await validateAll())) {
            return false;
        }
        const { code } = await _questionSave(newFormData as Question);
        if (code === 0) {
            Toast.success('保存成功');
            emit('close', 'submitSuccess');
        } else {
            return false;
        }
    } catch (error) {
        return true;
    } finally {
        confirmIsLoading.value = false;
    }
}

function onBeforeCancel() {
    emit('close', 'cancel');
    return false;
}

function scrollToBottom() {
    const container = document.querySelector('.extra');
    if (container) {
        container.scrollTop = container.scrollHeight;
    }
}

onMounted(async () => {
    await getQuestionsTypeList();
});

watch(
    () => [props.question, questionsTypeOptions.value.allList.length],
    () => {
        if (props.question.productId) {
            handleProductIdChange(props.question.productId, false);
        }

        if (!questionsTypeOptions.value.filterList.length) {
            questionsTypeOptionsFilter(props.question.productId);
        }
    },
    { immediate: true, deep: true }
);
</script>

<style scoped lang="less">
.dialog-add-evaluation {
    .form-content {
        width: 100%;
        height: calc(100vh - 144px);
        overflow: hidden;
        display: flex;

        .base {
            width: 400px;
            padding: 19px 24px 4px;
            border-radius: var(--border-radius-xlarge);
            background: var(--gray-color-1);
            overflow-y: auto;
        }
        .extra-scroll-wrap {
            margin-left: 20px;
            flex: 1;
            overflow-y: scroll;
            overflow-x: hidden;
        }
        .extra {
            width: 100%;
        }
    }
}
</style>
