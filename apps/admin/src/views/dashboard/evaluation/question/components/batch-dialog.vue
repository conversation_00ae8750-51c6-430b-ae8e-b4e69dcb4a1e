<template>
    <!-- 新增编辑测评维度 -->
    <b-dialog
        :title="title"
        :confirmIsLoading="loading"
        :showClose="stage !== 'importing'"
        :enableEscClose="stage !== 'importing'"
        unmountOnClose
        :layerClosable="false"
        wrapClass="dialog-wrap"
        :width="660"
        @close="resetUpload"
    >
        <b-form ref="form" :model="formData" autoLabelWidth layout="vertical">
            <div :class="`step-container stack center ${stageUpload ? 'justify-start' : ''}`">
                <template v-if="stageUpload">
                    <div class="group-s step-box">
                        <div class="center step-number">01</div>
                        <div class="detail-box first-step stack-s" style="gap: 10px">
                            <div>请选择测评产品</div>
                            <div class="select-wrap">
                                <b-select v-model="productId" :options="productListFilter()" placeholder="请选择" allowClear allowSearch />
                            </div>
                            <b-button status="info" type="outline" @click="handleDownTemplate">
                                <SvgIcon name="download" />
                                <div style="width: 3px" />
                                下载{{ templateName }}
                            </b-button>
                        </div>
                    </div>
                    <div class="group-s step-box">
                        <div class="center step-number">02</div>
                        <div class="detail-box second-step stack-s">
                            <div>将文件拖拽到此处，或点击上传</div>
                            <b-upload
                                ref="uploadInstance"
                                drag
                                :showFileList="false"
                                :action="getAction()"
                                :accept="accept"
                                :data="extraUploadData"
                                :beforeUpload="onBeforeUpload"
                                :onChange="onChange"
                                :onProgress="onProgress"
                                :onSuccess="onSuccess"
                                :onError="onError"
                            >
                                <div class="center group" style="gap: 20px">
                                    <div class="b-upload-plus-icon center">＋</div>
                                    <div>
                                        <div class="b-upload-text">拖拽或 <span class="b-upload-text-click">点击上传</span></div>
                                        <div class="b-upload-note">限制{{ maxSize }}M以下的{{ computedAcceptStr }}文件</div>
                                    </div>
                                </div>
                            </b-upload>
                            <div v-if="stage === 'uploading' || stage === 'uploaded'" class="upload-result">
                                <div class="center group justify-between upload-file">
                                    <div
                                        class="upload-progress"
                                        :style="{
                                            transition: 'all 1s',
                                            opacity: stage === 'uploading' ? 1 : 0,
                                        }"
                                    >
                                        <span
                                            class="rate"
                                            :style="{
                                                transition: 'all 0.2s',
                                                width: `${uploadPercent}%`,
                                            }"
                                        />
                                    </div>
                                    <div class="group" style="gap: 4px">
                                        <SvgIcon name="xlsx" class="file-list-icon" />
                                        <span class="name">{{ file.name || '' }}</span>
                                    </div>
                                    <b-tooltip v-if="stage === 'uploaded'" class="item" effect="dark" content="删除" placement="top">
                                        <SvgIcon name="delete" class="file-list-icon" @click="handleDelete" />
                                    </b-tooltip>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-if="authText" class="auth-wrap">
                        <b-checkbox v-model="confirmAuth">
                            {{ authText }}
                        </b-checkbox>
                    </div>
                </template>
                <template v-else-if="stage === 'importing'">
                    <div class="center">
                        <div class="center stack" style="gap: 32px">
                            <div class="import-process-text">正在导入…{{ importPercent }}%</div>
                            <div class="import-process-bar">
                                <div
                                    class="import-process-bar-content"
                                    :style="{
                                        transition: 'all 0.2s',
                                        width: `${importPercent}%`,
                                    }"
                                />
                            </div>
                        </div>
                    </div>
                </template>
                <template v-else-if="stage === 'done'">
                    <div class="center imported-panel stack">
                        <SvgIcon name="success" class="imported-panel-icon" />
                        <div class="imported-panel-success">
                            成功导入{{ successCount }}条，<span :class="{ 'imported-panel-error': errorCount > 0 }">失败{{ errorCount }}条</span>
                            <a v-if="errorCount > 0" class="imported-panel-button" @click="onErrorDownload">下载失败内容</a>
                        </div>
                        <span class="imported-panel-mark">{{ file.name }}</span>
                    </div>
                </template>
            </div>
        </b-form>
        <template #footer>
            <div class="group justify-end" style="gap: 10px">
                <b-button :disabled="cancelIsDisabled" type="outline" @click="onBeforeCancel">
                    {{ cancelText }}
                </b-button>
                <b-button :disabled="confirmIsDisabled" type="primary" @click="onBeforeConfirm">
                    {{ confirmText }}
                </b-button>
            </div>
        </template>
    </b-dialog>
</template>

<script setup lang="tsx">
import type { QuestionImportProgress } from '@/services/api/evaluation-question/type';
import type { AxiosResponseExtend } from '@/services/http';
import type { BatchData } from '../type';
import { PRODUCT_DISC_CODE, PRODUCT_FINANCE_PERSONNEL_CODE, PRODUCT_CAREER_MOTIVATION_CODE, PRODUCT_HIPO_CODE } from '@/constant/evaluation';
import { _questionTemplate } from '@/services/api/evaluation-question/index';
import qs from 'qs';

export type Stage = 'unUploaded' | 'uploading' | 'uploaded' | 'importing' | 'done';

type Response<T> = Promise<AxiosResponseExtend<T>>;

export interface BatchUrl {
    fileUpload: (params: File) => Response<{ key: string }>;
    importBegin: (params: Record<string, unknown>) => Response<QuestionImportProgress>;
    importProgress: (params: { key: string }) => Response<QuestionImportProgress>;
    errorDataDownload: (params: { key: string }) => Promise<void>;
}

const { title, templateName, url, maxSize, extraData, authText, accept, action, productList } = defineProps({
    title: {
        type: String,
        default: '批量导入',
    },
    templateName: {
        type: String,
        default: '模板',
    },
    maxSize: {
        type: Number,
        default: 20,
    },
    url: {
        type: Object,
        default: () => ({}),
    },
    extraData: {
        type: Object,
        default: () => ({}),
    },
    authText: {
        type: String,
        default: '',
    },
    accept: {
        type: String,
        default: '.xlsx',
    },
    action: {
        type: String,
        default: '/wapi/admin/import/evaluation/question/upload',
    },
    extraUploadData: {
        type: Object,
        default: () => ({}),
    },
    productList: {
        type: Array,
        default: () => [],
    },
});

const emit = defineEmits<{
    close: [type: 'submitSuccess' | 'cancel'];
    success: [];
}>();

const { fileUpload, importBegin, importProgress, errorDataDownload } = url;

const computedAcceptStr = computed(() => {
    return accept.split(',').join('和');
});

const formData = reactive<BatchData>({
    file: undefined,
});

const loading = ref(false);
const stage = ref<Stage>('unUploaded');
const form = ref();
const productId = ref(); // 测评产品id
const uploadInstance = ref();
const confirmAuth = ref(false);

const cancelText = computed(() => (stage.value === 'done' ? '继续上传' : '取消'));
const cancelIsDisabled = computed(() => stage.value === 'importing');
const confirmText = computed(() => (stage.value === 'done' ? '确定' : '开始导入'));
const confirmIsDisabled = computed(() => (stage.value !== 'done' && stage.value !== 'uploaded') || (!!authText && !confirmAuth.value));
const stageUpload = computed(() => stage.value === 'unUploaded' || stage.value === 'uploading' || stage.value === 'uploaded');

const successCount = ref(0);
const errorCount = ref(0);
const importPercent = ref(0); // 导入进度
function productListFilter() {
    return productList.filter((item) => ![PRODUCT_FINANCE_PERSONNEL_CODE, PRODUCT_CAREER_MOTIVATION_CODE, PRODUCT_DISC_CODE, PRODUCT_HIPO_CODE].includes(item.id));
}
async function rotateProcess() {
    if (!file.key) {
        return;
    }
    const { code, data } = await importProgress({
        key: file.key,
        productId: productId.value,
    });

    if (code === 0) {
        importPercent.value = Math.ceil(data.rate);
        if (data.complete === 1) {
            // 导入完成了ß

            setTimeout(() => {
                stage.value = 'done';
            }, 400);
            successCount.value = data.success || 0;
            errorCount.value = data.error || 0;
            if (data.success > 0) {
                // 导入成功回调
                emit('success');
            }
        } else {
            // 导入中
            setTimeout(() => {
                rotateProcess();
            }, 200);
        }
    } else {
        stage.value = 'uploaded';
    }
}
function handleDelete() {
    resetUpload();
}
function onErrorDownload() {
    errorDataDownload({ key: file.key });
}

const AVAILABLE_TYPE_LIST = ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'];
function onBeforeUpload(f: File) {
    if (!productId.value) {
        Toast.info('请选择测评产品');
        return false;
    }

    const fileSize = f.size / 1024 / 1024;
    const fileType = f.type;
    if (!AVAILABLE_TYPE_LIST.includes(fileType)) {
        Toast.danger({
            content: `文件格式错误`,
        });
        return false;
    }
    if (fileSize > maxSize) {
        Toast.danger({
            content: `文件超过${maxSize}M无法上传`,
        });
        return false;
    }
    stage.value = 'uploading';
    file.key = '';
}

async function onBeforeConfirm() {
    if (!productId.value) {
        Toast.info('请选择测评产品');
        return;
    }

    if (stage.value === 'done') {
        emit('close', 'submitSuccess');
    } else if (stage.value === 'uploaded') {
        const { code } = await importBegin({
            key: file.key,
            ...extraData,
            confirmAuth: confirmAuth.value,
            productId: productId.value,
        });

        if (code === 0) {
            stage.value = 'importing';
            // 循环查询进度
            rotateProcess();
        }
    } else {
        Toast.danger({
            content: '请先上传文件',
        });
    }
}
function onBeforeCancel() {
    if (stage.value === 'done') {
        resetUpload();
        stage.value = 'unUploaded';
    } else {
        resetUpload();
        emit('close', 'cancel');
    }
}

const uploadPercent = ref(0);
const file = reactive({
    key: '',
    name: '',
});
function onChange(f: any) {
    file.name = f.name;
}
function resetUpload() {
    file.key = '';
    file.name = '';
    importPercent.value = 0;
    uploadPercent.value = 0;
    uploadInstance.value?.clearFiles();
    stage.value = 'unUploaded';
    confirmAuth.value = false;
}
function onSuccess(res: any) {
    if (res.code === 0) {
        stage.value = 'uploaded';
        file.key = res?.data?.key;
    } else {
        resetUpload();
        Toast.danger({
            content: res.message || '上传失败',
        });
    }
}
function onError() {
    Toast.danger({
        content: '上传失败',
    });
    resetUpload();
}
function onProgress(event: any) {
    uploadPercent.value = Math.floor(event.percent);
}
function getAction() {
    const queryStr = qs.stringify({
        productId: productId.value,
    });
    const newAction = `${action}?${queryStr}`;
    console.log(newAction);
    return newAction;
}

function handleDownTemplate() {
    if (!productId.value) {
        Toast.info('请选择测评产品');
        return;
    }

    Toast.info('模板开始下载');

    _questionTemplate({
        productId: productId.value,
    });
}
</script>

<style lang="less" scoped>
@import url('../styles.less');
.second-step {
    width: 100%;
    :deep(.b-upload) {
        width: 100%;
    }
}
.upload-result {
    .upload-progress {
        position: absolute;
        bottom: -10px;
    }
}
</style>
