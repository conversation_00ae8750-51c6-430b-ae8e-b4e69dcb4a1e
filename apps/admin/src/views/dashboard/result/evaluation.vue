<template>
    <b-layout class="result-layout" direction="vertical">
        <div class="result-layout-form">
            <b-form
                ref="formRef"
                gentleValidation
                :model="queryData"
                layout="vertical"
                @submit="handleSearchExport"
            >
                <b-form-item label="测评产品" field="productId" :rules="[{ required: true, message: '请选择测评产品' }]" asteriskPosition="end">
                    <b-select
                        v-model="queryData.productId"
                        :loading="productListQuery.isPending.value"
                        :options="productListQuery.data.value"
                        allowSearch
                        placeholder="请选择"
                        :rules="[{ required: true, message: '请选择测评产品' }]"
                    />
                </b-form-item>
                <b-form-item label="所属项目" field="encryptProjectIds" asteriskPosition="end"  :rules="[{ required: true, message: '请选择所属项目' }]">
                    <b-select
                        multiple
                        :modelValue="queryData.encryptProjectIds"
                        :loading="projectListQuery.isPending.value"
                        :virtualListProps="{ height: 200 }"
                        :options="projectListQuery.data.value"
                        :hidden="!!maxTagCount || undefined"
                        :maxTagCount="maxTagCount"
                        allowClear
                        allowSearch
                        placeholder="请选择"
                        @update:modelValue="handleEncryptProjectIds"
                    />
                </b-form-item>
                <b-form-item label="所属场次">
                    <b-select
                        v-model="queryData.encryptSequenceIds"
                        multiple
                        :loading="sessionListQuery.isPending.value"
                        :virtualListProps="{ height: 200 }"
                        :options="sessionListQuery.data.value"
                        allowSearch
                        allowClear
                        placeholder="请选择"
                    >
                        <template v-if="!queryDataComputed.encryptProjectIds" #empty>
                            <div class="empty-state">
                                请先选择所属项目
                            </div>
                        </template>
                    </b-select>
                </b-form-item>
                <b-form-item label="测评试卷">
                    <b-select
                        v-model="queryData.encryptPaperIds"
                        multiple
                        :loading="paperListQuery.isPending.value"
                        :virtualListProps="{ height: 200 }"
                        :options="paperListQuery.data.value"
                        allowSearch
                        allowClear
                        placeholder="请选择"
                        @update:modelValue="handleEncryptPaperIds"
                    >
                        <template v-if="!queryDataComputed.encryptProjectIds" #empty>
                            <div class="empty-state">
                                请先选择所属项目
                            </div>
                        </template>
                    </b-select>
                </b-form-item>
                <b-form-item label="测评维度">
                    <b-cascader
                        v-model="queryData.encryptDimensionIds"
                        :triggerProps="{ contentClass: 'dimension-cascader-content' }"
                        :fieldNames="{ value: 'encryptId', label: 'name' }"
                        :defaultValue="queryData.encryptDimensionIds"
                        :loading="dimensionListQuery.isPending.value"
                        :options="dimensionListQuery.data.value"
                        expandTrigger="hover"
                        placeholder="请选择"
                        allowSearch
                        allowClear
                        multiple
                    >
                        <template v-if="!queryDataComputed.encryptPaperIds" #empty>
                            <div class="empty-state">
                                请先选择所属试卷
                            </div>
                        </template>
                    </b-cascader>
                </b-form-item>
                <b-form-item label="题目ID">
                    <b-textarea
                        v-model.trim="queryData.questionIds"
                        trimBeforePaste
                        allowSearch
                        allowClear
                        showWordLimit
                        :maxLength="50"
                        placeholder="输入多个ID请用英文“,”分隔"
                        :autoSize="{
                            minRows: 2,
                            maxRows: 6,
                        }"
                    />
                </b-form-item>
                <div style="width: 480px">
                    <b-form-item label="试卷关闭时间" field="dateRange" :rules="[{ required: true, message: '请选择试卷关闭时间' }]" asteriskPosition="end">
                        <b-range-picker
                            :modelValue="queryData.dateRange"
                            :disabledDate="dynamicDisabled"
                            allowClear
                            @update:modelValue="handleDateRangeChange"
                            @select="handleDateRangeSelect"
                            @popupVisibleChange="handleDateRangeVisibleChange"
                        />
                    </b-form-item>
                    <b-button
                        :loading="resultQueryInfo.isPending.value"
                        type="primary"
                        status="primary"
                        htmlType="submit"
                        class="confirm-button"
                    >
                        查询
                    </b-button>
                    <template v-if="infoDisplay">
                        <div v-if="resultQueryInfo.data.value?.overLimit" class="export-info">
                            <div class="export-info-content">
                                <SvgIcon name="warnning" width="20" height="20" />
                                <div>共查询到{{ resultQueryInfo.data.value.totalCount }}条数据，数据量过大，请分批查询后导出</div>
                            </div>
                        </div>
                        <div v-else class="export-info" style="background: #e8f9ff">
                            <div class="export-info-content">
                                <SvgIcon name="info" width="20" height="20" />
                                <div>共查询到{{ resultQueryInfo.data.value?.totalCount || 0 }}条数据</div>
                            </div>
                            <b-button
                                :disabled="!resultQueryInfo.data.value?.totalCount || exportLoading"
                                type="outline"
                                status="primary"
                                @click="handleExport"
                            >
                                导出
                            </b-button>
                        </div>
                        <b-progress
                            v-if="isProgressing || isError || isSuccess"
                            style="margin-left: 16px; margin-top: 8px"
                            :percentage="Math.round(percentage)"
                            :status
                        />
                    </template>
                </div>
            </b-form>
        </div>
    </b-layout>
</template>

<script setup lang="tsx">
import ReportService from '@/services/api/report'
import ResultService from '@/services/api/result'
import { number, string } from '@/utils'
import { useQueryLite } from '@crm/vueuse-pro'
import dayjs from 'dayjs'

import { useProgress } from './use-progress'

const queryData = ref<{
    productId?: number
    encryptProjectIds?: string[]
    encryptSequenceIds?: string[]
    encryptPaperIds?: string[]
    encryptDimensionIds?: string[]
    questionIds?: string
    paperCloseStartDate?: string
    paperCloseEndDate?: string
    dateRange?: string[]
}>({
    encryptProjectIds: [],
})

const queryDataComputed = computed(() => ({
    ...queryData.value,
    encryptProjectIds: queryData.value.encryptProjectIds?.filter(i => i !== '-1').join(','),
    encryptSequenceIds: queryData.value.encryptSequenceIds?.join(','),
    encryptPaperIds: queryData.value.encryptPaperIds?.join(','),
    encryptDimensionIds: queryData.value.encryptDimensionIds?.join(','),
}))

const rateRangeSelect = ref<string[]>([])
function dynamicDisabled(date: Date) {
    // 默认不允许选择的逻辑不变
    const defaultNotAllowDate = Date.now() - date.getTime() < 0

    const dateCount = rateRangeSelect.value.filter(i => i).length

    // 如果已经选择了一个日期
    if (dateCount === 1) {
        // 解析已选择的日期
        const selectedDate = new Date(rateRangeSelect.value[0] || rateRangeSelect.value[1])
        // 计算前后366天的日期
        const beforeDateLimit = new Date(selectedDate.getTime() - 366 * 24 * 3600 * 1000)
        const afterDateLimit = new Date(selectedDate.getTime() + 366 * 24 * 3600 * 1000)

        // 检查当前日期是否在可选择的范围内
        const isWithinRange = date >= beforeDateLimit && date <= afterDateLimit

        // 如果是默认不允许选择的日期，或者日期不在允许的范围内，则返回true
        return defaultNotAllowDate || !isWithinRange
    }

    // 如果还没有选择日期，或者已选择超过一个日期（这里可以根据实际需求调整），则保持原有逻辑
    return defaultNotAllowDate
}
watchEffect(() => {
    if (queryData.value.paperCloseStartDate && queryData.value.paperCloseEndDate) {
        queryData.value.dateRange = [queryData.value.paperCloseStartDate, queryData.value.paperCloseEndDate]
    }
})

function handleDateRangeSelect(value: string[] | undefined) {
    rateRangeSelect.value = value || []
}
function handleDateRangeVisibleChange(visible: boolean) {
    if (!visible) {
        rateRangeSelect.value = []
    }
}
function handleDateRangeChange(value?: string[]) {
    if (!value) {
        queryData.value.paperCloseStartDate = ''
        queryData.value.paperCloseEndDate = ''
        rateRangeSelect.value = []
        return
    }

    const startDate = dayjs(value[0])
    const endDate = dayjs(value[1])
    const today = dayjs()

    // 检查日期范围是否超过 366 天
    if (endDate.diff(startDate, 'day') > 366) {
        Toast.danger('日期范围不能超过 366 天')
        return
    }

    // 检查结束日期是否晚于昨天（T-1）
    if (endDate.isAfter(today.subtract(0, 'day'))) {
        Toast.danger('最晚可选日期为T')
        return
    }
    // 如果日期范围有效，则更新值
    queryData.value.paperCloseStartDate = startDate.format('YYYY-MM-DD')
    queryData.value.paperCloseEndDate = endDate.format('YYYY-MM-DD')
}

const maxTagCount = ref<number | undefined>(1)
function handleEncryptProjectIds(value: string[]) {
    const preventValue = queryData.value.encryptProjectIds || []
    const currentHasAll = !!queryData.value.encryptProjectIds?.includes('-1')
    const newHasAll = value.includes('-1')
    const allProjectIds = projectListQuery.data.value?.map(item => item.value) || []
    const totalProjectCount = projectListQuery.data.value?.length || 0

    // 重置标签显示数量
    maxTagCount.value = undefined

    // 更新项目ID列表
    updateProjectIds(currentHasAll, newHasAll, value, totalProjectCount, allProjectIds)

    // 查找从旧值中移除的项目ID
    const difference = findRemovedProjects(preventValue)

    // 更新关联的考试场次和试卷
    updateRelatedLists(difference)

    // 如果没有选择试卷，清空维度ID
    if (!queryData.value.encryptPaperIds?.length) {
        queryData.value.encryptDimensionIds = []
    }
}

// 更新项目ID列表
function updateProjectIds(currentHasAll: boolean, newHasAll: boolean, value: string[], totalProjectCount: number, allProjectIds: string[]) {
    if (currentHasAll && !newHasAll) {
        // 从"全部"变为具体选择
        queryData.value.encryptProjectIds = []
    } else if (!currentHasAll && (newHasAll || value.length === totalProjectCount - 1)) {
        // 从具体选择变为"全部" 或 几乎选择了所有项目，自动切换为"全部"
        queryData.value.encryptProjectIds = [...allProjectIds]
        maxTagCount.value = 1
    } else if (currentHasAll && value.length !== totalProjectCount) {
        // 保留"全部"但移除了部分选项
        queryData.value.encryptProjectIds = value.filter(i => i !== '-1')
    } else {
        // 保持选择不变
        queryData.value.encryptProjectIds = value
    }
}

// 查找从旧值中移除的项目ID
function findRemovedProjects(preventValue: string[]) {
    return preventValue.filter(x => !queryData.value.encryptProjectIds?.includes(x)).filter(x => x !== '-1') || []
}

// 更新关联的考试场次和试卷
function updateRelatedLists(difference: string[]) {
    // 更新场次列表
    queryData.value.encryptSequenceIds = sessionListQuery.raw.value?.data
        .filter(i => queryData.value.encryptSequenceIds?.includes(i.encryptId))
        ?.filter(item => !difference.includes(item.encryptProjectId))
        .map(i => i.encryptId) || []

    // 更新试卷列表
    queryData.value.encryptPaperIds = paperListQuery.raw.value?.data
        .filter(i => queryData.value.encryptPaperIds?.includes(i.encryptId))
        ?.filter(item => !difference.includes(item.encryptProjectId))
        .map(i => i.encryptId) || []
}

function handleEncryptPaperIds(value: string[]) {
    queryData.value.encryptPaperIds = value
    if (!queryData.value.encryptPaperIds?.length) {
        queryData.value.encryptDimensionIds = []
    }
}

const productListQuery = useQueryLite({
    keys: () => ['productList'],
    fetcher: ReportService.getProductList,
    select: raw =>
        raw?.data.map(item => ({
            label: item.name,
            value: item.id,
        })),
})

const projectListQuery = useQueryLite({
    keys: () => ['projectList'],
    fetcher: ResultService.getProjectList,
    select: (raw) => {
        const data = raw?.data.map(item => ({
            label: item.name,
            value: item.encryptId,
        }))
        data?.unshift({
            label: '全部',
            value: '-1',
        })
        return data
    },
    onSuccess: (raw) => {
        queryData.value.encryptProjectIds = ['-1', ...(raw.data?.map(item => item.encryptId) || [])]
    },
})

const sessionListQuery = useQueryLite({
    keys: () => ['sessionList', queryData.value.encryptProjectIds],
    fetcher: () => ResultService.getSequenceList({ encryptProjectIds: queryDataComputed.value.encryptProjectIds }),
    disabled: () => !queryData.value.encryptProjectIds?.length,
    select: (raw) => {
        if (!queryData.value.encryptProjectIds?.length) {
            return []
        }
        return raw?.data.map((item) => {
            return {
                label: item.name,
                value: item.encryptId,
                render: () => {
                    return (
                        <div>
                            <div>{item.name}</div>
                            <div style="font-size: 12px; color: #999999">{item.projectName}</div>
                        </div>
                    )
                },
            }
        })
    },
})

const paperListQuery = useQueryLite({
    keys: () => ['paperList', queryData.value.encryptProjectIds],
    fetcher: () => ResultService.getPaperList({ encryptProjectIds: queryDataComputed.value.encryptProjectIds }),
    disabled: () => !queryData.value.encryptProjectIds?.length,
    select: (raw) => {
        if (!queryData.value.encryptProjectIds?.length) {
            return []
        }
        return raw?.data.map(item => ({
            label: item.paperName,
            value: item.encryptId,
            render: () => {
                return (
                    <div>
                        <div>{item.paperName}</div>
                        <div style="font-size: 12px; color: #999999">{item.projectName}</div>
                    </div>
                )
            },
        }))
    },
})

const dimensionListQuery = useQueryLite({
    keys: () => ['dimensionList', queryData.value.encryptPaperIds],
    fetcher: () => ResultService.getOptionV2List({ encryptPaperIds: queryDataComputed.value.encryptPaperIds }),
    disabled: () => !queryData.value.encryptPaperIds?.length,
    select: (raw) => {
        if (!queryData.value.encryptPaperIds?.length) {
            return []
        }
        return raw?.data
    },
    onSuccess: (raw) => {
        queryData.value.encryptDimensionIds = queryData.value.encryptDimensionIds?.filter(i => raw.data.map(item => item.encryptId).includes(i))
    },
})

const infoDisplay = ref(false)
const resultQueryInfo = useQueryLite({
    keys: () => ['resultQueryInfo'],
    fetcher: () => ResultService.resultQueryInfo(queryDataComputed.value),
    disabled: () => true,
    onSuccess: (data) => {
        infoDisplay.value = true
        setTimeout(() => {
            window.scroll({ top: document.documentElement.clientHeight, left: 0, behavior: 'smooth' })
        }, 1000)
    },
    select: raw => raw?.data,
    keepPreviousData: true,
})

const formRef = ref()
async function handleSearchExport() {
    // const result = await safeParseAsync(QUESTION_SCHEMA, queryDataComputed.value)
     const res = await formRef.value.validate()
    if (res) {
        Toast.danger(res[Object.keys(res)[0]].message)
        return
    }
    try {
       await resultQueryInfo.refetch()
    } catch (error) {
        console.log(error)
    }
}

const { percentage, status, start, stop, isProgressing, isError, isSuccess } = useProgress(() => ((resultQueryInfo.data.value?.totalCount || 0) + 30) / 2)
const exportLoading = ref(false)
async function handleExport() {
    if (resultQueryInfo.data.value?.overLimit) {
        return
    }
    exportLoading.value = true
    try {
        start()
        await ResultService.resultExport(queryDataComputed.value)
        Toast.info('导出任务已执行，请注意查收邮件')
        stop('success')
    } catch (error) {
        stop('exception')
        console.log(error)
    } finally {
        exportLoading.value = false
    }
}
</script>

<style lang="less">
.b-trigger-popup {
    .empty-state {
        font-size: 14px;
        line-height: 30px;
        text-align: center;
        width: 100%;
        height: 30px;
    }
}
.result-layout {
    padding: 20px 14px;
    .b-select-view-multiple[hidden] .b-select-view-inner > :nth-last-child(2) {
        display: none;
    }
    .page-content-title {
        font-size: 16px;
        font-weight: 500;
        color: #1f1f1f;
        padding: 8px 12px;
        height: 40px;
        background: #f7f7f7;
        border-radius: 8px;
        position: relative;
        margin-bottom: 20px;

        &::before {
            content: '';
            display: block;
            position: absolute;
            top: calc(50% - 7px);
            left: 0;
            width: 4px;
            height: 14px;
            background: var(--primary-color);
            border-radius: 0 2px 2px 0;
        }
    }
    .confirm-button {
        width: 74px;
    }
    .result-layout-form {
        width: 720px;
    }
    .export-info {
        margin-top: 20px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 480px;
        height: 52px;
        background: #fffbe8;
        border-radius: 8px;
        padding: 16px;
    }
    .export-info-content {
        display: flex;
        flex-direction: row;
        gap: 8px;
        font-weight: 500;
        color: #1f1f1f;
        line-height: 20px;
        margin-right: 20px;
    }
}
.dimension-cascader-content {
    .b-cascader-option-label {
        max-width: 350px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .empty-state {
        width: 360px;
        margin-top: 20%;
    }
}
</style>

<style lang="less" scoped>
:deep(*) {
    .b-progress-bar-inner {
        animation-duration: 1s !important;
        transition: width 0.1s ease;
    }
    .b-progress-text {
        min-width: 40px;
    }
}
</style>
