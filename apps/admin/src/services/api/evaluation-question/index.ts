import type {
    DimensionOptionParams,
    DimensionOptionResponse,
    QuestionErrorParams,
    QuestionListParams,
    QuestionListResponse,
    QuestionParams,
    QuestionProgressParams,
    QuestionProgressResponse,
    QuestionResponse,
    QuestionSaveParams,
    QuestionSaveResponse,
    QuestionStartParams,
    QuestionStartResponse,
    QuestionUpdateStatusParams,
    QuestionUpdateStatusResponse,
    QuestionUploadParams,
    QuestionUploadResponse,
} from './type';
import { downloadFile, get, post, postForm } from '@/services/http';
import { u } from './mock';
/**
 * https://api.weizhipin.com/project/2353/interface/api/630347
 * 测评题库-试题列表
 */
export function _questionList(params: QuestionListParams) {
    return get<QuestionListResponse>(u('/wapi/admin/evaluation/question/list.json'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630386
 * 测评题库-试题信息
 */
export function _questionInfo(params: QuestionParams) {
    return get<QuestionResponse>(u('/wapi/admin/evaluation/question/info.json'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630356
 * 测评题库-试题保存
 */
export function _questionSave(params: QuestionSaveParams) {
    return post<QuestionSaveResponse>(u('/wapi/admin/evaluation/question/save.json'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630365
 * 测评题库-试题启动/停用
 */
export function _questionUpdateStatus(params: QuestionUpdateStatusParams) {
    return post<QuestionUpdateStatusResponse>(u('/wapi/admin/evaluation/question/updateStatus.json'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630368
 * 测评题库-试题导入模板下载
 */
export function _questionTemplate(params: any) {
    return downloadFile(u('/wapi/admin/import/evaluation/question/template'), '试题导入模板.xlsx', params);
}
export function _questionError(params: QuestionErrorParams) {
    return downloadFile(u('/wapi/admin/import/evaluation/question/error'), '导入错误的试题.xlsx', params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630371
 * 测评题库-试题文件上传
 */
export function _questionUpload(params: QuestionUploadParams) {
    return postForm<QuestionUploadResponse>(u('/wapi/admin/import/evaluation/question/upload'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630671
 * 测评题库-开始导入
 */
export function _questionStart(params: QuestionStartParams) {
    return postForm<QuestionStartResponse>(u('/wapi/admin/import/evaluation/question/start'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/630380
 * 测评题库-试题导入进度查询
 */
export function _questionProgress(params: QuestionProgressParams) {
    return get<QuestionProgressResponse>(u('/wapi/admin/import/evaluation/question/progress'), params);
}
/**
 * https://api.weizhipin.com/project/2353/interface/api/629564
 * 可选测评维度
 */
export function _dimensionOption(params: DimensionOptionParams) {
    return get<DimensionOptionResponse>(u('/wapi/admin/evaluation/dimension/option.json'), params);
}

/**
 * https://api.weizhipin.com/project/2353/interface/api/700041
 * 检测金融产品是否被使用
 */
export function _financeCheckDeactivate(params: DimensionOptionParams) {
    return get<DimensionOptionResponse>(u('/wapi/admin/evaluation/question/check4Product6'), params);
}
