import type { ITableColumnDataRaw } from '@boss/design';
import type { PCode } from '@crm/biz-exam-product';
import type {
    AnswerConsistentRule,
    AnswerDisguiseRule,
    AnswerValidityRule,
    AnswerValidityRuleLPA,
    AppendixData,
    CompetencyDetail,
    CompetencyStyleConfig,
    ConsistentRule,
    DimensionConfig,
    DimensionDesc,
    DimensionLanguage,
    DimensionLevel,
    DimensionLevelDesc,
    DimensionLevelDescSP,
    DISCConsistentRule,
    FieldInfo,
    GBADimensionConfig,
    GroupReferenceValue,
    HIPOConsistency,
    IndicatorInfo,
    InterviewAdviceContent,
    InterviewGuideline,
    JudgeQuestion,
    OneLevelBothEndsDesc,
    ParamConfig,
    PotentialQualitiesKeyDesc,
    QuestionInherent,
    QuestionShowConfig,
    ScoreLevel,
} from './base';

interface SharedField {
    encryptId?: string;
    templateName?: string;
    productId?: number;
    reportSubTitle?: string;
}

/**
 * @interface CA 报告字段信息
 */
type CAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    useIntroduction: string;
    indicativeList: IndicatorInfo[];
    indicativeIntroduction: string;
    totalScoreLevelList: ScoreLevel[];
    dimensionLevelList: ScoreLevel[];
    groupReferenceValueList: GroupReferenceValue[];
    competencyDetailIntroduction: string;
    competencyDetailList: CompetencyDetail[];
    interviewAdviceIntroduction: string;
    interviewAdviceContentList: InterviewAdviceContent[];

    // 工具字段
    appendixData: AppendixData[];
    referenceColumns: Partial<ITableColumnDataRaw & { thName: `th-${string}`; tdName: `td-${string}` }>[];
};

/**
 * @interface HMA 报告字段信息
 */
type HMAReportFieldInfo = SharedField & {
    testDescription: string;
    examineeInfoList: FieldInfo[];
    useIntroduction: string; // 使用说明
    indicativeList: IndicatorInfo[]; // 报告参考性
    indicativeIntroduction: string; // 报告参考性说明
    competencyDetailList: CompetencyDetail[]; // 因子等级描述
    interviewAdviceContentList: InterviewAdviceContent[]; // 面试建议内容
    testPurposes: string; // 测评用途
    resultIntroduction: string; // 结果说明
    interviewAdviceNoIntroduction: string; // 无需关注的风险因子时展示
    interviewAdviceHasIntroduction: string; // 有需关注的风险因子时的引导语
    answerConsistentRule: AnswerConsistentRule[][]; // 作答一致性规则
    answerDisguiseRule: AnswerDisguiseRule[]; // 作答掩饰性规则

    // 工具字段
    appendixData: AppendixData[];

    /*
     * 四象限解读 4.28
     */
    quadrantInterpretationList: {
        code: number;
        introduction: string;
    }[];
};

/**
 * @interface OEA 报告字段信息
 */
type OEAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    useIntroduction: string;
    indicativeList: IndicatorInfo[];
    indicativeIntroduction: string;
    comprehensiveDesc: string;
    comprehensiveIntroduction: string;
    oneLevelBothEndsDesc: OneLevelBothEndsDesc[];
    oneLevelLanguage: DimensionLevelDesc[];
    dimensionDetailList: {
        encryptDimensionId: string;
        dimensionName: string;
        oneLevelDetailInt: string;
        twoLevelDesc: string;
        twoLevelBothEndsDesc: OneLevelBothEndsDesc[];
        twoLevelLanguage: DimensionLevelDesc[];
    }[];
    potentialQualitiesKeyInt: string;
    potentialQualitiesKeyDesc: PotentialQualitiesKeyDesc[];
    interviewAdviceIntroduction: string;
    answerConsistentRule: ConsistentRule;

    /*
     * 面试建议 4.28
     */
    interviewAdviceContentList: InterviewGuideline[];

    /*
     * 维度简介 4.28
     */
    dimensionIntroductionList: {
        encryptDimensionId: string;
        dimensionName: string;
        introduction: string;
    }[];

    cultivateSuggest: {
        encryptDimensionId: string;
        dimensionName: string;
        suggestList: string[];
    }[];

    secondLevelDisplayNames: {
        encryptDimensionId: string;
        dimensionName: string;
        dimensionShowName: string;
    }[];
};

/**
 * @interface OEWA 报告字段信息
 */
type OEWAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    useIntroduction: string;
    indicativeList: IndicatorInfo[];
    indicativeIntroduction: string;
    comprehensiveDesc: string;
    comprehensiveIntroduction: string;
    oneLevelBothEndsDesc: OneLevelBothEndsDesc[];
    oneLevelLanguage: DimensionLevelDesc[];
    dimensionDetailList: {
        encryptDimensionId: string;
        dimensionName: string;
        oneLevelDetailInt: string;
        twoLevelDesc: string;
        twoLevelBothEndsDesc: OneLevelBothEndsDesc[];
        twoLevelLanguage: DimensionLevelDesc[];
    }[];

    matchJobDesc: string;
    matchJobImage: { jobName: string; jobImage: string }[];
    matchJobIntroduction: string;
    matchJobContent: { jobName: string; jobContent: string }[];

    potentialQualitiesKeyInt: string;
    potentialQualitiesKeyDesc: PotentialQualitiesKeyDesc[];
    interviewAdviceIntroduction: string;
    interviewAdviceContentList: InterviewGuideline[];
    answerConsistentRule: ConsistentRule;
};

/**
 * @interface CRA 报告字段信息
 */
type CRAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    readIntroduction: string;
    totalScoreArray: (string | undefined)[];
    totalScoreLevelList: DimensionLevel[];
    dimensionScoreArray: (string | undefined)[];
    dimensionLevelDescList: DimensionLevelDescSP[];
    advantageWeakIntroduction: string;
    advantageWeakFeaturesList: DimensionLevelDescSP[];
    noAdvantageIntroduction: string;
    noWeakIntroduction: string;
    interviewAdviceIntroduction: string;
    interviewAdviceContentList: InterviewGuideline[];
    useIntroduction: string;
    indicativeList: IndicatorInfo[];
    indicativeIntroduction: string;
    answerConsistentRule: ConsistentRule;
    answerDisguiseRule: {
        questionId?: number;
        badScore?: number;
    }[];
};

/**
 * @interface FCA 报告字段信息
 */
export type FCAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    testDescription: string; // 测验介绍
    useIntroduction: string; // 使用指南
    testDescriptionImage: string; // 测评介绍图 URL
    testDescriptionFootnote: string; // 测评介绍脚注
    readingStructureImage: string; // 报告阅读结构图 URL
    competencyScoreDesc: string; // 胜任力得分说明
    competencyStyleDesc: string; // 胜任力风格说明
    competencyStyleConfigList: CompetencyStyleConfig[]; // 胜任力风格配置列表
    indicativeList: IndicatorInfo[]; // 报告参考性
    indicativeIntroduction: string; // 报告参考性说明
    answerValidityRule: AnswerValidityRule; // 作答有效性规则
    aboutThisReport: string; // 关于本报告

    // 工具字段
    competencyStyleConfigTableData?: {
        parentIndex: number;
        currentIndex: number;
        primaryDimension?: string;
        primaryStyle?: string;
        sortOrder?: number;
        secondaryDimension?: string;
        secondaryStyle: string;
        description: string[];
    }[];
};

/**
 * @interface LPA 报告字段信息
 */
type LPAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    useIntroduction: string;
    indicativeIntroduction: string;
    totalScoreLevelList: ScoreLevel[];
    indicativeList: IndicatorInfo[];
    answerValidityRule: AnswerValidityRuleLPA;
    leaderModelIntroduction: string;
    leaderStyleIntroduction: string;
    leaderActiveIntroduction: string;
    manageExampleActive: {
        mangeActive: string;
        exampleActive: string;
    }[];
    aboutThisReport: string;
};

/**
 * @interface PTA 报告字段信息
 */
type PTAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    useIntroduction: string;

    reportReadImgUrl: string;
    dimensionScoreIntroduction: string;
    professionalQualityStarIntr: string;
    pickSuggestionIntroduction: string;
    indicativeIntroduction: string;

    indicativeList: IndicatorInfo[];
    aboutThisReport: string;

    answerConsistentRule?: ConsistentRule;
    breakTimes?: number;

    reportTypeConfig: {
        positionMatching: {
            reportType: number;
            testDescription: string;
            reportSubTitle: string;
            reportReadImgUrl: string;
            dimensionScoreIntroduction: string;
            professionalQualityStarIntr: string;
            pickSuggestionIntroduction: string;
            aboutThisReport: string;
            templateName: string;
        };
        common: {
            reportType: number;
            templateName: string;
            reportSubTitle: string; // 报告副标题
            testDescription: string;
            reportReadImgUrl: string | null; // 报告阅读结构图
            dimensionScoreIntroduction: string; // 维度得分图说明
            professionalQualityStarIntr: string | null; // 职业素质星级说明
            pickSuggestionIntroduction: string | null; // 选人建议说明
            aboutThisReport: string; // 关于本报告
            professionalTemperament: {
                name: string;
                showName: string;
            }[]; // 职业气质配置
            professionalValuesIntroduction: string; // 职业价值观说明
            preferenceFieldIntroduction: string; // 偏好领域说明
            taskManageAdviceIntroduction: string; // 任务管理建议说明
            leaderStyleAdviceIntroduction: string; // 领导风格建议说明
        };
    };
};

interface ReportInfo {
    examineeInfoList: FieldInfo[];
    expiryDate?: number;
    referenceRule?: number;
    readImage?: string;
    testDescription?: string;
    testDescriptionFooter?: string;
    importantResultIntr?: string;
    coreResultIntr?: string;
    aboutThisReport?: string;

    indicativeIntroduction?: string;
    indicativeList?: IndicatorInfo[];

    answerConsistentRule?: ConsistentRule;
    breakTimes?: number;
}

/**
 * @interface OMHA 报告字段信息
 */
type OMHAReportFieldInfo = SharedField & {
    questionGroupGuideInfo: Record<string, string>;
    team: ReportInfo;
    person: ReportInfo;
};

/**
 * @interface HOTS 报告字段信息
 */
type HOTSReportFieldInfo = SharedField & {
    testDescription?: string;
    examineeInfoList: FieldInfo[]; // 考生信息列表
    indicativeList: IndicatorInfo[]; // 指标列表
    indicativeIntroduction: string; // 报告参考性说明
    competencyDetailIntroduction: string; // 详细结果文案
    answerInstruction: string; // 作答说明
    operatingInstruction: string; // 作答指引（操作指引）
    questionShowConfig: QuestionShowConfig[]; // 题目产品展示配置
    questionConfig: {
        questionId: string; // 题目ID
        questionName: string; // 题目名称
        inherentQuestion: QuestionInherent[]; // 题目产品-问题
        paramConfig: ParamConfig[]; // 题目产品-参数
        judgeQuestion: JudgeQuestion[]; // 判断题
    }[]; // 题目产品配置
    dimensionConfig: DimensionConfig[]; // 维度配置
    aboutThisReport: string; // 关于本报告
    avgMinTime?: number; // 题目产品最短平均时长
    mockMinTimes?: number; // 题目产品最少平均模拟次数
    dimensionDesc: DimensionDesc[]; // 维度综合文案
    dimensionLanguage: DimensionLanguage[]; // 维度等级语言
};
/**
 * @interface HOTS 报告字段信息
 */
type DISCReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表'
    testDescription: string; // 测验介绍
    indicativeList: IndicatorInfo[]; // 指标列表
    indicativeIntroduction: string; // 报告参考性说明
    aboutThisReport: string; // 关于本报告
    answerConsistentRule: DISCConsistentRule;
    extraInfo?: string; // 附录补充信息
};

/**
 * @interface HIPO 报告字段信息
 */
type HIPOReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表'
    testDescription: string; // 测验介绍
    indicativeList: IndicatorInfo[]; // 指标列表
    indicativeIntroduction: string; // 报告参考性说明
    comprehensiveResult: {
        dimensionName: string;
        showName: string;
    }[];
    /*
     * 详细结果
     */
    detailResult: {
        /*
         * 任用建议说明
         */
        appointmentAdvice: string;
        /*
         * 人才分类说明
         */
        talentClassification: string;
    };
    aboutThisReport: string; // 关于本报告
    answerValidityRule: HIPOConsistency;
};

/**
 * @interface GBA 报告字段信息
 */
type GBAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[];
    testDescription: string;
    useIntroduction: string | null;
    indicativeList: IndicatorInfo[];
    indicativeIntroduction: string;
    totalScoreLevelList: any[] | null;
    dimensionLevelList: any[] | null;
    groupReferenceValueList: any[] | null;
    competencyDetailIntroduction: string;
    competencyDetailList: any[] | null;
    interviewAdviceIntroduction: string | null;
    interviewAdviceContentList: any[] | null;
    answerProcess: {
        totalAnswerInstruction: string;
        gameShowConfig: {
            gameId: number;
            gameName: string;
            gameShowName: string;
            sort: number;
        }[];
        gameQuestionParam: {
            id: number;
            name: string;
            showName: string;
            sort: number;
            answerDesc: string;
            answerDescTime: number;
            answerTime: number;
            chooseTime: number;
            distributeTime: number | null;
            distributeRound: number | null;
            successIntervalTime: number | null;
            resultAppearTime: number | null;
            repeatTime: number | null;
            objectAppearTime: number | null;
            objectIntervalTime: number | null;
            leftTitle: string;
            rightTitle: string;
            manualRound: number | null;
            gameImageList: {
                id: number;
                image: string;
            }[];
        }[];
    };
    reportValidity: number;
    dimensionConfig: GBADimensionConfig[];
    oneLevelDimensionConfig: GBADimensionConfig[];
    dimensionConfigCache?: GBADimensionConfig[];
    aboutThisReport: string;
    extParam: {
        breakTimes: number;
        correctRate: number;
        completionRate: number;
    };
    reportTypeConfig: {
        character: {
            templateName: string;
            reportSubTitle: string;
            testDescription: string;
            characterPortraitList: {
                level: number;
                levelLabel: string;
                levelPerformance: string;
            }[];
            evaluationModelExplanation: string;
        };
        careerRole: {
            templateName: string;
            reportSubTitle: string;
            testDescription: string;
            characterPortraitList: {
                level: number;
                levelLabel: string;
                levelPerformance: string;
            }[];
            evaluationModelExplanation: string;
        };
        cognition: {
            templateName: string;
            reportSubTitle: string;
            testDescription: string;
            oneLevelDimensionConfig: GBADimensionConfig[];
            dimensionConfigCache?: GBADimensionConfig[];
        };
    };
};

/**
 * @interface ES 报告字段信息
 */
type ESReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    reportValidity?: number; // 报告有效期
    testDescription?: string; // 测验介绍
    indicativeList: IndicatorInfo[]; // 指标列表
    reportType?: number; // 报告类型
    aboutThisReport?: string; // 关于本报告
    repeatAnswerPercent?: number; // 重复作答率
    answerValidityExplain?: string; // 作答有效性说明
    surveyResultDetailExplain?: string; // 问卷结果详情说明
    improveConfidenceExplain?: string; // 提高自信说明
    actionSuggestion: {
        id?: number;
        dimensionName?: string;
        actionSuggestionGroupName?: string;
        contentOne?: string;
        contentTwo?: string;
    }[];
};

/**
 * @interface GPA 报告字段信息
 */
type GPAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    reportValidity?: number; // 报告有效期
    testDescription?: string; // 测验介绍
    useIntroduction?: string; // 使用说明
    indicativeList: IndicatorInfo[]; // 指标列表
    indicativeIntroduction?: string; // 报告参考性说明
    interviewAdviceIntroduction?: string; // 面试建议说明
    interviewAdviceContentList?: InterviewAdviceContent[]; // 面试建议内容列表
    dimensionComprehensiveResultAnnotation?: string; // 维度综合结果注释
    dimensionConfig: {
        id: string;
        name: string;
        showName: string;
        define: string;
    }[]; // 维度配置
    occupationStyleConfig: {
        id: string;
        name: string;
        showName: string;
    }[]; // 职业风格配置
    detailResultExplain?: string; // 详细结果说明
    breakTimes?: number; // 中断次数
    principleExplain?: string; // 原则说明
    aboutThisReport?: string; // 关于本报告
};

/**
 * @interface CAA 报告字段信息
 */
type CAAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    indicativeIntroduction?: string; // 报告参考性说明
    reportType?: number; // 报告类型
    testDescriptionOne?: string; // 测验介绍1
    testDescriptionTwo?: string; // 测验介绍2
    reportAppendix?: string; // 报告附录
    dimensionConfig: {
        encryptDimensionId: string;
        dimensionName: string;
        showName: string;
        define: string[];
    }[]; // 维度配置
    advantageAndDisadvantage: {
        encryptDimensionId: string;
        dimensionName: string;
        levelList: {
            level: number;
            levelLanguage: string[];
        }[];
    }[];

    levelLanguage: {
        encryptDimensionId: string;
        dimensionName: string;
        levelList: {
            level: number;
            levelLanguage: string[];
        }[];
    }[];

    advantageAndDisadvantageCache?: any[];
    levelLanguageCache?: any[];

    /*
     * 认知能力特点
     */
    featureList: {
        encryptDimensionId: string;
        dimensionName: string;
        levelList: {
            level: number;
            feature: string;
        }[];
    }[];

    featureList2?: {
        encryptDimensionId: string;
        dimensionName: string;
        level: number;
        feature: string;
    }[];
};

/**
 * @interface MA 报告字段信息
 */
type MAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    answerConsistentRule?: AnswerConsistentRule[][]; // 作答一致性规则
    indicativeIntroduction?: string | null;
    totalScoreLevelList?: any[] | null;
    dimensionLevelList?: any[] | null;
    groupReferenceValueList?: any[] | null;
    competencyDetailIntroduction?: string | null;
    competencyDetailList?: any[] | null;
    interviewAdviceIntroduction?: string | null;
    interviewAdviceContentList?: any[] | null;
    reportSubTitle?: string;
    totalResultExplain?: string;
    careerMotivationIntroduction?: string;
    careerMotivationDetailIntroduction?: string;
    careerAnchorIntroduction?: string;
    testDescription?: string;
    useIntroduction?: string | null;
    indicativeList: IndicatorInfo[]; // 指标列表
    careerAnchorConfig?: {
        encryptId: string;
        name: string;
        showName: string;
        introduction: string;
        define: string;
        title: string;
        content: string;
    }[];
    oneLevelDimensionConfig?: {
        encryptId: string;
        name: string;
        define: string;
        groupReferenceValue: string | null;
        managementAdvice: string | null;
        levelPerformance: any; // 根据实际数据调整类型，目前为 any 或 null
    }[];
    twoLevelDimensionConfig?: {
        encryptId: string;
        name: string;
        define: string;
        groupReferenceValue: number | null;
        managementAdvice: string | null;
        levelPerformance: Record<string, string>;
    }[];
};

type NTAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    indicativeList: IndicatorInfo[]; // 指标列表
    dimensionDefinitions?: {
        encryptDimensionId: string;
        dimensionName: string;
        definition: string;
    }[];
    dimensionConfig?: {
        encryptDimensionId: string;
        dimensionName: string;
        showName: string;
    }[];
    levelLanguage?: {
        encryptDimensionId: string;
        dimensionName: string;
        levelList: {
            level: number;
            score: number;
            description: string;
        }[];
    }[];
    subDimensionLevels?: {
        id: number;
        dimensionName: string;
        levelList: {
            level: string;
            score: number;
            description: string;
        }[];
    }[];
    talentTypeDescriptions?: {
        dimensionName: string;
        description: string;
    }[];
    thinkingStyleDescription?: string;
    behaviorStyleDescription?: string;
    communicationStyleDescription?: string;
    managementPotentialDescription?: string;
    appendixDescription?: string;
};

interface PQADimensionConfig {
    encryptDimensionId: string;
    dimensionName: string;
    dimensionId?: number;
    dimensionDefinition: string;
    subDimensionConfig?: PQADimensionConfig &
        {
            levelDescriptions: Record<string, any>;
            interviewAdviceList: {
                question: string;
                keyPoints: string;
            }[];
        }[];
}
type PQAReportFieldInfo = SharedField & {
    examineeInfoList: FieldInfo[]; // 考生信息列表
    aboutReport: string;
    answerValidityRule: {
        questionId: number;
        abnormalScore: number;
    }[];
    politicalLiteracyLevelDescriptions: Record<string, any>;
    dimensionConfig: PQADimensionConfig[];
    indicativeList: IndicatorInfo[];
    productId: number;
    readingInstructions: string;
    testDescription: string;
    oneLevelDimensionConfig?: {
        description: string | null;
        dimensionLevel: number;
        dimensionName: string;
        encryptId: string;
        encryptParentId: string;
        factorType: string | null;
    }[];
    twoLevelDimensionConfig?: {
        firstDimensionName: string;
        encryptDimensionId: string;
        dimensionName: string;
        dimensionId: number;
        dimensionDefinition: string;
        encryptParentId: string;
        splitDimensionName: string;
        levelDescriptions?: Record<string, any>;
    }[];
    interviewAdviceList?: {
        encryptId?: string;
        question: string;
        keyPoints: string;
        index?: number;
    }[];
    twoLevelDimensionConfigLevelDescriptions?: {
        splitDimensionName?: string;
        encryptDimensionId?: string;
        encryptId?: string;
        levelDescriptions?: Record<string, any>;
        levelDescriptions1?: string | undefined;
        levelDescriptions2?: string | undefined;
        levelDescriptions3?: string | undefined;
        [key: string]: any;
    }[];
};

// 为每个 PCode 指定对应的类型
export interface ReportFieldMapping {
    [PCode.CA]: CAReportFieldInfo;
    [PCode.HMA]: HMAReportFieldInfo;
    [PCode.OEA]: OEAReportFieldInfo;
    [PCode.OEWA]: OEWAReportFieldInfo;
    [PCode.CRA]: CRAReportFieldInfo;
    [PCode.LPA]: LPAReportFieldInfo;
    [PCode.FCA]: FCAReportFieldInfo;
    [PCode.PTA]: PTAReportFieldInfo;
    [PCode.OMHA]: OMHAReportFieldInfo;
    [PCode.HOTS]: HOTSReportFieldInfo;
    [PCode.DISC]: DISCReportFieldInfo;
    [PCode.HIPO]: HIPOReportFieldInfo;
    [PCode.GBA]: GBAReportFieldInfo;
    [PCode.ES]: ESReportFieldInfo;
    [PCode.GPA]: GPAReportFieldInfo;
    [PCode.CAA]: CAAReportFieldInfo;
    [PCode.MA]: MAReportFieldInfo;
    [PCode.NTA]: NTAReportFieldInfo;
    [PCode.PQA]: PQAReportFieldInfo;
}

export interface DimensionNewList {
    encryptId: string;
    name: string;
    description: string;
    dimensionLevel: number;
    encryptParentId: string;
    encryptOriginalId: string;
    originalId: number;
    level: number;
    leaf: boolean;
    children?: DimensionNewList[];
}

// 使用 ReportFieldMapping 映射来定义 SpecificReportFieldInfo
export type SpecificReportFieldInfo<T extends PCode> = T extends keyof ReportFieldMapping ? ReportFieldMapping[T] : never;

/**
 * @interface 考核结果接口
 */
export type AssessmentResult<T extends PCode> = SpecificReportFieldInfo<T>;

/**
 * @interface 列表项类型
 */
export interface ListItemType extends Record<string, any> {
    /**
     * 加密 ID
     */
    encryptId: string;
    /**
     * 加密模板 ID
     */
    encryptTemplateId: string;
    /**
     * 产品名称
     */
    productName: string;
    /**
     * 创建时间
     */
    createTime: number;
    /**
     * 创建用户名
     */
    createUserName: string;
}

export type FCFileType = 'quality' | 'advantage' | 'proposal' | 'example';
export type LPFileType = 'product5DimensionIntroduction' | 'product5LevelLanguage' | 'product5AdvanceWeakDesc' | 'product5LeaderStyleDesc' | 'product5DevelopSuggestion';
export type FileType = FCFileType | LPFileType;

export interface FileUploadProcess {
    total: number;
    success: number;
    error: number;
    done: number;
    complete: number;
    key: string;
    importType: string;
    startTime: number;
    encryptOperatorId: number;
    importFileName: string;
    systemErrorInfo: null;
    rate: number;
}

export interface QuestionInfo {
    encryptId: string;
    productId: number;
    encryptDimensionId: string;
    dimensionName: string;
    /**
     * 题目类型：1-单选题、9-量表题、10-迫选题
     */
    questionType: 1 | 9 | 10;
    questionTitle: string;
    questionOptionList: {
        rank: number;
        score: number;
        optionContent: string;
        encryptId: string;
        encryptDimensionId: string;
    }[];
    fillBlankList: {
        encryptId: string;
        rank: number;
        score: number;
    }[];
}

export interface QuestionOption {
    /**
     * 选项id
     */
    id: number;

    /**
     * 题目id
     */
    questionId: number;

    /**
     * 序号
     */
    rank: number;

    /**
     * 得分
     */
    score: number;

    /**
     * 选项内容
     */
    optionContent: string;

    /**
     * 维度id
     */
    dimensionId: number;

    /**
     * 选项加密id
     */
    encryptId: string;
}

export interface Question {
    /**
     * 题目id
     */
    id: number;

    /**
     * 产品id
     */
    productId: number;

    /**
     * 产品名
     */
    productName: string | null;

    /**
     * 维度id
     */
    dimensionId: number;

    /**
     * 维度名
     */
    dimensionName: string | null;

    /**
     * 题目标题
     */
    questionTitle: string;

    /**
     * 题目类型
     */
    questionType: number;

    /**
     * 题目状态
     */
    questionStatus: number;

    /**
     * 创建时间
     */
    createTime: string;

    /**
     * 创建人id
     */
    creatorId: number;

    /**
     * 创建人姓名
     */
    creatorName: string | null;

    /**
     * 题目选项列表
     */
    questionOptionList: QuestionOption[];

    /**
     * 题目类型描述
     */
    questionTypeDesc: string;

    /**
     * 题目状态描述
     */
    questionStatusDesc: string;

    /**
     * 加密维度id
     */
    encryptDimensionId: string;

    /**
     * 加密id
     */
    encryptId: string;
}
