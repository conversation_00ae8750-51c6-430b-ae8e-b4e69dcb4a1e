import type { PCode } from '@crm/biz-exam-product';
import type { ListType } from './base';
import type { AssessmentResult, FileUploadProcess, ListItemType, Question, QuestionInfo, DimensionNewList } from './interface';
import { downloadFile2, get, post, postForm } from '@/services/http';

// const u = 'https://api.weizhipin.com/mock/2353';
const u = '';

export const ReportFileService = {
    upload: (params: { type: string; file: File }) => postForm<{ key: string }>(`${u}/wapi/admin/import/evaluation/report/product6/upload`, params),
    beginImport: (params: { type: string; key: string }) => postForm<FileUploadProcess>(`${u}/wapi/admin/import/evaluation/report/product6/start`, params),
    importProcess: (params: { type: string; key: string }) => get<FileUploadProcess>(`${u}/wapi/admin/import/evaluation/report/product6/progress`, params),
    importError: (params: { type: string; key: string }) => downloadFile2(`${u}/wapi/admin/import/evaluation/report/product6/error`, params),
    download: (params: { type: string }) => downloadFile2(`${u}/wapi/admin/evaluation/report/product6/export`, params),
};

const ReportService = {
    // 模板list
    list: (params: { productId?: string; page: number; pageSize: number }) => get<ListType<ListItemType>>('/wapi/admin/evaluation/report/paged.json', params),
    // 保存模板
    save: (params: AssessmentResult<PCode.CA>) => post<undefined>('/wapi/admin/evaluation/report/template/save.json', params),
    // 模板信息
    detail: (params: { encryptId: string }) => get<AssessmentResult<PCode.CA> | null>('/wapi/admin/evaluation/report/template/info.json', params),
    // 可选测评产品
    // 1测评模板 2测评题库 3测评维度 4测评计分 5测评报告
    getProductList: (params?: { source: 1 | 2 | 3 | 4 | 5 }) =>
        get<
            {
                id: number;
                name: string;
            }[]
        >('/wapi/admin/project/getProductOptions.json', params),
    // 产品下所有维度
    getDimensionList: (params: { productId: string; needContainsQuestion?: boolean }) =>
        get<
            {
                encryptId: string;
                dimensionName: string;
                description: string;
                dimensionLevel: number;
                encryptParentId: string;
                factorType: 1 | 2 | 3 | null; // 1风险因子 2积极因子 3其他
            }[]
        >('/wapi/admin/evaluation/product/dimensionList.json', params),
    // 产品下所有维度支持排序
    getDimensionNewList: (params: { productId: string; sortField?: string; desc: boolean; flat?: boolean; dimensionLevel?: number }) =>
        get<DimensionNewList[]>('/wapi/admin/evaluation/dimension/query.json', params),
    // 查询可选考生信息字段
    getExamineeList: (params: { productId: string }) =>
        get<
            {
                encryptFieldId: string;
                fieldName: string;
                showName: string;
                required: boolean; // 项目是否可选，默认选中 -- 可以编辑，默认不选中 -- 不可以编辑
                changeInCorp: boolean; // 默认是否选中，默认选中 -- 不可以编辑，默认不选中 -- 可以编辑
            }[]
        >('/wapi/admin/evaluation/report/examinee/infoField/option.json', params),
    previewDetial: (params: { encryptId: string }) => get('/wapi/admin/evaluation/report/template/preview.json', params),
    previewSave: (params: AssessmentResult<PCode.CA>) => post<undefined>('/wapi/admin/evaluation/report/template/preview/save.json', params),
    previewSaveDetial: (params: { encryptId: string }) => get<AssessmentResult<PCode.CA>>('/wapi/admin/evaluation/report/template/add/preview.json', params),

    previewDetialByProductId: (params: { encryptId: string; productId: number }) => get(`/wapi/admin/evaluation/report/template/${params.productId}/preview.json`, params),
    previewSaveDetialByProductId: (params: { encryptId: string; productId: number }) =>
        get<AssessmentResult<PCode.HMA>>(`/wapi/admin/evaluation/report/template/${params.productId}/add/preview.json`, params),

    questionCheck: (params: { ids: string; productId: number; questionStatus: 0 | 1 }) => get<number[]>('/wapi/admin/evaluation/question/notCheck', params),

    questionInfo: (params: { encryptId: string }) => get<QuestionInfo>(`${u}/wapi/admin/evaluation/question/info.json`, params),
    questionList: (params: { productId: number; questionStatus: 0 | 1 }) => get<Question[]>(`${u}/wapi/admin/evaluation/question/optionContainsDetailInfo.json`, params),

    getGroupOption: (params: { productId: number }) => get<undefined>(`${u}/wapi/admin/evaluation/report/question/group/option.json`, params),

    getSubDimension: (params: { productId: number; parentId: string; dimensionStatus?: number }) =>
        get<
            {
                encryptId: string;
                id: number;
                dimensionName: string;
            }[]
        >(`${u}/wapi/admin/evaluation/dimension/subDimension.json`, params),

    getJobList: (params: { productId: number; page: number; pageSize: number }) =>
        get<
            ListType<{
                encryptId: string;
                jobName: string; // 岗位
                status: 0 | 1; // 状态 0 启用 1停用
            }>
        >(`${u}/wapi/admin/evaluation/score/param/job/list.json`, params),

    // 上传文件
    imageUrlExport: (params: { randomKey: string }) => downloadFile2(`${u}/wapi/admin/evaluation/question/image/export`, params),
    getActiveOption: () => get<string[]>(`${u}/wapi/admin/evaluation/report/template/5/option/active.json`),
};

export default ReportService;
