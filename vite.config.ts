// 根目录的 vite.config.js
import { fileURLToPath } from 'node:url'
import path from 'node:path'
import { defineConfig } from 'vite'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 共享的别名配置
export const aliasConfig = [
  // exam-report 模块内部别名
  {
    find: '@',
    replacement: path.resolve(__dirname, 'packages/exam-report/src'),
  },
  {
    find: '@common',
    replacement: path.resolve(__dirname, 'packages/exam-report/src/components-atom'),
  },
  // 解决 exam-report 模块分发后的路径问题
  {
    find: /^@bzl\/zhice-exam-report\/dist\/(.*)/,
    replacement: path.resolve(__dirname, 'packages/exam-report/$1'),
  },
  // monorepo 内其他模块的别名
  { find: '@packages/exam-question', replacement: path.resolve(__dirname, 'packages/exam-question/src') },
  { find: '@packages/exam-report', replacement: path.resolve(__dirname, 'packages/exam-report/src') },
  { find: '@packages/exam-product', replacement: path.resolve(__dirname, 'packages/exam-product/src') },
  { find: '@packages/biz-compositions', replacement: path.resolve(__dirname, 'packages/biz-compositions/src') },
  { find: '@packages/components', replacement: path.resolve(__dirname, 'packages/components/src') },
  { find: '@packages/compositions', replacement: path.resolve(__dirname, 'packages/compositions/src') },
  { find: '@packages/lext-core', replacement: path.resolve(__dirname, 'packages/lext-core/src') },
  { find: '@packages/vite-plugins', replacement: path.resolve(__dirname, 'packages/vite-plugins/src') },
]

export default defineConfig({
  resolve: {
    alias: aliasConfig,
  },
})
