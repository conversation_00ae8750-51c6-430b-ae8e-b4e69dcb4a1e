{"name": "kaoshi-fe-web", "version": "0.0.0", "private": true, "beeGazeId": "2f7904a1b8923a41df14fb73b729cbbc", "scripts": {"build": "exam-cli app build --interactive --no-config", "build:silent": "exam-cli app build --interactive --silent", "build:workspace": "exam-cli build workspace", "build:optimize": "exam-cli optimize full", "dev": "exam-cli app dev --interactive", "init": "exam-cli init", "lint": "exam-cli quality lint --fix", "lint:check": "exam-cli quality lint", "postcheckout": "pnpm i", "precommit": "lint-staged", "preinstall": "npx only-allow pnpm", "prepare": "husky", "prepush": "git fetch --all", "publish": "exam-cli publish release", "publish:changeset": "exam-cli publish changeset", "quality": "exam-cli quality check-all", "test": "exam-cli quality test --watch", "test:ci": "exam-cli quality test --ci", "tsc": "exam-cli quality type-check"}, "lint-staged": {"*": ["eslint --fix --cache", "git add -A"]}, "dependencies": {"@types/js-cookie": "3.0.6", "js-cookie": "3.0.5", "msw": "2.10.2", "node-fetch": "3.3.2", "vue": "3.5.16"}, "devDependencies": {"sourcemap-set-path-plugin": "1.2.3", "@boss/design-resolver": "1.7.125", "@boss/eslint-config": "2.0.1", "@boss/tsconfig": "1.7.125", "@bzl/exam-cli": "workspace:*", "@changesets/cli": "2.29.4", "@commitlint/cli": "19.8.1", "@commitlint/config-conventional": "19.8.1", "@crm/eslint-config": "workspace:*", "@eslint/js": "10.0.0", "@rollup/plugin-inject": "5.0.5", "@types/node": "22.15.31", "@types/qrcode": "1.5.5", "@types/qs": "6.14.0", "@types/webfontloader": "1.6.38", "@vitejs/plugin-vue": "5.2.4", "@vitejs/plugin-vue-jsx": "4.2.0", "@vitest/coverage-istanbul": "3.2.3", "@vitest/ui": "3.2.3", "chalk": "5.4.1", "eslint": "9.28.0", "eslint-plugin-prettier": "5.4.1", "eslint-plugin-vue": "10.2.0", "execa": "9.6.0", "glob": "11.0.2", "globals": "16.2.0", "husky": "9.1.7", "jiti": "2.4.2", "lint-staged": "16.1.0", "only-allow": "1.2.1", "prettier": "3.5.3", "prompts": "2.4.2", "rollup-plugin-visualizer": "6.0.3", "semver": "7.7.2", "stylelint": "16.20.0", "tinyglobby": "0.2.14", "turbo": "2.5.4", "typescript": "5.8.3", "typescript-eslint": "8.34.0", "unplugin-auto-import": "19.3.0", "unplugin-vue-components": "28.7.0", "vite": "6.3.5", "vite-plugin-checker": "0.9.3", "vite-plugin-compression": "0.5.1", "vite-plugin-dts": "4.5.4", "vite-plugin-jsonx": "1.1.2", "vite-plugin-lib-inject-css": "2.2.2", "vite-plugin-svg-icons": "2.0.1", "vitest": "3.2.3", "vue-tsc": "2.2.10"}, "packageManager": "pnpm@10.12.1", "engines": {"node": ">=22.13.0", "pnpm": ">=10.0.0"}, "pnpm": {"peerDependencyRules": {"allowedVersions": {"eslint": "9", "stylelint": "16", "vite": "6", "vue": "3"}}}}